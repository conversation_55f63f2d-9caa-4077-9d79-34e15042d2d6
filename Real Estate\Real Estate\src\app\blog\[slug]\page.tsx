import { Navbar } from '@/components/Navbar'
import { Footer } from '@/components/Footer'
import { notFound } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import blogData from '@/data/blog.json'

// Generate static params for all blog posts
export async function generateStaticParams() {
  return blogData.map((post) => ({
    slug: post.slug,
  }));
}

// Generate metadata for each blog post
export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }) {
  const resolvedParams = await params;
  const post = blogData.find(p => p.slug === resolvedParams.slug && p.published);

  if (!post) {
    return {
      title: 'Blog Post Not Found',
    };
  }

  return {
    title: `${post.title} - Real Estate Blog`,
    description: post.excerpt,
  };
}

async function getBlogPost(slug: string) {
  const post = blogData.find(p => p.slug === slug && p.published);
  return post ? { ...post, createdAt: new Date(post.createdAt) } : null;
}

async function getRelatedPosts(currentPostId: string, category: string | null) {
  const relatedPosts = blogData
    .filter(post => post.id !== currentPostId && post.published && (!category || post.category === category))
    .slice(0, 3)
    .map(post => ({ ...post, createdAt: new Date(post.createdAt) }));
  return relatedPosts;
}

export default async function BlogPostPage({ params }: { params: Promise<{ slug: string }> }) {
  const resolvedParams = await params;
  const post = await getBlogPost(resolvedParams.slug);

  if (!post) {
    notFound()
  }

  const relatedPosts = await getRelatedPosts(post.id, post.category)

  const parseJsonSafely = (jsonString: string): string[] => {
    try {
      return JSON.parse(jsonString)
    } catch {
      return []
    }
  }

  const tags = parseJsonSafely(post.tags)

  return (
    <main className="min-h-screen">
      <Navbar />
      
      <article className="py-16">
        <div className="container-custom max-w-4xl">
          {/* Post Header */}
          <header className="mb-8">
            <div className="flex items-center mb-4">
              {post.category && (
                <span className="bg-primary text-white px-3 py-1 rounded-full text-sm mr-3">
                  {post.category}
                </span>
              )}
              <span className="text-gray-500 text-sm">
                {new Date(post.createdAt).toLocaleDateString('en-IN', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </span>
            </div>
            
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
              {post.title}
            </h1>
            
            <div className="flex items-center text-gray-600">
              <span>By {post.author.name || 'Admin'}</span>
            </div>
          </header>

          {/* Featured Image - Static version */}
          <div className="mb-8">
            <div className="w-full h-64 md:h-96 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
              <span className="text-white text-xl font-semibold">Blog Post Image</span>
            </div>
          </div>

          {/* Post Content */}
          <div className="prose prose-lg max-w-none mb-8">
            <div dangerouslySetInnerHTML={{ __html: post.content }} />
          </div>

          {/* Tags */}
          {tags.length > 0 && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold mb-3">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {tags.map((tag, index) => (
                  <span 
                    key={index}
                    className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Navigation */}
          <div className="border-t border-gray-200 pt-8 mb-8">
            <Link 
              href="/blog"
              className="text-primary hover:text-secondary font-medium"
            >
              ← Back to Blog
            </Link>
          </div>
        </div>
      </article>

      {/* Related Posts */}
      {relatedPosts.length > 0 && (
        <section className="py-16 bg-gray-50">
          <div className="container-custom">
            <h2 className="text-2xl font-bold mb-8">Related Posts</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {relatedPosts.map((relatedPost) => (
                <article key={relatedPost.id} className="bg-white rounded-lg shadow-lg overflow-hidden">
                  <div className="w-full h-48 bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center">
                    <span className="text-white font-semibold">Blog Post</span>
                  </div>
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      {relatedPost.category && (
                        <span className="bg-primary text-white px-2 py-1 rounded text-xs mr-2">
                          {relatedPost.category}
                        </span>
                      )}
                      <span className="text-gray-500 text-xs">
                        {new Date(relatedPost.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    <h3 className="text-lg font-semibold mb-3">
                      <Link href={`/blog/${relatedPost.slug}`} className="hover:text-primary">
                        {relatedPost.title}
                      </Link>
                    </h3>
                    <p className="text-gray-600 text-sm mb-4">
                      {relatedPost.excerpt || relatedPost.title}
                    </p>
                    <Link 
                      href={`/blog/${relatedPost.slug}`}
                      className="text-primary hover:text-secondary text-sm font-medium"
                    >
                      Read More →
                    </Link>
                  </div>
                </article>
              ))}
            </div>
          </div>
        </section>
      )}
      
      <Footer />
    </main>
  )
}
