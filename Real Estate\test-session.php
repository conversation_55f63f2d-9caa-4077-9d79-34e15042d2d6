<?php
session_start();

// Add CORS headers
header('Access-Control-Allow-Origin: http://localhost:3000');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

echo "<h1>Session Test</h1>";
echo "<h2>Current Session Data:</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

if (isset($_SESSION['user_logged_in']) && $_SESSION['user_logged_in'] === true) {
    echo "<p style='color: green;'>✅ User is logged in!</p>";
    echo "<p>User ID: " . $_SESSION['user_id'] . "</p>";
    echo "<p>User Name: " . $_SESSION['user_name'] . "</p>";
    echo "<p>User Role: " . $_SESSION['user_role'] . "</p>";
    
    echo "<h3>JSON Response:</h3>";
    echo "<pre>";
    echo json_encode([
        'user' => [
            'id' => $_SESSION['user_id'],
            'role' => $_SESSION['user_role'],
            'name' => $_SESSION['user_name']
        ]
    ], JSON_PRETTY_PRINT);
    echo "</pre>";
} else {
    echo "<p style='color: red;'>❌ User is not logged in</p>";
    echo "<p><a href='http://localhost:3000/login'>Go to Login</a></p>";
}

echo "<hr>";
echo "<h3>Test Links:</h3>";
echo "<ul>";
echo "<li><a href='http://localhost:3000/login'>Login Page</a></li>";
echo "<li><a href='http://localhost:3000/properties/create'>Create Property (should redirect if not logged in)</a></li>";
echo "<li><a href='http://localhost:3000/dashboard'>Dashboard (should redirect if not logged in)</a></li>";
echo "</ul>";
?>