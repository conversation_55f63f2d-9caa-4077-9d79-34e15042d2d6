<?php
/**
 * Automated Debug Files Cleanup Script
 * 
 * This script will automatically delete all debug and test files
 * created during the development and troubleshooting process.
 * 
 * USAGE: Upload this file to your server root and visit it in browser
 * URL: https://housing.okayy.in/cleanup-debug-files.php
 * 
 * IMPORTANT: This script will delete itself after cleanup!
 */
?>
<!DOCTYPE html>
<html>
<head>
    <title>🧹 Debug Files Cleanup - housing.okayy.in</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 40px auto; 
            padding: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .file-item { 
            padding: 8px 12px; 
            margin: 4px 0; 
            border-radius: 5px; 
            background: #f8f9fa; 
        }
        .summary { 
            background: #e9ecef; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0; 
        }
        .btn { 
            background: #007bff; 
            color: white; 
            padding: 10px 20px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            text-decoration: none; 
            display: inline-block; 
        }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Debug Files Cleanup</h1>
        <p><strong>Website:</strong> housing.okayy.in</p>
        <p><strong>Purpose:</strong> Remove all debug and test files for security</p>
        
        <?php
        // List of debug files to delete
        $debugFiles = [
            'debug-api.php',
            'debug.php',
            'db-test.php',
            'check-admin.php',
            'fix-admin-password.php',
            'reset-admin.php',
            'reset-admin-production.php',
            'test-api-fix.php',
            'test-api-responses.php',
            'debug-client-error.php',
            'check-nextjs-config.php',
            'test-login-api.php',
            'simple-debug.php'
        ];
        
        // Documentation files (optional to delete)
        $docFiles = [
            'DATABASE_SETUP_GUIDE.md',
            'FIXES_SUMMARY.md',
            'ADMIN_DASHBOARD_IMPROVEMENTS.md',
            'CLEANUP_GUIDE.md',
            'test-complete-flow.md'
        ];
        
        $deletedCount = 0;
        $notFoundCount = 0;
        $failedCount = 0;
        
        echo "<h2>🔍 Scanning for Debug Files...</h2>";
        
        // Delete debug files
        echo "<h3>Debug & Test Files:</h3>";
        foreach ($debugFiles as $file) {
            echo "<div class='file-item'>";
            echo "<strong>$file:</strong> ";
            
            if (file_exists($file)) {
                if (unlink($file)) {
                    echo "<span class='success'>✅ Deleted successfully</span>";
                    $deletedCount++;
                } else {
                    echo "<span class='error'>❌ Failed to delete</span>";
                    $failedCount++;
                }
            } else {
                echo "<span class='info'>⚪ Not found (already clean)</span>";
                $notFoundCount++;
            }
            echo "</div>";
        }
        
        // Check documentation files
        echo "<h3>Documentation Files:</h3>";
        foreach ($docFiles as $file) {
            echo "<div class='file-item'>";
            echo "<strong>$file:</strong> ";
            
            if (file_exists($file)) {
                if (unlink($file)) {
                    echo "<span class='success'>✅ Deleted successfully</span>";
                    $deletedCount++;
                } else {
                    echo "<span class='error'>❌ Failed to delete</span>";
                    $failedCount++;
                }
            } else {
                echo "<span class='info'>⚪ Not found</span>";
                $notFoundCount++;
            }
            echo "</div>";
        }
        
        // Summary
        echo "<div class='summary'>";
        echo "<h2>📊 Cleanup Summary</h2>";
        echo "<p><span class='success'>✅ Files deleted: $deletedCount</span></p>";
        echo "<p><span class='info'>⚪ Files not found: $notFoundCount</span></p>";
        echo "<p><span class='error'>❌ Failed deletions: $failedCount</span></p>";
        
        if ($failedCount > 0) {
            echo "<p class='warning'>⚠️ Some files couldn't be deleted. Check file permissions.</p>";
        } else {
            echo "<p class='success'>🎉 All debug files cleaned successfully!</p>";
        }
        echo "</div>";
        
        // Security recommendations
        echo "<h2>🔒 Security Status</h2>";
        echo "<div class='file-item'>";
        if ($deletedCount > 0 || $notFoundCount > 0) {
            echo "<span class='success'>✅ Your website is now secure and clean!</span>";
        } else {
            echo "<span class='warning'>⚠️ No cleanup was needed.</span>";
        }
        echo "</div>";
        
        // Next steps
        echo "<h2>🎯 Next Steps</h2>";
        echo "<ol>";
        echo "<li><strong>Test your website:</strong> <a href='/' target='_blank'>Visit Homepage</a></li>";
        echo "<li><strong>Test admin login:</strong> <a href='/admin/login' target='_blank'>Admin Dashboard</a></li>";
        echo "<li><strong>Verify functionality:</strong> Check property listings and user features</li>";
        echo "<li><strong>Delete this cleanup script</strong> (it will delete itself below)</li>";
        echo "</ol>";
        
        // Self-destruct
        echo "<h2>🗑️ Final Cleanup</h2>";
        echo "<div class='file-item'>";
        echo "<strong>cleanup-debug-files.php (this script):</strong> ";
        
        // Delete this script itself
        if (unlink(__FILE__)) {
            echo "<span class='success'>✅ Self-deleted successfully</span>";
        } else {
            echo "<span class='error'>❌ Failed to self-delete - please delete manually</span>";
        }
        echo "</div>";
        
        echo "<div class='summary'>";
        echo "<h3>🎉 Cleanup Complete!</h3>";
        echo "<p>Your housing.okayy.in website is now:</p>";
        echo "<ul>";
        echo "<li>🔒 <strong>Secure</strong> - No debug files exposing sensitive info</li>";
        echo "<li>🚀 <strong>Fast</strong> - Fewer files to serve</li>";
        echo "<li>💼 <strong>Professional</strong> - Clean file structure</li>";
        echo "<li>🛡️ <strong>Protected</strong> - No development artifacts</li>";
        echo "</ul>";
        echo "<p><strong>Note:</strong> This page will show a 404 error on refresh since the script deleted itself.</p>";
        echo "</div>";
        ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="/" class="btn">🏠 Go to Homepage</a>
            <a href="/admin/login" class="btn">👑 Admin Login</a>
        </div>
    </div>
</body>
</html>
