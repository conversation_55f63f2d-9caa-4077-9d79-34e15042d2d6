import { useState, useEffect } from 'react';

type Testimonial = {
  id: number;
  name: string;
  role: string;
  image: string;
  quote: string;
  rating: number;
};

export function useTestimonials(testimonials: Testimonial[], autoRotateInterval = 5000) {
  const [activeIndex, setActiveIndex] = useState(0);

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
    }, autoRotateInterval);
    
    return () => clearInterval(interval);
  }, [testimonials, autoRotateInterval]);

  const nextTestimonial = () => {
    setActiveIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setActiveIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length);
  };

  const goToTestimonial = (index: number) => {
    setActiveIndex(index);
  };

  return {
    activeIndex,
    activeTestimonial: testimonials[activeIndex],
    nextTestimonial,
    prevTestimonial,
    goToTestimonial,
  };
}