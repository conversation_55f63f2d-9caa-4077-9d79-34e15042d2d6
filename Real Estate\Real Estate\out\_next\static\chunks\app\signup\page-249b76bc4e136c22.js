(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[879],{5695:(e,r,a)=>{"use strict";var s=a(8999);a.o(s,"useRouter")&&a.d(r,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(r,{useSearchParams:function(){return s.useSearchParams}})},6218:(e,r,a)=>{Promise.resolve().then(a.bind(a,9349))},9349:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>m});var s=a(5155),t=a(2115),o=a(5695),n=a(6874),l=a.n(n),d=a(5494),i=a(6821),c=a(1008);function m(){let[e,r]=(0,t.useState)({name:"",email:"",password:"",confirmPassword:"",phone:""}),[a,n]=(0,t.useState)(""),[m,u]=(0,t.useState)(!1),p=(0,o.useRouter)(),x=a=>{r({...e,[a.target.name]:a.target.value})},h=async r=>{if(r.preventDefault(),u(!0),n(""),e.password!==e.confirmPassword){n("Passwords do not match"),u(!1);return}if(e.password.length<6){n("Password must be at least 6 characters long"),u(!1);return}try{let r=await c.R2.signup(e.name,e.email,e.password,e.phone);r.success?p.push("/login?message=Account created successfully"):n(r.error||"An error occurred")}catch(e){console.error("Signup error:",e),n(e.message||"An error occurred. Please try again.")}finally{u(!1)}};return(0,s.jsxs)("main",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(d.Navbar,{}),(0,s.jsx)("div",{className:"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Create your account"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,s.jsx)(l(),{href:"/login",className:"font-medium text-primary-600 hover:text-primary-700",children:"sign in to your existing account"})]})]}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:h,children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,s.jsx)("input",{id:"name",name:"name",type:"text",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",placeholder:"Enter your full name",value:e.name,onChange:x})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",placeholder:"Enter your email address",value:e.email,onChange:x})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Phone Number (Optional)"}),(0,s.jsx)("input",{id:"phone",name:"phone",type:"tel",className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",placeholder:"Enter your phone number",value:e.phone,onChange:x})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",placeholder:"Create a password",value:e.password,onChange:x})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,s.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",autoComplete:"new-password",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",placeholder:"Confirm your password",value:e.confirmPassword,onChange:x})]})]}),a&&(0,s.jsx)("div",{className:"text-red-600 text-sm text-center",children:a}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:m,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",children:m?"Creating account...":"Create account"})})]})]})}),(0,s.jsx)(i.w,{})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[874,494,821,441,684,358],()=>r(6218)),_N_E=e.O()}]);