<?php
session_start();

echo "<h1>Testing Redirect Flow</h1>";
echo "<p>Session data:</p>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<p>Testing redirect to dashboard...</p>";
echo "<a href='redirect-to-dashboard.php'>Click here to test redirect</a>";

// Also test direct redirect
if (isset($_GET['test'])) {
    $_SESSION['user_logged_in'] = true;
    $_SESSION['user_id'] = 'test';
    $_SESSION['user_role'] = 'USER';
    $_SESSION['user_name'] = 'Test User';
    
    header('Location: http://localhost:3000/dashboard');
    exit;
}

echo "<br><br><a href='?test=1'>Test direct redirect to dashboard</a>";
?>