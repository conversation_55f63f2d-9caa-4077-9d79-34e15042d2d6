(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[957],{4163:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>d});var r=a(5155),t=a(2115),n=a(5695);function d(){let[e,s]=(0,t.useState)(!0),[a,d]=(0,t.useState)(null),l=(0,n.useRouter)();return((0,t.useEffect)(()=>{(async()=>{try{let e=await fetch("/php-backend/api/auth/check-session.php",{credentials:"include"}),a=await e.json();if(!a.user||"ADMIN"!==a.user.role)return void l.push("/admin/login");d(a.user),s(!1)}catch(e){console.error("Auth check failed:",e),l.push("/admin/login")}})()},[]),e)?(0,r.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading admin dashboard..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,r.jsx)("header",{className:"bg-white shadow",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Admin Dashboard"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Welcome back, ",null==a?void 0:a.name]})]}),(0,r.jsx)("button",{onClick:()=>l.push("/admin/login"),className:"bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700",children:"Logout"})]})})}),(0,r.jsx)("main",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"px-4 py-6 sm:px-0",children:(0,r.jsx)("div",{className:"border-4 border-dashed border-gray-200 rounded-lg h-96 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Admin Dashboard"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Welcome to the admin panel"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("a",{href:"/admin/properties",className:"block bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Manage Properties"}),(0,r.jsx)("a",{href:"/admin/users",className:"block bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Manage Users"})]})]})})})})]})}},5695:(e,s,a)=>{"use strict";var r=a(8999);a.o(r,"useRouter")&&a.d(s,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(s,{useSearchParams:function(){return r.useSearchParams}})},8505:(e,s,a)=>{Promise.resolve().then(a.bind(a,4163))}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(8505)),_N_E=e.O()}]);