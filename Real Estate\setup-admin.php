<?php
try {
    $db = new PDO('sqlite:Real Estate/prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create admin user
    $adminEmail = '<EMAIL>';
    $adminPassword = 'admin123';
    $adminHashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
    
    // Check if admin already exists
    $stmt = $db->prepare('SELECT id FROM User WHERE email = :email');
    $stmt->execute(['email' => $adminEmail]);
    $existingAdmin = $stmt->fetch();
    
    if ($existingAdmin) {
        // Update existing admin's password
        $stmt = $db->prepare('UPDATE User SET password = :password WHERE email = :email');
        $stmt->execute(['password' => $adminHashedPassword, 'email' => $adminEmail]);
        echo "Updated existing admin: $adminEmail with password: $adminPassword\n";
        $adminId = $existingAdmin['id'];
    } else {
        // Create new admin
        $adminId = 'admin_' . uniqid();
        $stmt = $db->prepare('INSERT INTO User (id, name, email, password, role, isActive, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
        $now = date('Y-m-d H:i:s');
        $stmt->execute([
            $adminId,
            'Admin User',
            $adminEmail,
            $adminHashedPassword,
            'ADMIN',
            1,
            $now,
            $now
        ]);
        echo "Created new admin: $adminEmail with password: $adminPassword\n";
    }
    
    // Create a regular user
    $userEmail = '<EMAIL>';
    $userPassword = 'user123';
    $userHashedPassword = password_hash($userPassword, PASSWORD_DEFAULT);
    
    $stmt = $db->prepare('SELECT id FROM User WHERE email = :email');
    $stmt->execute(['email' => $userEmail]);
    $existingUser = $stmt->fetch();
    
    if ($existingUser) {
        $stmt = $db->prepare('UPDATE User SET password = :password WHERE email = :email');
        $stmt->execute(['password' => $userHashedPassword, 'email' => $userEmail]);
        echo "Updated existing user: $userEmail with password: $userPassword\n";
        $userId = $existingUser['id'];
    } else {
        $userId = 'user_' . uniqid();
        $stmt = $db->prepare('INSERT INTO User (id, name, email, password, role, isActive, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
        $now = date('Y-m-d H:i:s');
        $stmt->execute([
            $userId,
            'John Doe',
            $userEmail,
            $userHashedPassword,
            'USER',
            1,
            $now,
            $now
        ]);
        echo "Created new user: $userEmail with password: $userPassword\n";
    }
    
    // Create sample properties with different approval statuses
    $properties = [
        [
            'title' => 'Luxury 3BHK Apartment in Bandra West',
            'description' => 'Spacious 3BHK apartment with modern amenities, sea view, and prime location in Bandra West.',
            'price' => 25000000,
            'currency' => 'INR',
            'type' => 'APARTMENT',
            'listingType' => 'SALE',
            'bedrooms' => 3,
            'bathrooms' => 2,
            'area' => 1200,
            'address' => '123 Hill Road, Bandra West',
            'city' => 'Mumbai',
            'state' => 'Maharashtra',
            'pincode' => '400050',
            'images' => '["https://images.unsplash.com/photo-1560518883-ce09059eeffa?q=80&w=1973&auto=format&fit=crop"]',
            'amenities' => '["Swimming Pool", "Gym", "Parking", "Security", "Elevator"]',
            'isFeatured' => 1,
            'isApproved' => 1,
            'approvalStatus' => 'APPROVED',
            'ownerId' => $userId,
        ],
        [
            'title' => 'Premium PG for Boys in Gachibowli',
            'description' => 'Fully furnished PG accommodation for working professionals and students. Includes meals, Wi-Fi, and all amenities.',
            'price' => 12000,
            'currency' => 'INR',
            'type' => 'PG',
            'listingType' => 'RENT',
            'pgRoomType' => 'DOUBLE',
            'pgGenderPreference' => 'MALE',
            'bedrooms' => 1,
            'bathrooms' => 1,
            'area' => 150,
            'address' => 'Near HITEC City, Gachibowli',
            'city' => 'Hyderabad',
            'state' => 'Telangana',
            'pincode' => '500032',
            'images' => '["https://images.unsplash.com/photo-1555854877-bab0e564b8d5?q=80&w=1469&auto=format&fit=crop"]',
            'amenities' => '["Wi-Fi", "Food Included", "Laundry", "AC", "Security", "Parking"]',
            'isFeatured' => 1,
            'isApproved' => 1,
            'approvalStatus' => 'APPROVED',
            'ownerId' => $userId,
        ],
        [
            'title' => 'Modern 2BHK Villa in Gurgaon',
            'description' => 'Beautiful 2BHK villa with garden, modern kitchen, and excellent connectivity to Delhi NCR.',
            'price' => 8500000,
            'currency' => 'INR',
            'type' => 'VILLA',
            'listingType' => 'SALE',
            'bedrooms' => 2,
            'bathrooms' => 2,
            'area' => 1500,
            'address' => 'Sector 45, DLF Phase 2',
            'city' => 'Gurgaon',
            'state' => 'Haryana',
            'pincode' => '122002',
            'images' => '["https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?q=80&w=1975&auto=format&fit=crop"]',
            'amenities' => '["Garden", "Parking", "Security", "Power Backup"]',
            'isFeatured' => 1,
            'isApproved' => 1,
            'approvalStatus' => 'APPROVED',
            'ownerId' => $userId,
        ],
        [
            'title' => 'Cozy 1BHK Apartment for Rent in Pune',
            'description' => 'Well-furnished 1BHK apartment in a prime location with all modern amenities.',
            'price' => 18000,
            'currency' => 'INR',
            'type' => 'APARTMENT',
            'listingType' => 'RENT',
            'bedrooms' => 1,
            'bathrooms' => 1,
            'area' => 600,
            'address' => 'Koregaon Park, Pune',
            'city' => 'Pune',
            'state' => 'Maharashtra',
            'pincode' => '411001',
            'images' => '["https://images.unsplash.com/photo-1493809842364-78817add7ffb?q=80&w=1470&auto=format&fit=crop"]',
            'amenities' => '["Wi-Fi", "Parking", "Security", "Gym"]',
            'isFeatured' => 0,
            'isApproved' => 1,
            'approvalStatus' => 'APPROVED',
            'ownerId' => $userId,
        ],
        [
            'title' => 'Ladies PG in Madhapur - Pending Approval',
            'description' => 'Safe and secure PG accommodation for working women. Single and double sharing rooms available.',
            'price' => 15000,
            'currency' => 'INR',
            'type' => 'PG',
            'listingType' => 'RENT',
            'pgRoomType' => 'SINGLE',
            'pgGenderPreference' => 'FEMALE',
            'bedrooms' => 1,
            'bathrooms' => 1,
            'area' => 120,
            'address' => 'Ayyappa Society, Madhapur',
            'city' => 'Hyderabad',
            'state' => 'Telangana',
            'pincode' => '500081',
            'images' => '["https://images.unsplash.com/photo-1586023492125-27b2c045efd7?q=80&w=1458&auto=format&fit=crop"]',
            'amenities' => '["Wi-Fi", "Food Included", "Laundry", "AC", "Security", "Common Area"]',
            'isFeatured' => 0,
            'isApproved' => 0,
            'approvalStatus' => 'PENDING',
            'ownerId' => $userId,
        ]
    ];
    
    foreach ($properties as $property) {
        // Check if property already exists
        $stmt = $db->prepare('SELECT id FROM Property WHERE title = :title');
        $stmt->execute(['title' => $property['title']]);
        $existingProperty = $stmt->fetch();
        
        if (!$existingProperty) {
            $propertyId = 'prop_' . uniqid();
            $now = date('Y-m-d H:i:s');
            
            $stmt = $db->prepare('INSERT INTO Property (id, title, description, price, currency, type, listingType, pgRoomType, pgGenderPreference, bedrooms, bathrooms, area, address, city, state, pincode, images, amenities, isFeatured, isApproved, approvalStatus, ownerId, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)');
            
            $stmt->execute([
                $propertyId,
                $property['title'],
                $property['description'],
                $property['price'],
                $property['currency'],
                $property['type'],
                $property['listingType'],
                $property['pgRoomType'] ?? null,
                $property['pgGenderPreference'] ?? null,
                $property['bedrooms'],
                $property['bathrooms'],
                $property['area'],
                $property['address'],
                $property['city'],
                $property['state'],
                $property['pincode'],
                $property['images'],
                $property['amenities'],
                $property['isFeatured'],
                $property['isApproved'],
                $property['approvalStatus'],
                $property['ownerId'],
                $now,
                $now
            ]);
            
            echo "Created property: " . $property['title'] . " (Status: " . $property['approvalStatus'] . ")\n";
        } else {
            echo "Property already exists: " . $property['title'] . "\n";
        }
    }
    
    echo "\nSetup completed successfully!\n";
    echo "Admin login: <EMAIL> / admin123\n";
    echo "User login: <EMAIL> / user123\n";
    echo "Admin panel: http://localhost:8000/php-admin/login.php\n";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
