import Link from 'next/link';
import Image from 'next/image';
import { prisma } from '@/lib/db';
import { formatIndianAmount, parseJsonSafely, isRecentDate } from '@/lib/utils';

interface Property {
  id: string;
  title: string;
  price: number;
  currency: string;
  type: string;
  bedrooms: number | null;
  bathrooms: number | null;
  area: number | null;
  address: string;
  city: string;
  state: string;
  images: string;
  createdAt: Date;
}

async function getFeaturedProperties(): Promise<Property[]> {
  try {
    const properties = await prisma.property.findMany({
      where: {
        isFeatured: true,
        isApproved: true,
      },
      take: 6,
      orderBy: {
        createdAt: 'desc',
      },
    });
    return properties;
  } catch (error) {
    console.error('Error fetching featured properties:', error);
    return [];
  }
}

export async function FeaturedProperties() {
  const featuredProperties = await getFeaturedProperties();



  if (featuredProperties.length === 0) {
    return (
      <div className="text-center py-16">
        <h3 className="text-xl font-semibold text-gray-600 mb-4">No featured properties available</h3>
        <p className="text-gray-500">Check back soon for amazing property listings!</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {featuredProperties.map((property) => {
        const images = parseJsonSafely<string>(property.images);
        const mainImage = images.length > 0 ? images[0] : '/placeholder-property.jpg';
        const isNew = isRecentDate(property.createdAt, 7);

        return (
          <div key={property.id} className="card group overflow-hidden">
            {/* Property Image */}
            <div className="relative h-64 w-full overflow-hidden">
              <Image
                src={mainImage}
                alt={property.title}
                fill
                className="object-cover transition-transform duration-500 group-hover:scale-110"
              />
              {/* Property Type Badge */}
              <div className="absolute top-4 left-4">
                <span className="px-3 py-1 rounded-md text-sm font-semibold bg-primary-600 text-white">
                  {property.type}
                </span>
              </div>

              {/* New Badge */}
              {isNew && (
                <div className="absolute top-4 right-4">
                  <span className="px-3 py-1 rounded-md text-sm font-semibold bg-green-500 text-white">
                    New
                  </span>
                </div>
              )}

              {/* Save Button */}
              <button className="absolute bottom-4 right-4 w-10 h-10 rounded-full bg-white shadow-md flex items-center justify-center text-gray-700 hover:text-primary transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </button>
            </div>

            {/* Property Details */}
            <div className="p-6">
              <h3 className="text-xl font-bold mb-2 text-text-primary">{property.title}</h3>
              <p className="text-text-secondary mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                {property.address}, {property.city}, {property.state}
              </p>

              <div className="flex justify-between items-center mb-6">
                <span className="text-xl font-bold text-primary-600">{formatIndianAmount(property.price)}</span>
              </div>

              <div className="flex justify-between border-t border-gray-200 pt-4">
                {property.bedrooms && (
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    <span className="text-text-secondary">{property.bedrooms} Beds</span>
                  </div>
                )}
                {property.bathrooms && (
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="text-text-secondary">{property.bathrooms} Baths</span>
                  </div>
                )}
                {property.area && (
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
                    </svg>
                    <span className="text-text-secondary">{property.area} sqft</span>
                  </div>
                )}
              </div>

              <div className="mt-6">
                <Link href={`/properties/${property.id}`} className="btn-primary w-full text-center">
                  View Details
                </Link>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}