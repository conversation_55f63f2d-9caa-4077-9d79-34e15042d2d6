'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'

export default function AdminDashboard() {
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<any>(null)
  const router = useRouter()

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await fetch('/php-backend/api/auth/check-session.php', {
          credentials: 'include'
        })
        const data = await response.json()
        
        if (!data.user || data.user.role !== 'ADMIN') {
          router.push('/admin/login')
          return
        }
        
        setUser(data.user)
        setLoading(false)
      } catch (error) {
        console.error('Auth check failed:', error)
        router.push('/admin/login')
      }
    }
    
    checkAuth()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading admin dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-gray-600">Welcome back, {user?.name}</p>
            </div>
            <button
              onClick={() => router.push('/admin/login')}
              className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
            >
              Logout
            </button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg h-96 flex items-center justify-center">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Admin Dashboard</h2>
              <p className="text-gray-600 mb-4">Welcome to the admin panel</p>
              <div className="space-y-2">
                <a href="/admin/properties" className="block bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-center">
                  Manage Properties
                </a>
                <a href="/admin/users" className="block bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-center">
                  Manage Users
                </a>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
