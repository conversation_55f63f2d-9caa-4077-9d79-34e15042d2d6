<?php
require_once '../../config/database.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendError('Method not allowed', 405);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check authentication
    $user = getCurrentUser($db);
    if (!$user) {
        sendError('Authentication required', 401);
    }
    
    // Get user's properties
    $query = "SELECT p.*,
              (SELECT COUNT(*) FROM inquiries WHERE property_id = p.id) as inquiries_count,
              (SELECT COUNT(*) FROM saved_properties WHERE property_id = p.id) as saved_count
              FROM properties p 
              WHERE p.owner_id = :user_id 
              ORDER BY p.created_at DESC";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user['id']);
    $stmt->execute();
    
    $properties = $stmt->fetchAll();
    
    // Process properties data
    foreach ($properties as &$property) {
        $property['images'] = json_decode($property['images'], true) ?: [];
        $property['amenities'] = json_decode($property['amenities'], true) ?: [];
    }
    
    sendResponse($properties);
    
} catch (Exception $e) {
    error_log("User properties error: " . $e->getMessage());
    sendError('Failed to fetch properties', 500);
}

function getCurrentUser($db) {
    // Get session token
    $session_token = null;
    
    if (isset($_COOKIE['session_token'])) {
        $session_token = $_COOKIE['session_token'];
    } elseif (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $auth_header = $_SERVER['HTTP_AUTHORIZATION'];
        if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
            $session_token = $matches[1];
        }
    }
    
    if (!$session_token) {
        return null;
    }
    
    // Check session
    $query = "SELECT u.* FROM users u 
              JOIN user_sessions s ON u.id = s.user_id 
              WHERE s.session_token = :token AND s.expires_at > NOW()";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':token', $session_token);
    $stmt->execute();
    
    return $stmt->fetch();
}
?>
