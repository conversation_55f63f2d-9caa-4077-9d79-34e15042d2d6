<?php
require_once '../../config/database.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendError('Method not allowed', 405);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $input = json_decode(file_get_contents('php://input'), true);
    $input = validateInput($input, ['name', 'email', 'message']);
    
    $name = sanitizeInput($input['name']);
    $email = sanitizeInput($input['email']);
    $phone = isset($input['phone']) ? sanitizeInput($input['phone']) : null;
    $message = sanitizeInput($input['message']);
    $type = isset($input['type']) ? sanitizeInput($input['type']) : 'GENERAL';
    
    // Validate email
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        sendError('Invalid email format', 422);
    }
    
    // Insert contact message
    $message_id = uniqid('msg_', true);
    $query = "INSERT INTO contact_messages (id, name, email, phone, message, type) VALUES (:id, :name, :email, :phone, :message, :type)";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $message_id);
    $stmt->bindParam(':name', $name);
    $stmt->bindParam(':email', $email);
    $stmt->bindParam(':phone', $phone);
    $stmt->bindParam(':message', $message);
    $stmt->bindParam(':type', $type);
    
    if ($stmt->execute()) {
        sendResponse([
            'success' => true,
            'message' => 'Message sent successfully'
        ]);
    } else {
        sendError('Failed to send message', 500);
    }
    
} catch (Exception $e) {
    error_log("Contact error: " . $e->getMessage());
    sendError('Failed to send message', 500);
}
?>
