import Link from 'next/link';
import Image from 'next/image';

export function CtaSection() {
  return (
    <section className="relative py-20 overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image 
          src="https://images.unsplash.com/photo-1560518883-ce09059eeffa?q=80&w=1973&auto=format&fit=crop"
          alt="Modern luxury home"
          fill
          className="object-cover brightness-50"
        />
      </div>
      
      <div className="container-custom relative z-10">
        <div className="max-w-3xl mx-auto text-center text-white">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Find Your Dream Home?
          </h2>
          <p className="text-lg md:text-xl mb-8">
            Join thousands of satisfied customers who found their perfect property with us. 
            Our expert agents are ready to help you navigate the real estate market.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/properties" className="btn-primary py-3 px-8 text-center">
              Browse Properties
            </Link>
            <Link href="/contact" className="btn-secondary py-3 px-8 text-center bg-white text-primary">
              Contact an Agent
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}