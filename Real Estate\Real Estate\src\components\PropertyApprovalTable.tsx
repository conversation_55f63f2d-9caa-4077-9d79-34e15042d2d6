'use client';

import { useState } from 'react';
import { Property, User } from '@prisma/client';
import { useRouter } from 'next/navigation';
import { adminAPI } from '@/config/api';

export interface PropertyWithUser extends Property {
  owner: User;
}

export interface PropertyApprovalTableProps {
  properties: PropertyWithUser[];
}

export default function PropertyApprovalTable({ properties }: PropertyApprovalTableProps) {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleApprove = async (propertyId: string) => {
    setLoading(true);
    try {
      const response = await adminAPI.approveProperty(propertyId);
      if (response.success) {
        router.refresh();
      } else {
        alert(`Failed to approve property: ${response.error}`);
      }
    } catch (error: any) {
      console.error('Error approving property:', error);
      alert(error.message || 'An error occurred while approving the property.');
    } finally {
      setLoading(false);
    }
  };

  const handleReject = async (propertyId: string) => {
    const reason = prompt('Please provide a reason for rejection:');
    if (!reason) return;

    setLoading(true);
    try {
      const response = await adminAPI.rejectProperty(propertyId, reason);
      if (response.success) {
        router.refresh();
      } else {
        alert(`Failed to reject property: ${response.error}`);
      }
    } catch (error: any) {
      console.error('Error rejecting property:', error);
      alert(error.message || 'An error occurred while rejecting the property.');
    } finally {
      setLoading(false);
    }
  };

  if (properties.length === 0) {
    return <p>No pending properties for approval.</p>;
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white border border-gray-200">
        <thead>
          <tr>
            <th className="py-2 px-4 border-b">Title</th>
            <th className="py-2 px-4 border-b">Price</th>
            <th className="py-2 px-4 border-b">Type</th>
            <th className="py-2 px-4 border-b">Owner</th>
            <th className="py-2 px-4 border-b">Actions</th>
          </tr>
        </thead>
        <tbody>
          {properties.map((property) => (
            <tr key={property.id}>
              <td className="py-2 px-4 border-b">{property.title}</td>
              <td className="py-2 px-4 border-b">{property.price}</td>
              <td className="py-2 px-4 border-b">{property.type}</td>
              <td className="py-2 px-4 border-b">{property.owner.email}</td>
              <td className="py-2 px-4 border-b">
                <button
                  onClick={() => handleApprove(property.id)}
                  disabled={loading}
                  className="bg-green-500 text-white px-3 py-1 rounded mr-2 disabled:opacity-50"
                >
                  {loading ? 'Approving...' : 'Approve'}
                </button>
                <button
                  onClick={() => handleReject(property.id)}
                  disabled={loading}
                  className="bg-red-500 text-white px-3 py-1 rounded mr-2 disabled:opacity-50"
                >
                  {loading ? 'Rejecting...' : 'Reject'}
                </button>
                <a
                  href={`/properties/${property.id}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-blue-500 text-white px-3 py-1 rounded disabled:opacity-50"
                >
                  Preview
                </a>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}