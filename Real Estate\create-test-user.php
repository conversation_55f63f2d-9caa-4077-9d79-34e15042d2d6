<?php
try {
    $db = new PDO('sqlite:Real Estate/prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create a test user with known credentials
    $email = '<EMAIL>';
    $password = 'password123';
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    // Check if user already exists
    $stmt = $db->prepare('SELECT id FROM User WHERE email = :email');
    $stmt->execute(['email' => $email]);
    $existingUser = $stmt->fetch();
    
    if ($existingUser) {
        // Update existing user's password
        $stmt = $db->prepare('UPDATE User SET password = :password WHERE email = :email');
        $stmt->execute(['password' => $hashedPassword, 'email' => $email]);
        echo "Updated existing user: $email with password: $password\n";
    } else {
        // Create new user
        $stmt = $db->prepare('INSERT INTO User (id, name, email, password, role, isActive, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
        $userId = 'test_user_' . uniqid();
        $now = date('Y-m-d H:i:s');
        $stmt->execute([
            $userId,
            'Test User',
            $email,
            $hashedPassword,
            'USER',
            1,
            $now,
            $now
        ]);
        echo "Created new user: $email with password: $password\n";
    }
    
    // Also create an admin user
    $adminEmail = '<EMAIL>';
    $adminPassword = 'admin123';
    $adminHashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
    
    $stmt = $db->prepare('SELECT id FROM User WHERE email = :email');
    $stmt->execute(['email' => $adminEmail]);
    $existingAdmin = $stmt->fetch();
    
    if ($existingAdmin) {
        $stmt = $db->prepare('UPDATE User SET password = :password WHERE email = :email');
        $stmt->execute(['password' => $adminHashedPassword, 'email' => $adminEmail]);
        echo "Updated existing admin: $adminEmail with password: $adminPassword\n";
    } else {
        $stmt = $db->prepare('INSERT INTO User (id, name, email, password, role, isActive, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
        $adminId = 'admin_user_' . uniqid();
        $now = date('Y-m-d H:i:s');
        $stmt->execute([
            $adminId,
            'Admin User',
            $adminEmail,
            $adminHashedPassword,
            'ADMIN',
            1,
            $now,
            $now
        ]);
        echo "Created new admin: $adminEmail with password: $adminPassword\n";
    }
    
    echo "Test users created successfully!\n";
    echo "User login: <EMAIL> / password123\n";
    echo "Admin login: <EMAIL> / admin123\n";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
