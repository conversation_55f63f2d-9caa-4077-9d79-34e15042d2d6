1:"$Sreact.fragment"
2:I[7555,[],""]
3:I[1295,[],""]
4:I[5494,["874","static/chunks/874-8e9a565f7eb17c9e.js","63","static/chunks/63-daa300108b4615e5.js","494","static/chunks/494-d450966e233bb5a9.js","527","static/chunks/527-cc44b4833e8edc34.js","974","static/chunks/app/page-89c07e33a04e242f.js"],"Navbar"]
5:I[3063,["874","static/chunks/874-8e9a565f7eb17c9e.js","63","static/chunks/63-daa300108b4615e5.js","494","static/chunks/494-d450966e233bb5a9.js","527","static/chunks/527-cc44b4833e8edc34.js","974","static/chunks/app/page-89c07e33a04e242f.js"],"Image"]
6:I[4527,["874","static/chunks/874-8e9a565f7eb17c9e.js","63","static/chunks/63-daa300108b4615e5.js","494","static/chunks/494-d450966e233bb5a9.js","527","static/chunks/527-cc44b4833e8edc34.js","974","static/chunks/app/page-89c07e33a04e242f.js"],"SearchBar"]
7:I[6874,["874","static/chunks/874-8e9a565f7eb17c9e.js","63","static/chunks/63-daa300108b4615e5.js","494","static/chunks/494-d450966e233bb5a9.js","527","static/chunks/527-cc44b4833e8edc34.js","974","static/chunks/app/page-89c07e33a04e242f.js"],""]
9:I[2975,["874","static/chunks/874-8e9a565f7eb17c9e.js","63","static/chunks/63-daa300108b4615e5.js","494","static/chunks/494-d450966e233bb5a9.js","527","static/chunks/527-cc44b4833e8edc34.js","974","static/chunks/app/page-89c07e33a04e242f.js"],"TestimonialsSection"]
b:I[9665,[],"OutletBoundary"]
e:I[4911,[],"AsyncMetadataOutlet"]
10:I[9665,[],"ViewportBoundary"]
12:I[9665,[],"MetadataBoundary"]
14:I[6614,[],""]
:HL["/_next/static/css/6650b15f75106e01.css","style"]
a:T63b,M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z0:{"P":null,"b":"NPlM-KmMDsq2FJAox8jxu","p":"","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/6650b15f75106e01.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","className":"scroll-smooth","children":["$","body",null,{"className":"__variable_e8ce0c font-sans bg-background text-text-primary antialiased","children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[[["$","script",null,{"type":"application/ld+json","dangerouslySetInnerHTML":{"__html":"{\"@context\":\"https://schema.org\",\"@type\":\"RealEstateAgent\",\"name\":\"Real Estate India\",\"description\":\"Leading real estate platform in India for buying, selling, and renting properties\",\"url\":\"https://realestate-india.com\",\"logo\":\"https://realestate-india.com/logo.png\",\"address\":{\"@type\":\"PostalAddress\",\"addressCountry\":\"IN\"},\"areaServed\":{\"@type\":\"Country\",\"name\":\"India\"},\"serviceType\":[\"Property Sales\",\"Property Rentals\",\"Property Management\",\"Real Estate Consultation\"],\"priceRange\":\"₹₹₹\"}"}}],["$","main",null,{"className":"min-h-screen","children":[["$","$L4",null,{}],["$","section",null,{"className":"relative min-h-[700px] flex items-center justify-center overflow-hidden","children":[["$","div",null,{"className":"absolute inset-0 z-0","children":[["$","$L5",null,{"src":"https://images.unsplash.com/photo-1560518883-ce09059eeffa?q=80&w=1973&auto=format&fit=crop","alt":"Modern luxury home exterior","fill":true,"priority":true,"className":"object-cover"}],["$","div",null,{"className":"absolute inset-0 bg-gradient-to-br from-primary-900/80 via-primary-800/70 to-accent-900/80"}],["$","div",null,{"className":"absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"}]]}],["$","div",null,{"className":"absolute inset-0 overflow-hidden pointer-events-none","children":[["$","div",null,{"className":"absolute top-20 left-10 w-72 h-72 bg-primary-400/10 rounded-full blur-3xl animate-bounce-subtle"}],["$","div",null,{"className":"absolute bottom-20 right-10 w-96 h-96 bg-accent-400/10 rounded-full blur-3xl animate-bounce-subtle","style":{"animationDelay":"1s"}}]]}],["$","div",null,{"className":"container-custom relative z-10 text-center text-white","children":[["$","div",null,{"className":"animate-fade-in","children":[["$","h1",null,{"className":"heading-1 mb-6 text-white","children":["Find Your ",["$","span",null,{"className":"text-gradient bg-gradient-to-r from-white to-accent-200 bg-clip-text text-transparent","children":"Dream Home"}]]}],["$","p",null,{"className":"body-large mb-12 max-w-4xl mx-auto text-white/90","children":"Discover the perfect property for buying, selling, or renting with our comprehensive real estate platform. Your journey to the ideal home starts here."}]]}],["$","div",null,{"className":"animate-slide-up mb-12","style":{"animationDelay":"0.2s"},"children":["$","$L6",null,{}]}],["$","div",null,{"className":"flex flex-col sm:flex-row gap-6 items-center justify-center animate-slide-up","style":{"animationDelay":"0.4s"},"children":[["$","$L7",null,{"href":"/properties","className":"btn-secondary px-10 py-4 text-lg bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20 hover:border-white/30","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","className":"h-5 w-5 mr-2","fill":"none","viewBox":"0 0 24 24","stroke":"currentColor","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"}]}],"Browse Properties"]}],["$","$L7",null,{"href":"/properties/create","className":"btn-success px-10 py-4 text-lg shadow-colored-lg","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","className":"h-5 w-5 mr-2","fill":"none","viewBox":"0 0 24 24","stroke":"currentColor","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M12 6v6m0 0v6m0-6h6m-6 0H6"}]}],"List Your Property FREE"]}]]}],["$","div",null,{"className":"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 animate-slide-up","style":{"animationDelay":"0.6s"},"children":[["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"text-3xl md:text-4xl font-bold text-white mb-2","children":"10,000+"}],["$","div",null,{"className":"text-white/80","children":"Properties Listed"}]]}],["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"text-3xl md:text-4xl font-bold text-white mb-2","children":"5,000+"}],["$","div",null,{"className":"text-white/80","children":"Happy Customers"}]]}],["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"text-3xl md:text-4xl font-bold text-white mb-2","children":"50+"}],["$","div",null,{"className":"text-white/80","children":"Cities Covered"}]]}]]}]]}]]}],["$","section",null,{"className":"py-20 bg-background-secondary","children":["$","div",null,{"className":"container-custom","children":[["$","div",null,{"className":"text-center mb-16","children":[["$","h2",null,{"className":"heading-2 mb-6","children":"Browse by Category"}],["$","p",null,{"className":"body-medium max-w-2xl mx-auto","children":"Explore our diverse range of property types to find exactly what you're looking for"}]]}],["$","div",null,{"className":"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6","children":[["$","$L7","1",{"href":"/properties?type=house","className":"group overflow-hidden rounded-lg shadow-md transition-transform duration-300 hover:-translate-y-2","children":["$","div",null,{"className":"relative h-60 w-full overflow-hidden","children":[["$","$L5",null,{"src":"https://images.unsplash.com/photo-1564013799919-ab600027ffc6?q=80&w=1470&auto=format&fit=crop","alt":"Houses","fill":true,"className":"object-cover transition-transform duration-500 group-hover:scale-110"}],["$","div",null,{"className":"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent","children":["$","div",null,{"className":"absolute bottom-0 left-0 p-6 text-white","children":[["$","h3",null,{"className":"text-xl font-bold mb-1","children":"Houses"}],["$","p",null,{"className":"text-sm text-gray-200","children":"Find your dream house"}]]}]}]]}]}],["$","$L7","2",{"href":"/properties?type=apartment","className":"group overflow-hidden rounded-lg shadow-md transition-transform duration-300 hover:-translate-y-2","children":["$","div",null,{"className":"relative h-60 w-full overflow-hidden","children":[["$","$L5",null,{"src":"https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?q=80&w=1035&auto=format&fit=crop","alt":"Apartments","fill":true,"className":"object-cover transition-transform duration-500 group-hover:scale-110"}],["$","div",null,{"className":"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent","children":["$","div",null,{"className":"absolute bottom-0 left-0 p-6 text-white","children":[["$","h3",null,{"className":"text-xl font-bold mb-1","children":"Apartments"}],["$","p",null,{"className":"text-sm text-gray-200","children":"Modern apartment living"}]]}]}]]}]}],["$","$L7","3",{"href":"/properties?type=condo","className":"group overflow-hidden rounded-lg shadow-md transition-transform duration-300 hover:-translate-y-2","children":["$","div",null,{"className":"relative h-60 w-full overflow-hidden","children":[["$","$L5",null,{"src":"https://images.unsplash.com/photo-1512917774080-9991f1c4c750?q=80&w=1470&auto=format&fit=crop","alt":"Condos","fill":true,"className":"object-cover transition-transform duration-500 group-hover:scale-110"}],["$","div",null,{"className":"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent","children":["$","div",null,{"className":"absolute bottom-0 left-0 p-6 text-white","children":[["$","h3",null,{"className":"text-xl font-bold mb-1","children":"Condos"}],["$","p",null,{"className":"text-sm text-gray-200","children":"Luxury condominium units"}]]}]}]]}]}],["$","$L7","4",{"href":"/properties?type=land","className":"group overflow-hidden rounded-lg shadow-md transition-transform duration-300 hover:-translate-y-2","children":["$","div",null,{"className":"relative h-60 w-full overflow-hidden","children":[["$","$L5",null,{"src":"https://images.unsplash.com/photo-1500382017468-9049fed747ef?q=80&w=1032&auto=format&fit=crop","alt":"Land","fill":true,"className":"object-cover transition-transform duration-500 group-hover:scale-110"}],["$","div",null,{"className":"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent","children":["$","div",null,{"className":"absolute bottom-0 left-0 p-6 text-white","children":[["$","h3",null,{"className":"text-xl font-bold mb-1","children":"Land"}],["$","p",null,{"className":"text-sm text-gray-200","children":"Build your future home"}]]}]}]]}]}]]}]]}]}],["$","section",null,{"className":"py-20 bg-white","children":["$","div",null,{"className":"container-custom","children":[["$","div",null,{"className":"flex flex-col md:flex-row justify-between items-start md:items-center mb-16 space-y-4 md:space-y-0","children":[["$","div",null,{"children":[["$","h2",null,{"className":"heading-2 mb-4","children":"Featured Properties"}],["$","p",null,{"className":"body-medium","children":"Handpicked premium properties just for you"}]]}],["$","$L7",null,{"href":"/properties","className":"btn-secondary","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","className":"h-5 w-5 mr-2","fill":"none","viewBox":"0 0 24 24","stroke":"currentColor","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M13 7l5 5m0 0l-5 5m5-5H6"}]}],"View All Properties"]}]]}],"$L8"]}]}],["$","section",null,{"className":"py-20 bg-gradient-to-br from-success-500 via-success-600 to-success-700 text-white relative overflow-hidden","children":[["$","div",null,{"className":"absolute inset-0 opacity-10","children":["$","div",null,{"className":"absolute top-0 left-0 w-full h-full","style":{"backgroundImage":"url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")"}}]}],["$","div",null,{"className":"container-custom text-center relative z-10","children":["$","div",null,{"className":"max-w-4xl mx-auto","children":[["$","h2",null,{"className":"heading-2 mb-6 text-white","children":"List Your Property for FREE!"}],["$","p",null,{"className":"body-large mb-12 text-white/90 max-w-3xl mx-auto","children":"Join thousands of property owners who trust us to showcase their properties. Get maximum exposure with zero listing fees and professional support."}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12","children":[["$","div",null,{"className":"bg-green-100 rounded-lg p-8 text-center","children":[["$","div",null,{"className":"w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","className":"h-8 w-8 text-green-800","fill":"none","viewBox":"0 0 24 24","stroke":"currentColor","children":[["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"}],["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M15 13a3 3 0 11-6 0 3 3 0 016 0z"}]]}]}],["$","h3",null,{"className":"text-xl font-bold mb-3 text-green-900","children":"Professional Photos"}],["$","p",null,{"className":"text-green-700","children":"Upload multiple high-quality images to showcase your property beautifully"}]]}],["$","div",null,{"className":"bg-green-100 rounded-lg p-8 text-center","children":[["$","div",null,{"className":"w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","className":"h-8 w-8 text-green-800","fill":"none","viewBox":"0 0 24 24","stroke":"currentColor","children":[["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z"}],["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z"}]]}]}],["$","h3",null,{"className":"text-xl font-bold mb-3 text-green-900","children":"Targeted Reach"}],["$","p",null,{"className":"text-green-700","children":"Reach genuine buyers and tenants in your area with smart targeting"}]]}],["$","div",null,{"className":"bg-green-100 rounded-lg p-8 text-center","children":[["$","div",null,{"className":"w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","className":"h-8 w-8 text-green-800","fill":"none","viewBox":"0 0 24 24","stroke":"currentColor","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M13 10V3L4 14h7v7l9-11h-7z"}]}]}],["$","h3",null,{"className":"text-xl font-bold mb-3 text-green-900","children":"Quick Approval"}],["$","p",null,{"className":"text-green-700","children":"Get your property approved and live within 24 hours with our fast process"}]]}]]}],["$","$L7",null,{"href":"/properties/create","className":"inline-flex items-center px-10 py-4 bg-white text-success-600 font-bold rounded-2xl text-lg hover:bg-gray-50 transition-all duration-300 shadow-large transform hover:-translate-y-1","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","className":"h-6 w-6 mr-3","fill":"none","viewBox":"0 0 24 24","stroke":"currentColor","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M12 6v6m0 0v6m0-6h6m-6 0H6"}]}],"Start Listing Now - It's FREE!"]}]]}]}]]}],["$","section",null,{"className":"py-20 bg-background-secondary","children":["$","div",null,{"className":"container-custom","children":[["$","div",null,{"className":"text-center mb-16","children":[["$","h2",null,{"className":"heading-2 mb-6","children":"Our Services"}],["$","p",null,{"className":"body-medium max-w-2xl mx-auto","children":"Comprehensive real estate solutions tailored to your needs"}]]}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-3 gap-8","children":[["$","div",null,{"className":"card-elevated p-8 text-center group hover:shadow-colored transition-all duration-500","children":[["$","div",null,{"className":"w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","className":"h-10 w-10 text-white","fill":"none","viewBox":"0 0 24 24","stroke":"currentColor","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"}]}]}],["$","h3",null,{"className":"text-2xl font-bold mb-4 text-text-primary","children":"Buy a Home"}],["$","p",null,{"className":"text-text-secondary leading-relaxed mb-6","children":"Find your perfect home with our immersive photo experience and comprehensive listings, including exclusive properties you won't find anywhere else."}],["$","$L7",null,{"href":"/buy","className":"btn-ghost text-primary-600 hover:bg-primary-50","children":["Explore Buying Options",["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","className":"h-4 w-4 ml-2","fill":"none","viewBox":"0 0 24 24","stroke":"currentColor","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M13 7l5 5m0 0l-5 5m5-5H6"}]}]]}]]}],["$","div",null,{"className":"card-elevated p-8 text-center group hover:shadow-colored transition-all duration-500","children":[["$","div",null,{"className":"w-20 h-20 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","className":"h-10 w-10 text-white","fill":"none","viewBox":"0 0 24 24","stroke":"currentColor","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"}]}]}],["$","h3",null,{"className":"text-2xl font-bold mb-4 text-text-primary","children":"Rent a Home"}],["$","p",null,{"className":"text-text-secondary leading-relaxed mb-6","children":"Experience seamless rental solutions from browsing our extensive network to application processing and rent management."}],["$","$L7",null,{"href":"/rent","className":"btn-ghost text-accent-600 hover:bg-accent-50","children":["Find Rentals",["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","className":"h-4 w-4 ml-2","fill":"none","viewBox":"0 0 24 24","stroke":"currentColor","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M13 7l5 5m0 0l-5 5m5-5H6"}]}]]}]]}],["$","div",null,{"className":"card-elevated p-8 text-center group hover:shadow-colored transition-all duration-500","children":[["$","div",null,{"className":"w-20 h-20 bg-gradient-to-br from-success-500 to-success-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","className":"h-10 w-10 text-white","fill":"none","viewBox":"0 0 24 24","stroke":"currentColor","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"}]}]}],["$","h3",null,{"className":"text-2xl font-bold mb-4 text-text-primary","children":"Sell a Home"}],["$","p",null,{"className":"text-text-secondary leading-relaxed mb-6","children":"Navigate your property sale successfully with our expert guidance, marketing support, and comprehensive selling solutions."}],["$","$L7",null,{"href":"/sell","className":"btn-ghost text-success-600 hover:bg-success-50","children":["Start Selling",["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","className":"h-4 w-4 ml-2","fill":"none","viewBox":"0 0 24 24","stroke":"currentColor","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M13 7l5 5m0 0l-5 5m5-5H6"}]}]]}]]}]]}]]}]}],["$","section",null,{"className":"py-20 bg-white","children":["$","div",null,{"className":"container-custom","children":[["$","div",null,{"className":"text-center mb-16","children":[["$","h2",null,{"className":"heading-2 mb-6","children":"What Our Clients Say"}],["$","p",null,{"className":"body-medium max-w-2xl mx-auto","children":"Real stories from real people who found their dream homes with us"}]]}],["$","$L9",null,{}]]}]}],["$","section",null,{"className":"relative py-20 overflow-hidden","children":[["$","div",null,{"className":"absolute inset-0 z-0","children":["$","$L5",null,{"src":"https://images.unsplash.com/photo-1560518883-ce09059eeffa?q=80&w=1973&auto=format&fit=crop","alt":"Modern luxury home","fill":true,"className":"object-cover brightness-50"}]}],["$","div",null,{"className":"container-custom relative z-10","children":["$","div",null,{"className":"max-w-3xl mx-auto text-center text-white","children":[["$","h2",null,{"className":"text-3xl md:text-4xl font-bold mb-6","children":"Ready to Find Your Dream Home?"}],["$","p",null,{"className":"text-lg md:text-xl mb-8","children":"Join thousands of satisfied customers who found their perfect property with us. Our expert agents are ready to help you navigate the real estate market."}],["$","div",null,{"className":"flex flex-col sm:flex-row justify-center gap-4","children":[["$","$L7",null,{"href":"/properties","className":"btn-primary py-3 px-8 text-center","children":"Browse Properties"}],["$","$L7",null,{"href":"/contact","className":"btn-secondary py-3 px-8 text-center bg-white text-primary","children":"Contact an Agent"}]]}]]}]}]]}],["$","footer",null,{"className":"bg-gradient-to-br from-secondary-900 via-secondary-800 to-secondary-900 text-white pt-20 pb-8 relative overflow-hidden","children":[["$","div",null,{"className":"absolute inset-0 opacity-5","children":["$","div",null,{"className":"absolute top-0 left-0 w-full h-full","style":{"backgroundImage":"url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")"}}]}],["$","div",null,{"className":"container-custom relative z-10","children":[["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-16","children":[["$","div",null,{"className":"lg:col-span-2","children":[["$","div",null,{"className":"flex items-center space-x-3 mb-6","children":[["$","div",null,{"className":"w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center","children":["$","svg",null,{"viewBox":"0 0 24 24","fill":"none","xmlns":"http://www.w3.org/2000/svg","className":"w-6 h-6 text-white","children":[["$","path",null,{"d":"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z","stroke":"currentColor","strokeWidth":"2","strokeLinecap":"round","strokeLinejoin":"round"}],["$","path",null,{"d":"M9 22V12H15V22","stroke":"currentColor","strokeWidth":"2","strokeLinecap":"round","strokeLinejoin":"round"}]]}]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-2xl font-bold text-gradient bg-gradient-to-r from-white to-primary-200 bg-clip-text text-transparent","children":"RealEstate"}],["$","p",null,{"className":"text-sm text-gray-300","children":"India"}]]}]]}],["$","p",null,{"className":"text-gray-300 mb-6 leading-relaxed max-w-md","children":"Your trusted partner in finding the perfect property across India. Whether you're buying, selling, or renting, we provide expert guidance and comprehensive solutions for all your real estate needs."}],["$","div",null,{"className":"flex space-x-4","children":[["$","a",null,{"href":"#","className":"w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-300 transform hover:scale-110","children":["$","svg",null,{"className":"w-5 h-5","fill":"currentColor","viewBox":"0 0 24 24","aria-hidden":"true","children":["$","path",null,{"fillRule":"evenodd","d":"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z","clipRule":"evenodd"}]}]}],["$","a",null,{"href":"#","className":"w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-300 transform hover:scale-110","children":["$","svg",null,{"className":"w-5 h-5","fill":"currentColor","viewBox":"0 0 24 24","aria-hidden":"true","children":["$","path",null,{"d":"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"}]}]}],["$","a",null,{"href":"#","className":"w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-300 transform hover:scale-110","children":["$","svg",null,{"className":"w-5 h-5","fill":"currentColor","viewBox":"0 0 24 24","aria-hidden":"true","children":["$","path",null,{"fillRule":"evenodd","d":"$a","clipRule":"evenodd"}]}]}],["$","a",null,{"href":"#","className":"w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-300 transform hover:scale-110","children":["$","svg",null,{"className":"w-5 h-5","fill":"currentColor","viewBox":"0 0 24 24","aria-hidden":"true","children":["$","path",null,{"fillRule":"evenodd","d":"M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z","clipRule":"evenodd"}]}]}]]}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-lg font-semibold mb-4","children":"Quick Links"}],["$","ul",null,{"className":"space-y-3","children":[["$","li",null,{"children":["$","$L7",null,{"href":"/properties","className":"text-gray-400 hover:text-white transition-colors duration-300","children":"Properties"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/buy","className":"text-gray-400 hover:text-white transition-colors duration-300","children":"Buy"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/rent","className":"text-gray-400 hover:text-white transition-colors duration-300","children":"Rent"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/sell","className":"text-gray-400 hover:text-white transition-colors duration-300","children":"Sell"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/about","className":"text-gray-400 hover:text-white transition-colors duration-300","children":"About Us"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/contact","className":"text-gray-400 hover:text-white transition-colors duration-300","children":"Contact Us"}]}]]}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-lg font-semibold mb-4","children":"Services"}],["$","ul",null,{"className":"space-y-3","children":[["$","li",null,{"children":["$","$L7",null,{"href":"/services/property-management","className":"text-gray-400 hover:text-white transition-colors duration-300","children":"Property Management"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/services/property-valuation","className":"text-gray-400 hover:text-white transition-colors duration-300","children":"Property Valuation"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/services/mortgage-services","className":"text-gray-400 hover:text-white transition-colors duration-300","children":"Mortgage Services"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/services/legal-services","className":"text-gray-400 hover:text-white transition-colors duration-300","children":"Legal Services"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/services/investment-advisory","className":"text-gray-400 hover:text-white transition-colors duration-300","children":"Investment Advisory"}]}]]}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-lg font-semibold mb-4","children":"Contact Us"}],["$","ul",null,{"className":"space-y-3","children":[["$","li",null,{"className":"flex items-start space-x-3","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","className":"h-6 w-6 text-gray-400 flex-shrink-0","fill":"none","viewBox":"0 0 24 24","stroke":"currentColor","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"}]}],["$","span",null,{"className":"text-gray-400","children":"<EMAIL>"}]]}],["$","li",null,{"className":"flex items-start space-x-3","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","className":"h-6 w-6 text-gray-400 flex-shrink-0","fill":"none","viewBox":"0 0 24 24","stroke":"currentColor","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"}]}],["$","span",null,{"className":"text-gray-400","children":["Mon-Fri: 9AM - 6PM",["$","br",null,{}],"Sat: 10AM - 4PM",["$","br",null,{}],"Sun: Closed"]}]]}]]}]]}]]}],["$","div",null,{"className":"pt-8 border-t border-gray-800 text-center md:flex md:justify-between md:text-left","children":[["$","p",null,{"className":"text-gray-400 mb-4 md:mb-0","children":["© ",2025," RealEstate. All rights reserved."]}],["$","div",null,{"className":"space-x-6","children":[["$","$L7",null,{"href":"/privacy-policy","className":"text-gray-400 hover:text-white transition-colors duration-300","children":"Privacy Policy"}],["$","$L7",null,{"href":"/terms-of-service","className":"text-gray-400 hover:text-white transition-colors duration-300","children":"Terms of Service"}],["$","$L7",null,{"href":"/sitemap","className":"text-gray-400 hover:text-white transition-colors duration-300","children":"Sitemap"}]]}]]}]]}]]}]]}]],null,["$","$Lb",null,{"children":["$Lc","$Ld",["$","$Le",null,{"promise":"$@f"}]]}]]}],{},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","UO8Arc2SJ0OXJkO8ziHOhv",{"children":[["$","$L10",null,{"children":"$L11"}],null]}],["$","$L12",null,{"children":"$L13"}]]}],false]],"m":"$undefined","G":["$14","$undefined"],"s":false,"S":true}
15:"$Sreact.suspense"
16:I[4911,[],"AsyncMetadata"]
13:["$","div",null,{"hidden":true,"children":["$","$15",null,{"fallback":null,"children":["$","$L16",null,{"promise":"$@17"}]}]}]
d:null
11:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
c:null
f:{"metadata":[["$","title","0",{"children":"Real Estate India - Buy, Sell, and Rent Properties"}],["$","meta","1",{"name":"description","content":"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities, or list your property with us. Expert guidance for all your property needs."}],["$","meta","2",{"name":"author","content":"Real Estate India"}],["$","meta","3",{"name":"keywords","content":"real estate India,property for sale,property for rent,buy property,sell property,apartments,houses,villas,commercial property"}],["$","meta","4",{"name":"creator","content":"Real Estate India"}],["$","meta","5",{"name":"publisher","content":"Real Estate India"}],["$","meta","6",{"name":"robots","content":"index, follow"}],["$","meta","7",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","link","8",{"rel":"canonical","href":"https://realestate-india.com/"}],["$","meta","9",{"name":"format-detection","content":"telephone=no, address=no, email=no"}],["$","meta","10",{"property":"og:title","content":"Real Estate India - Buy, Sell, and Rent Properties"}],["$","meta","11",{"property":"og:description","content":"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities."}],["$","meta","12",{"property":"og:url","content":"https://realestate-india.com/"}],["$","meta","13",{"property":"og:site_name","content":"Real Estate India"}],["$","meta","14",{"property":"og:locale","content":"en_IN"}],["$","meta","15",{"property":"og:type","content":"website"}],["$","meta","16",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","17",{"name":"twitter:creator","content":"@realestateindia"}],["$","meta","18",{"name":"twitter:title","content":"Real Estate India - Buy, Sell, and Rent Properties"}],["$","meta","19",{"name":"twitter:description","content":"Find your dream home in India with our comprehensive real estate platform."}]],"error":null,"digest":"$undefined"}
17:{"metadata":"$f:metadata","error":null,"digest":"$undefined"}
8:["$","div",null,{"className":"text-center py-16","children":[["$","h3",null,{"className":"text-xl font-semibold text-gray-600 mb-4","children":"No featured properties available"}],["$","p",null,{"className":"text-gray-500","children":"Check back soon for amazing property listings!"}]]}]
