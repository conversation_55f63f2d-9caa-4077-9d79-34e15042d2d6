<?php
/**
 * Production Admin Password Reset Script
 * Use this script to reset admin password on housing.okayy.in
 * 
 * IMPORTANT: Delete this file after use for security!
 */

require_once 'Real Estate/php-backend/config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Admin credentials
    $adminEmail = '<EMAIL>';
    $adminPassword = 'Admin@2024!'; // Strong password
    
    // Hash password using <PERSON><PERSON>'s password_hash (NOT MD5)
    $hashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
    
    echo "Starting admin password reset...\n";
    
    // Check if admin user exists
    $checkQuery = "SELECT id, email, role FROM users WHERE email = :email";
    $checkStmt = $db->prepare($checkQuery);
    $checkStmt->bindParam(':email', $adminEmail);
    $checkStmt->execute();
    $existingAdmin = $checkStmt->fetch();
    
    if ($existingAdmin) {
        // Update existing admin password
        $updateQuery = "UPDATE users SET password = :password, is_active = 1 WHERE email = :email";
        $updateStmt = $db->prepare($updateQuery);
        $updateStmt->bindParam(':password', $hashedPassword);
        $updateStmt->bindParam(':email', $adminEmail);
        
        if ($updateStmt->execute()) {
            echo "✅ Admin password updated successfully!\n";
            echo "Email: $adminEmail\n";
            echo "Password: $adminPassword\n";
            echo "Role: " . $existingAdmin['role'] . "\n";
        } else {
            echo "❌ Failed to update admin password\n";
        }
    } else {
        // Create new admin user
        $adminId = 'admin_' . uniqid();
        $insertQuery = "INSERT INTO users (id, name, email, password, role, is_active, created_at, updated_at) 
                       VALUES (:id, :name, :email, :password, 'ADMIN', 1, NOW(), NOW())";
        
        $insertStmt = $db->prepare($insertQuery);
        $insertStmt->bindParam(':id', $adminId);
        $insertStmt->bindParam(':name', 'Admin User');
        $insertStmt->bindParam(':email', $adminEmail);
        $insertStmt->bindParam(':password', $hashedPassword);
        
        if ($insertStmt->execute()) {
            echo "✅ New admin user created successfully!\n";
            echo "Email: $adminEmail\n";
            echo "Password: $adminPassword\n";
            echo "Role: ADMIN\n";
        } else {
            echo "❌ Failed to create admin user\n";
        }
    }
    
    // Verify the password works
    echo "\n🔍 Verifying password...\n";
    $verifyQuery = "SELECT id, email, password, role FROM users WHERE email = :email";
    $verifyStmt = $db->prepare($verifyQuery);
    $verifyStmt->bindParam(':email', $adminEmail);
    $verifyStmt->execute();
    $admin = $verifyStmt->fetch();
    
    if ($admin && password_verify($adminPassword, $admin['password'])) {
        echo "✅ Password verification successful!\n";
        echo "Admin can now login with these credentials.\n";
    } else {
        echo "❌ Password verification failed!\n";
    }
    
    echo "\n📋 Next Steps:\n";
    echo "1. Try logging in at: https://housing.okayy.in/admin/login\n";
    echo "2. Use email: $adminEmail\n";
    echo "3. Use password: $adminPassword\n";
    echo "4. DELETE this file after successful login for security!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Check your database configuration in php-backend/config/database.php\n";
}
?>
