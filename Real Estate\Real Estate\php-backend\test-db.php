<?php
// Test database connection and check users
require_once 'config/database.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=UTF-8');

echo "<h1>Database Connection Test</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();

    echo "<p style='color: green;'>✅ Database connection successful!</p>";

    // Check if users table exists
    $check_table = "SHOW TABLES LIKE 'users'";
    $stmt = $db->prepare($check_table);
    $stmt->execute();
    $table_exists = $stmt->fetch();

    if (!$table_exists) {
        echo "<p style='color: red;'>❌ Users table does not exist. Please import database.sql</p>";
        exit;
    }

    echo "<p style='color: green;'>✅ Users table exists</p>";

    // Check users in database
    $query = "SELECT id, name, email, role, is_active FROM users";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $users = $stmt->fetchAll();

    echo "<h2>👥 Users in database:</h2>";
    echo "<p>Count: " . count($users) . "</p>";

    if (count($users) == 0) {
        echo "<p style='color: orange;'>⚠️ No users found. You need to create the admin user.</p>";
        echo "<h3>Run this SQL to create admin user:</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px;'>";
        echo "INSERT INTO users (id, name, email, password, role) VALUES \n";
        echo "('admin-user-id', 'Admin User', '<EMAIL>', '\$2y\$12\$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VJunLVlm2', 'ADMIN');";
        echo "</pre>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Role</th><th>Active</th></tr>";

        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($user['id']) . "</td>";
            echo "<td>" . htmlspecialchars($user['name']) . "</td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . htmlspecialchars($user['role']) . "</td>";
            echo "<td>" . ($user['is_active'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // Test password verification for admin user
    $admin_query = "SELECT password FROM users WHERE email = '<EMAIL>'";
    $admin_stmt = $db->prepare($admin_query);
    $admin_stmt->execute();
    $admin = $admin_stmt->fetch();

    if ($admin) {
        echo "<h3>🔐 Testing admin password:</h3>";
        $test_password = 'admin123';
        $password_valid = password_verify($test_password, $admin['password']);
        echo "<p>Password 'admin123' valid: " . ($password_valid ? '<span style="color: green;">✅ Yes</span>' : '<span style="color: red;">❌ No</span>') . "</p>";

        if (!$password_valid) {
            echo "<p style='color: orange;'>⚠️ Admin password is incorrect. Run this SQL to fix:</p>";
            echo "<pre style='background: #f5f5f5; padding: 10px;'>";
            echo "UPDATE users SET password = '\$2y\$12\$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VJunLVlm2' WHERE email = '<EMAIL>';";
            echo "</pre>";
        }
    } else {
        echo "<p style='color: red;'>❌ Admin user not found</p>";
    }

    // Check properties table
    echo "<h2>🏠 Properties Check:</h2>";
    $prop_check = "SHOW TABLES LIKE 'properties'";
    $prop_stmt = $db->prepare($prop_check);
    $prop_stmt->execute();
    $prop_exists = $prop_stmt->fetch();

    if ($prop_exists) {
        $prop_count_query = "SELECT COUNT(*) as total FROM properties";
        $prop_count_stmt = $db->prepare($prop_count_query);
        $prop_count_stmt->execute();
        $prop_count = $prop_count_stmt->fetch()['total'];
        echo "<p style='color: green;'>✅ Properties table exists with {$prop_count} properties</p>";
    } else {
        echo "<p style='color: red;'>❌ Properties table does not exist</p>";
    }

    // Check blog_posts table
    echo "<h2>📝 Blog Posts Check:</h2>";
    $blog_check = "SHOW TABLES LIKE 'blog_posts'";
    $blog_stmt = $db->prepare($blog_check);
    $blog_stmt->execute();
    $blog_exists = $blog_stmt->fetch();

    if ($blog_exists) {
        $blog_count_query = "SELECT COUNT(*) as total FROM blog_posts WHERE published = 1";
        $blog_count_stmt = $db->prepare($blog_count_query);
        $blog_count_stmt->execute();
        $blog_count = $blog_count_stmt->fetch()['total'];
        echo "<p style='color: green;'>✅ Blog posts table exists with {$blog_count} published posts</p>";
    } else {
        echo "<p style='color: red;'>❌ Blog posts table does not exist</p>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>
