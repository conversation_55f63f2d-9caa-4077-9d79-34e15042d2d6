(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{4879:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var r=t(5155),a=t(2115),l=t(5695),i=t(5494),n=t(6821),d=t(6874),o=t.n(d),c=t(1008);function x(){let e=(0,l.useRouter)(),[s,t]=(0,a.useState)(null),[d,x]=(0,a.useState)([]),[p,m]=(0,a.useState)([]),[h,g]=(0,a.useState)([]),[u,b]=(0,a.useState)(0),[N,j]=(0,a.useState)("all");return((0,a.useEffect)(()=>{(async()=>{try{let s=await c.R2.checkSession();t(s),s.user||e.push("/login?error=Please%20login%20to%20access%20dashboard")}catch(s){console.error("Session fetch error:",s),e.push("/login?error=Please%20login%20to%20access%20dashboard")}})()},[]),(0,a.useEffect)(()=>{(async()=>{try{let[e,s]=await Promise.all([c.Eo.getProperties(),c.Eo.getInquiries()]);Array.isArray(e)?x(e):(console.error("API /api/user/properties did not return an array:",e),x([])),Array.isArray(s)?m(s):(console.error("API /api/user/inquiries did not return an array:",s),m([])),g([])}catch(e){console.error("Failed to fetch dashboard data:",e)}})()},[u]),null===s)?(0,r.jsx)("main",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})}):s.user?(0,r.jsxs)("main",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(i.Navbar,{}),(0,r.jsxs)("div",{className:"container-custom py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Welcome to Your Dashboard!"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"Manage your properties, inquiries, and saved listings here."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Total Properties"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-primary",children:d.length}),(0,r.jsx)("p",{className:"text-gray-600",children:"Your listed properties"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Approved Properties"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-green-600",children:d.filter(e=>"APPROVED"===e.approvalStatus).length}),(0,r.jsx)("p",{className:"text-gray-600",children:"Properties approved by admin"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Pending Properties"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-yellow-600",children:d.filter(e=>"PENDING"===e.approvalStatus).length}),(0,r.jsx)("p",{className:"text-gray-600",children:"Properties awaiting approval"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Rejected Properties"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-red-600",children:d.filter(e=>"REJECTED"===e.approvalStatus).length}),(0,r.jsx)("p",{className:"text-gray-600",children:"Properties rejected by admin"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Inquiries Received"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-primary",children:p.length}),(0,r.jsx)("p",{className:"text-gray-600",children:"Inquiries on your properties"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Saved Properties"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-primary",children:h.length}),(0,r.jsx)("p",{className:"text-gray-600",children:"Properties you have saved"})]})]}),(0,r.jsxs)("div",{className:"mt-8 bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Your Listed Properties"}),(0,r.jsxs)("div",{className:"flex space-x-4 mb-4",children:[(0,r.jsxs)("button",{onClick:()=>j("all"),className:"px-4 py-2 rounded-md text-sm font-medium ".concat("all"===N?"bg-primary text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:["All (",d.length,")"]}),(0,r.jsxs)("button",{onClick:()=>j("approved"),className:"px-4 py-2 rounded-md text-sm font-medium ".concat("approved"===N?"bg-primary text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:["Approved (",d.filter(e=>"APPROVED"===e.approvalStatus).length,")"]}),(0,r.jsxs)("button",{onClick:()=>j("pending"),className:"px-4 py-2 rounded-md text-sm font-medium ".concat("pending"===N?"bg-primary text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:["Pending (",d.filter(e=>"PENDING"===e.approvalStatus).length,")"]}),(0,r.jsxs)("button",{onClick:()=>j("rejected"),className:"px-4 py-2 rounded-md text-sm font-medium ".concat("rejected"===N?"bg-primary text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:["Rejected (",d.filter(e=>"REJECTED"===e.approvalStatus).length,")"]})]}),0===d.filter(e=>"approved"===N?"APPROVED"===e.approvalStatus:"pending"===N?"PENDING"===e.approvalStatus:"rejected"!==N||"REJECTED"===e.approvalStatus).length?(0,r.jsx)("p",{className:"text-gray-600",children:"No properties found for the selected status."}):(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:d.filter(e=>"approved"===N?"APPROVED"===e.approvalStatus:"pending"===N?"PENDING"===e.approvalStatus:"rejected"!==N||"REJECTED"===e.approvalStatus).map(e=>{let s="string"==typeof e.images?JSON.parse(e.images):e.images;return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,r.jsx)("img",{src:s[0],alt:e.title,className:"w-full h-48 object-cover rounded-md mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-1",children:e.title}),(0,r.jsxs)("p",{className:"text-primary font-bold",children:[e.currency," ",e.price.toLocaleString()]}),(0,r.jsxs)("p",{className:"text-gray-600 text-sm",children:[e.type," in ",e.city,", ",e.state]}),(0,r.jsxs)("p",{className:"text-gray-500 text-xs mt-2",children:["Status: ",e.approvalStatus]}),(0,r.jsxs)("p",{className:"text-gray-500 text-xs",children:["Views: ",e.viewCount]}),(0,r.jsxs)("p",{className:"text-gray-500 text-xs",children:["Inquiries: ",e._count.inquiries]}),(0,r.jsxs)("p",{className:"text-gray-500 text-xs",children:["Saved By: ",e._count.savedBy]}),(0,r.jsx)(o(),{href:"/properties/".concat(e.id),className:"text-blue-500 hover:underline text-sm mt-2 block",children:"View Details"})]},e.id)})})]}),(0,r.jsxs)("div",{className:"mt-8 bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(o(),{href:"/properties/create/",className:"btn-primary text-center",children:"List New Property"}),(0,r.jsx)(o(),{href:"/properties/",className:"btn-secondary text-center",children:"Browse Properties"}),(0,r.jsx)("button",{onClick:()=>b(e=>e+1),className:"btn-secondary text-center",children:"Refresh Dashboard Data"})]})]})]}),(0,r.jsx)(n.w,{})]}):null}},5695:(e,s,t)=>{"use strict";var r=t(8999);t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},5876:(e,s,t)=>{Promise.resolve().then(t.bind(t,4879))}},e=>{var s=s=>e(e.s=s);e.O(0,[874,494,821,441,684,358],()=>s(5876)),_N_E=e.O()}]);