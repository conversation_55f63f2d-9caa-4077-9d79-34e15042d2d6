(()=>{var e={};e.id=957,e.ids=[957],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1031:()=>{throw Error('Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31mx\x1b[0m Unexpected token `div`. Expected jsx identifier\n     ,-[\x1b[36;1;4mC:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\dashboard\\page.tsx\x1b[0m:117:1]\n \x1b[2m114\x1b[0m |   const pendingProperties = properties.filter(p => p.approval_status === \'PENDING\')\n \x1b[2m115\x1b[0m | \n \x1b[2m116\x1b[0m |   return (\n \x1b[2m117\x1b[0m |     <div className="min-h-screen bg-gray-100">\n     : \x1b[35;1m     ^^^\x1b[0m\n \x1b[2m118\x1b[0m |       {/* Header */}\n \x1b[2m119\x1b[0m |       <header className="bg-white shadow">\n \x1b[2m120\x1b[0m |         <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">\n     `----\n\n\nCaused by:\n    Syntax Error')},1125:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>p,routeModule:()=>u,tree:()=>l});var s=t(5239),a=t(8088),n=t(8170),i=t.n(n),o=t(893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["admin",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1031)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\dashboard\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=["C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\dashboard\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/dashboard/page",pathname:"/admin/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},1135:()=>{},1388:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o,metadata:()=>i});var s=t(7413),a=t(5091),n=t.n(a);t(1135);let i={title:{default:"Real Estate India - Buy, Sell, and Rent Properties",template:"%s | Real Estate India"},description:"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities, or list your property with us. Expert guidance for all your property needs.",keywords:["real estate India","property for sale","property for rent","buy property","sell property","apartments","houses","villas","commercial property"],authors:[{name:"Real Estate India"}],creator:"Real Estate India",publisher:"Real Estate India",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("https://realestate-india.com"),alternates:{canonical:"/"},openGraph:{type:"website",locale:"en_IN",url:"https://realestate-india.com",title:"Real Estate India - Buy, Sell, and Rent Properties",description:"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities.",siteName:"Real Estate India"},twitter:{card:"summary_large_image",title:"Real Estate India - Buy, Sell, and Rent Properties",description:"Find your dream home in India with our comprehensive real estate platform.",creator:"@realestateindia"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function o({children:e}){return(0,s.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,s.jsx)("body",{className:`${n().variable} font-sans bg-background text-text-primary antialiased`,children:e})})}},4940:()=>{},5171:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},6487:()=>{},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9243:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[771],()=>t(1125));module.exports=s})();