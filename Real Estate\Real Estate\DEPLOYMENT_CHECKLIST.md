
# 📋 Deployment Checklist

## Pre-Upload
- [ ] Built Next.js application (npm run build)
- [ ] Verified 'out' folder exists with static files
- [ ] Verified 'php-backend' folder exists with PHP APIs

## Upload to Shared Hosting
- [ ] Uploaded all contents of 'out' folder to public_html/
- [ ] Uploaded 'php-backend' folder to public_html/php-backend/
- [ ] Created uploads folder: public_html/php-backend/uploads/

## Database Setup
- [ ] Created MySQL database in hosting control panel
- [ ] Imported database.sql schema
- [ ] Updated database credentials in php-backend/config/database.php

## Permissions & Security
- [ ] Set uploads folder permissions to 755
- [ ] Set PHP files permissions to 644
- [ ] Added .htaccess protection for config files

## Testing
- [ ] Frontend loads: https://yourdomain.com
- [ ] API responds: https://yourdomain.com/php-backend/api/auth/check-session.php
- [ ] User registration works
- [ ] Property creation works
- [ ] File upload works

## Go Live!
- [ ] All tests passed
- [ ] Application is live and functional
