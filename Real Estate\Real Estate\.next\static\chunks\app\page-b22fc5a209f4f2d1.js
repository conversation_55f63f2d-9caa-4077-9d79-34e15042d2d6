(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2975:(e,t,a)=>{"use strict";a.d(t,{TestimonialsSection:()=>n});var s=a(5155),r=a(2115),i=a(6766);function o(e){let{name:t,role:a,image:r,quote:o,rating:l}=e;return(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-8 md:p-12 transition-all duration-500 transform hover:shadow-xl",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row items-center md:items-start gap-8",children:[(0,s.jsx)("div",{className:"relative w-24 h-24 md:w-32 md:h-32 flex-shrink-0",children:(0,s.jsx)(i.default,{src:r,alt:t,fill:!0,className:"object-cover rounded-full border-4 border-primary/20"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"flex mb-4",children:Array.from({length:5}).map((e,t)=>(0,s.jsx)("svg",{className:"w-5 h-5 ".concat(t<l?"text-yellow-400":"text-gray-300"),fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})},t))}),(0,s.jsxs)("blockquote",{className:"text-lg md:text-xl italic text-gray-700 mb-6",children:['"',o,'"']}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-bold text-lg text-primary",children:t}),(0,s.jsx)("p",{className:"text-gray-600",children:a})]})]})]})})}let l=[{id:1,name:"Sarah Johnson",role:"Home Buyer",image:"https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=1374&auto=format&fit=crop",quote:"Working with RealEstate was an absolute pleasure. They helped me find my dream home in just a few weeks. The team was professional, knowledgeable, and always available to answer my questions. I couldn't be happier with my new home!",rating:5},{id:2,name:"Michael Rodriguez",role:"Property Seller",image:"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=1374&auto=format&fit=crop",quote:"I was impressed by how quickly RealEstate sold my property. Their marketing strategy was effective, and they managed to get me a better price than I expected. The entire process was smooth and stress-free. I highly recommend their services!",rating:5},{id:3,name:"Emily Chen",role:"Apartment Renter",image:"https://images.unsplash.com/photo-1580489944761-15a19d654956?q=80&w=1522&auto=format&fit=crop",quote:"As someone new to the city, finding an apartment was daunting. RealEstate made it easy! They listened to my needs and showed me options that fit my budget and preferences. I found a great place in a neighborhood I love. Thank you!",rating:4},{id:4,name:"David Wilson",role:"Investment Property Buyer",image:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=1374&auto=format&fit=crop",quote:"RealEstate's expertise in the investment property market is unmatched. They helped me identify properties with great ROI potential and guided me through the purchasing process. Their advice has been invaluable for building my real estate portfolio.",rating:5}];function n(){let{activeIndex:e,activeTestimonial:t,nextTestimonial:a,prevTestimonial:i,goToTestimonial:n}=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5e3,[a,s]=(0,r.useState)(0);return(0,r.useEffect)(()=>{let a=setInterval(()=>{s(t=>(t+1)%e.length)},t);return()=>clearInterval(a)},[e,t]),{activeIndex:a,activeTestimonial:e[a],nextTestimonial:()=>{s(t=>(t+1)%e.length)},prevTestimonial:()=>{s(t=>(t-1+e.length)%e.length)},goToTestimonial:e=>{s(e)}}}(l);return(0,s.jsx)("div",{className:"relative overflow-hidden py-16 bg-gray-50",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"What Our Clients Say"}),(0,s.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Hear from our satisfied clients about their experience working with our real estate professionals."})]}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)(o,{name:t.name,role:t.role,image:t.image,quote:t.quote,rating:t.rating}),(0,s.jsxs)("div",{className:"flex justify-center mt-8 space-x-4",children:[(0,s.jsx)("button",{onClick:i,className:"w-12 h-12 rounded-full bg-white shadow-md flex items-center justify-center text-gray-700 hover:text-primary hover:shadow-lg transition-all duration-300","aria-label":"Previous testimonial",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),(0,s.jsx)("button",{onClick:a,className:"w-12 h-12 rounded-full bg-white shadow-md flex items-center justify-center text-gray-700 hover:text-primary hover:shadow-lg transition-all duration-300","aria-label":"Next testimonial",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),(0,s.jsx)("div",{className:"flex justify-center mt-6 space-x-2",children:l.map((t,a)=>(0,s.jsx)("button",{onClick:()=>n(a),className:"w-3 h-3 rounded-full transition-colors duration-300 ".concat(a===e?"bg-primary":"bg-gray-300"),"aria-label":"Go to testimonial ".concat(a+1)},a))})]})]})})}},6255:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,6874,23)),Promise.resolve().then(a.t.bind(a,3063,23)),Promise.resolve().then(a.bind(a,5494)),Promise.resolve().then(a.bind(a,4527)),Promise.resolve().then(a.bind(a,2975))}},e=>{var t=t=>e(e.s=t);e.O(0,[874,63,494,982,441,684,358],()=>t(6255)),_N_E=e.O()}]);