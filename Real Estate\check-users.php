<?php
try {
    $db = new PDO('sqlite:Real Estate/prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>Users in Database</h1>";
    
    $users = $db->query("SELECT id, name, email, role FROM User")->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($users)) {
        echo "<p>No users found in database.</p>";
        
        // Create a test user
        $hashedPassword = password_hash('password123', PASSWORD_DEFAULT);
        $stmt = $db->prepare("INSERT INTO User (name, email, password, role) VALUES (?, ?, ?, ?)");
        $stmt->execute(['Test User', '<EMAIL>', $hashedPassword, 'USER']);
        
        echo "<p>Created test user:</p>";
        echo "<ul>";
        echo "<li>Email: <EMAIL></li>";
        echo "<li>Password: password123</li>";
        echo "<li>Role: USER</li>";
        echo "</ul>";
    } else {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Role</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($user['id']) . "</td>";
            echo "<td>" . htmlspecialchars($user['name']) . "</td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . htmlspecialchars($user['role']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage();
}
?>