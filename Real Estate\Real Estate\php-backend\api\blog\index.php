<?php
require_once '../../config/database.php';

setCorsHeaders();

$method = $_SERVER['REQUEST_METHOD'];

try {
    $database = new Database();
    $db = $database->getConnection();
    
    switch ($method) {
        case 'GET':
            handleGetBlogPosts($db);
            break;
        default:
            sendError('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Blog API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

function handleGetBlogPosts($db) {
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $offset = ($page - 1) * $limit;
    
    // Build WHERE clause
    $where_conditions = ["published = 1"];
    $params = [];
    
    // Category filter
    if (isset($_GET['category']) && !empty($_GET['category'])) {
        $where_conditions[] = "category = :category";
        $params[':category'] = $_GET['category'];
    }
    
    // Search filter
    if (isset($_GET['search']) && !empty($_GET['search'])) {
        $search = '%' . $_GET['search'] . '%';
        $where_conditions[] = "(title LIKE :search OR content LIKE :search2 OR excerpt LIKE :search3)";
        $params[':search'] = $search;
        $params[':search2'] = $search;
        $params[':search3'] = $search;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Get total count
    $count_query = "SELECT COUNT(*) as total FROM blog_posts WHERE $where_clause";
    $count_stmt = $db->prepare($count_query);
    foreach ($params as $key => $value) {
        $count_stmt->bindValue($key, $value);
    }
    $count_stmt->execute();
    $total = $count_stmt->fetch()['total'];
    
    // Get blog posts
    $query = "SELECT bp.*, u.name as author_name 
              FROM blog_posts bp 
              LEFT JOIN users u ON bp.author_id = u.id 
              WHERE $where_clause 
              ORDER BY bp.created_at DESC 
              LIMIT :limit OFFSET :offset";
    
    $stmt = $db->prepare($query);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    
    $posts = $stmt->fetchAll();
    
    // Process posts data
    foreach ($posts as &$post) {
        $post['tags'] = json_decode($post['tags'], true) ?: [];
        $post['author'] = [
            'name' => $post['author_name'] ?: 'Admin'
        ];
        unset($post['author_name'], $post['author_id']);
    }
    
    $total_pages = ceil($total / $limit);
    
    sendResponse([
        'posts' => $posts,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => $total,
            'pages' => $total_pages
        ]
    ]);
}
?>
