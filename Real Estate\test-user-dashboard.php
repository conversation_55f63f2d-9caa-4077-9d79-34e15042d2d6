<?php
/**
 * User Dashboard Test Script
 * Tests user authentication and dashboard API endpoints
 */
?>
<!DOCTYPE html>
<html>
<head>
    <title>🧪 User Dashboard Test - housing.okayy.in</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .test-item { padding: 10px; margin: 5px 0; background: #f8f9fa; border-radius: 5px; }
        .btn { background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; max-height: 200px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 User Dashboard Test</h1>
        <p><strong>Testing:</strong> User authentication and dashboard functionality</p>
        
        <?php
        // Test 1: Database Connection
        echo "<div class='test-section'>";
        echo "<h2>1. 🔗 Database Connection</h2>";
        
        try {
            require_once 'php-backend/config/database.php';
            $database = new Database();
            $db = $database->getConnection();
            echo "<div class='test-item'><span class='success'>✅ Database connection: Working</span></div>";
            
            // Check users table
            $stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE role = 'USER'");
            $result = $stmt->fetch();
            $userCount = $result['count'];
            echo "<div class='test-item'><span class='info'>👥 Regular users: $userCount</span></div>";
            
        } catch (Exception $e) {
            echo "<div class='test-item'><span class='error'>❌ Database error: " . $e->getMessage() . "</span></div>";
        }
        echo "</div>";
        
        // Test 2: User API Endpoints
        echo "<div class='test-section'>";
        echo "<h2>2. 🔌 User API Endpoints</h2>";
        
        $userEndpoints = [
            'User Properties' => '/php-backend/api/user/properties.php',
            'User Inquiries' => '/php-backend/api/user/inquiries.php',
            'Check Session' => '/php-backend/api/auth/check-session.php',
            'User Login' => '/php-backend/api/auth/login.php'
        ];
        
        foreach ($userEndpoints as $name => $endpoint) {
            $url = 'https://' . $_SERVER['HTTP_HOST'] . $endpoint;
            echo "<div class='test-item'>";
            echo "<strong>$name:</strong> ";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode == 200) {
                $json = json_decode($response, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    echo "<span class='success'>✅ Working (JSON)</span>";
                } else {
                    echo "<span class='warning'>⚠️ Working (Non-JSON)</span>";
                }
            } elseif ($httpCode == 401) {
                echo "<span class='info'>🔒 Requires Authentication (Expected)</span>";
            } else {
                echo "<span class='error'>❌ HTTP $httpCode</span>";
            }
            
            echo " - <a href='$url' target='_blank' class='btn'>Test</a>";
            echo "</div>";
        }
        echo "</div>";
        
        // Test 3: Sample User Data
        echo "<div class='test-section'>";
        echo "<h2>3. 👤 Sample User Data</h2>";
        
        try {
            // Get a sample user
            $stmt = $db->query("SELECT id, name, email, role FROM users WHERE role = 'USER' LIMIT 1");
            $sampleUser = $stmt->fetch();
            
            if ($sampleUser) {
                echo "<div class='test-item'><span class='success'>✅ Sample user found</span></div>";
                echo "<div class='test-item'>";
                echo "<strong>Sample User:</strong><br>";
                echo "ID: " . htmlspecialchars($sampleUser['id']) . "<br>";
                echo "Name: " . htmlspecialchars($sampleUser['name']) . "<br>";
                echo "Email: " . htmlspecialchars($sampleUser['email']) . "<br>";
                echo "Role: " . htmlspecialchars($sampleUser['role']);
                echo "</div>";
                
                // Check user's properties
                $stmt = $db->prepare("SELECT COUNT(*) as count FROM properties WHERE owner_id = ?");
                $stmt->execute([$sampleUser['id']]);
                $result = $stmt->fetch();
                $propertyCount = $result['count'];
                echo "<div class='test-item'><span class='info'>🏠 User's properties: $propertyCount</span></div>";
                
            } else {
                echo "<div class='test-item'><span class='warning'>⚠️ No regular users found</span></div>";
            }
        } catch (Exception $e) {
            echo "<div class='test-item'><span class='error'>❌ Error: " . $e->getMessage() . "</span></div>";
        }
        echo "</div>";
        
        // Test 4: Frontend Dashboard Test
        echo "<div class='test-section'>";
        echo "<h2>4. 🌐 Frontend Dashboard Test</h2>";
        
        $dashboardPages = [
            'User Dashboard' => '/dashboard',
            'User Login' => '/login',
            'User Signup' => '/signup',
            'Property Create' => '/properties/create'
        ];
        
        foreach ($dashboardPages as $name => $path) {
            echo "<div class='test-item'>";
            echo "<strong>$name:</strong> ";
            echo "<a href='$path' target='_blank' class='btn'>Test Page</a>";
            echo "<span class='info'> - Check browser console for errors</span>";
            echo "</div>";
        }
        echo "</div>";
        
        // Test 5: JavaScript Error Detection
        echo "<div class='test-section'>";
        echo "<h2>5. 🐛 JavaScript Error Detection</h2>";
        echo "<div id='js-errors'></div>";
        echo "<div class='test-item'><span class='info'>ℹ️ JavaScript errors will appear above (if any)</span></div>";
        echo "</div>";
        ?>
        
        <!-- JavaScript Error Catcher -->
        <script>
            let errorCount = 0;
            const errorContainer = document.getElementById('js-errors');
            
            // Catch JavaScript errors
            window.addEventListener('error', function(e) {
                errorCount++;
                const errorDiv = document.createElement('div');
                errorDiv.className = 'test-item';
                errorDiv.innerHTML = `<span class="error">❌ JS Error ${errorCount}: ${e.message} at ${e.filename}:${e.lineno}</span>`;
                errorContainer.appendChild(errorDiv);
            });
            
            // Catch unhandled promise rejections
            window.addEventListener('unhandledrejection', function(e) {
                errorCount++;
                const errorDiv = document.createElement('div');
                errorDiv.className = 'test-item';
                errorDiv.innerHTML = `<span class="error">❌ Promise Rejection ${errorCount}: ${e.reason}</span>`;
                errorContainer.appendChild(errorDiv);
            });
            
            // Test user API calls
            async function testUserAPIs() {
                const apiTests = [
                    { name: 'Check Session', url: '/php-backend/api/auth/check-session.php' },
                    { name: 'User Properties', url: '/php-backend/api/user/properties.php' },
                    { name: 'User Inquiries', url: '/php-backend/api/user/inquiries.php' }
                ];
                
                for (const test of apiTests) {
                    try {
                        const response = await fetch(test.url);
                        const data = await response.text();
                        console.log(`✅ ${test.name}: ${response.status}`);
                    } catch (error) {
                        console.error(`❌ ${test.name}: ${error.message}`);
                        errorCount++;
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'test-item';
                        errorDiv.innerHTML = `<span class="error">❌ API Error: ${test.name} - ${error.message}</span>`;
                        errorContainer.appendChild(errorDiv);
                    }
                }
                
                // Show success message if no errors
                if (errorCount === 0) {
                    setTimeout(() => {
                        if (errorCount === 0) {
                            const successDiv = document.createElement('div');
                            successDiv.className = 'test-item';
                            successDiv.innerHTML = `<span class="success">✅ No JavaScript errors detected!</span>`;
                            errorContainer.appendChild(successDiv);
                        }
                    }, 2000);
                }
            }
            
            // Run tests when page loads
            document.addEventListener('DOMContentLoaded', testUserAPIs);
        </script>
        
        <div class="test-section">
            <h2>6. 📋 Manual Testing Steps</h2>
            <div class="test-item">
                <strong>Test these manually:</strong>
                <ol>
                    <li>✅ Create a new user account (signup)</li>
                    <li>✅ Login with user credentials</li>
                    <li>✅ Access user dashboard without errors</li>
                    <li>✅ Create a new property listing</li>
                    <li>✅ View user's properties in dashboard</li>
                    <li>✅ Check browser console for errors</li>
                </ol>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="/login" class="btn">👤 User Login</a>
            <a href="/signup" class="btn">📝 User Signup</a>
            <a href="/dashboard" class="btn">📊 User Dashboard</a>
        </div>
        
        <div class="test-section">
            <h2>🗑️ Cleanup</h2>
            <p><strong>After testing, delete this file:</strong> <code>test-user-dashboard.php</code></p>
        </div>
    </div>
</body>
</html>
