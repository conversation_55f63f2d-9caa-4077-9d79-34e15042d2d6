<?php
session_start();

// Debug: Log session data
error_log('Redirect helper called. Session data: ' . print_r($_SESSION, true));

// Check if user is logged in
if (!isset($_SESSION['user_logged_in']) || !$_SESSION['user_logged_in']) {
    error_log('User not logged in, redirecting to login');
    header('Location: http://localhost:3000/login?error=Please%20log%20in%20first');
    exit;
}

// Debug: Log successful redirect
error_log('User logged in, redirecting to Next.js dashboard: http://localhost:3000/dashboard');

// Redirect to Next.js dashboard
header('Location: http://localhost:3000/dashboard');
exit;
?>