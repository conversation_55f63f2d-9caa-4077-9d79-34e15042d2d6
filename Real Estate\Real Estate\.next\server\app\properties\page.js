(()=>{var e={};e.id=754,e.ids=[754],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1606:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Music\\\\Real Estate\\\\Real Estate\\\\src\\\\app\\\\properties\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\properties\\page.tsx","default")},1997:(e,t,s)=>{"use strict";s.d(t,{y:()=>o});var r=s(687),a=s(5814),i=s.n(a);function l(e,t=!0){let s=t?"₹":"";if(e>=1e7){let t=e/1e7;return`${s}${t.toFixed(+(t%1!=0))} Cr`}if(e>=1e5){let t=e/1e5;return`${s}${t.toFixed(+(t%1!=0))} L`}if(!(e>=1e3))return`${s}${e.toLocaleString("en-IN")}`;{let t=e/1e3;return`${s}${t.toFixed(+(t%1!=0))}K`}}function o({property:e}){var t,s,a,o;let n=(e=>{try{return JSON.parse(e||"[]")}catch{return[]}})(e.images),c=n.length>0&&(t=n[0])?t.startsWith("http")?t:t.startsWith("/")?`https://housing.okayy.in${t}`:`https://housing.okayy.in/php-backend/uploads/${t}`:"https://via.placeholder.com/400x300/e5e7eb/6b7280?text=Property+Image",d=(e=>{let t=new Date(e);return 7>=Math.ceil(Math.abs(new Date().getTime()-t.getTime())/864e5)})(e.createdAt);return(0,r.jsxs)("div",{className:"card-elevated group overflow-hidden animate-fade-in",children:[(0,r.jsxs)("div",{className:"relative h-72 w-full overflow-hidden",children:[(0,r.jsx)("img",{src:c,alt:e.title,className:"w-full h-full object-cover transition-all duration-700 group-hover:scale-110 group-hover:brightness-110",onError:e=>{e.currentTarget.src="https://via.placeholder.com/400x300/e5e7eb/6b7280?text=Property+Image"}}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,r.jsx)("div",{className:"absolute top-4 left-4",children:(0,r.jsx)("span",{className:`px-4 py-2 rounded-xl text-sm font-semibold backdrop-blur-sm border border-white/20 ${"PG"===e.type||e.title.toLowerCase().includes("pg")?"bg-purple-500/90 text-white":e.title.toLowerCase().includes("rent")||e.price<1e5?"bg-accent-500/90 text-white":"bg-primary-500/90 text-white"} shadow-soft`,children:"PG"===e.type||e.title.toLowerCase().includes("pg")?"PG":e.title.toLowerCase().includes("rent")||e.price<1e5?"For Rent":"For Sale"})}),d&&(0,r.jsx)("div",{className:"absolute top-4 right-16",children:(0,r.jsx)("span",{className:"px-4 py-2 rounded-xl text-sm font-semibold bg-success-500/90 text-white backdrop-blur-sm border border-white/20 shadow-soft animate-bounce-subtle",children:"✨ New"})}),(0,r.jsx)("div",{className:"absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300",children:(0,r.jsx)("button",{className:"w-full py-3 bg-white/95 backdrop-blur-sm text-text-primary font-semibold rounded-xl shadow-soft hover:bg-white transition-all duration-300",children:"Quick View"})})]}),(0,r.jsxs)("div",{className:"p-6 space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-text-primary group-hover:text-primary-600 transition-colors duration-300 line-clamp-2",children:e.title}),(0,r.jsx)("p",{className:"text-text-tertiary text-sm flex items-center",children:(0,r.jsxs)("span",{className:"line-clamp-1",children:[e.address,", ",e.city,", ",e.state]})})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("span",{className:"text-2xl font-bold text-gradient",children:(s=e.price,a=e.title,o=e.type,a.toLowerCase().includes("rent")||"PG"===o||a.toLowerCase().includes("pg")||a.toLowerCase().includes("paying guest")||s<1e5?`${l(s)}/month`:l(s))}),(e.title.toLowerCase().includes("rent")||"PG"===e.type||e.title.toLowerCase().includes("pg")||e.price<1e5)&&(0,r.jsx)("p",{className:"text-xs text-text-tertiary",children:"per month"})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-sm text-text-tertiary",children:"Price per sqft"}),(0,r.jsxs)("div",{className:"text-lg font-semibold text-text-secondary",children:["₹",Math.round(e.price/e.area).toLocaleString("en-IN")]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 py-4 border-t border-gray-100",children:[e.bedrooms&&(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-lg font-bold text-blue-600",children:e.bedrooms})}),(0,r.jsx)("div",{className:"text-xs text-text-tertiary",children:"Beds"})]}),e.bathrooms&&(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-green-50 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-lg font-bold text-green-600",children:e.bathrooms})}),(0,r.jsx)("div",{className:"text-xs text-text-tertiary",children:"Baths"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-purple-50 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-lg font-bold text-purple-600",children:e.area})}),(0,r.jsx)("div",{className:"text-xs text-text-tertiary",children:"sqft"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(i(),{href:"PG"===e.type?`/pg/${e.id}`:`/properties/${e.id}`,className:"btn-primary flex-1 text-center",children:"View Details"}),(0,r.jsx)("button",{className:"px-4 py-3 bg-primary-50 text-primary-600 font-semibold rounded-xl hover:bg-primary-100 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:"Call"})]})]})]},e.id)}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3145:(e,t,s)=>{Promise.resolve().then(s.bind(s,3568))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3568:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var r=s(687),a=s(3210),i=s(9190),l=s(1317),o=s(2540),n=s(1997),c=s(3870),d=s(216);function p(){let[e,t]=(0,a.useState)([]),[s,p]=(0,a.useState)(null),[x,m]=(0,a.useState)(!0),[u,h]=(0,a.useState)({page:1,limit:12,total:0,pages:0}),[g,f]=(0,a.useState)({type:"",minPrice:"",maxPrice:"",bedrooms:"",city:"",roomType:"",sharing:"",foodIncluded:"",gender:""}),b=(0,a.useRef)(null),y=async(e,s)=>{let r=e||g,a=s||u.page,i=u.limit;if(a&&i){console.log("Fetching properties with filters:",r,"page:",a),m(!0);try{let e=Object.entries(r).reduce((e,[t,s])=>(s&&"string"==typeof s&&""!==s.trim()&&(e[t]=s),e),{});console.log("Clean filters being sent to API:",e);let s={page:a,limit:i,...e};console.log("API filter params:",s);let l=await d.M5.getProperties(s);console.log("API response:",l),l&&l.properties?(t(l.properties),l.pagination&&h(l.pagination),p(null)):(console.warn("Invalid API response structure:",l),t([]),p("No properties found"))}catch(e){console.error("Error loading properties:",e),console.error("Error stack:",e.stack),p(e.message||"Failed to load properties. Please try again."),t([])}finally{m(!1)}}},j=()=>{if(b.current){let e=b.current.offsetTop-80;window.scrollTo({top:e,behavior:"smooth"})}},v=(0,a.useCallback)(e=>{f(e),h(e=>({...e,page:1})),y(e,1),setTimeout(()=>{j()},100)},[]),N=e=>{h(t=>({...t,page:e})),setTimeout(()=>{j()},100)};return(0,r.jsxs)("main",{className:"min-h-screen",children:[(0,r.jsx)(i.Navbar,{}),(0,r.jsx)("section",{className:"bg-gray-100 py-12",children:(0,r.jsxs)("div",{className:"container-custom",children:[(0,r.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Properties"}),(0,r.jsx)("p",{className:"text-lg text-text-secondary mb-8",children:"Browse our extensive collection of properties for sale and rent. Use the filters to find your perfect match."}),(0,r.jsx)(o.SearchBar,{})]})}),(0,r.jsx)("section",{ref:b,"data-results":!0,className:"py-12",children:(0,r.jsx)("div",{className:"container-custom",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,r.jsx)("div",{className:"lg:w-1/4",children:(0,r.jsx)(c.f,{onFilterChange:v})}),(0,r.jsxs)("div",{className:"lg:w-3/4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsxs)("p",{className:"text-text-secondary",children:["Showing ",(0,r.jsx)("span",{className:"font-semibold",children:e.length})," of ",(0,r.jsx)("span",{className:"font-semibold",children:u.total})," properties"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("label",{htmlFor:"sort",className:"text-text-secondary",children:"Sort by:"}),(0,r.jsxs)("select",{id:"sort",className:"border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",children:[(0,r.jsx)("option",{value:"newest",children:"Newest"}),(0,r.jsx)("option",{value:"price-asc",children:"Price (Low to High)"}),(0,r.jsx)("option",{value:"price-desc",children:"Price (High to Low)"})]})]})]}),x?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"bg-gray-300 h-48 rounded-lg mb-4"}),(0,r.jsx)("div",{className:"h-4 bg-gray-300 rounded mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-300 rounded w-3/4"})]},t))}):0===e.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-500 text-lg mb-4",children:"No properties found"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Try adjusting your search filters"})]}):(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,r.jsx)(n.y,{property:e},e.id))}),u.pages>1&&(0,r.jsx)("div",{className:"mt-12 flex justify-center",children:(0,r.jsxs)("nav",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>N(u.page-1),disabled:1===u.page,className:"w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),[...Array(Math.min(5,u.pages))].map((e,t)=>{let s=t+1;return(0,r.jsx)("button",{onClick:()=>N(s),className:`w-10 h-10 rounded-md flex items-center justify-center ${u.page===s?"bg-primary text-white":"border border-gray-300 hover:bg-gray-100"}`,children:s},s)}),u.pages>5&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"text-gray-500",children:"..."}),(0,r.jsx)("button",{onClick:()=>N(u.pages),className:"w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center hover:bg-gray-100",children:u.pages})]}),(0,r.jsx)("button",{onClick:()=>N(u.page+1),disabled:u.page===u.pages,className:"w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})})]})]})})}),(0,r.jsx)(l.w,{})]})}},3873:e=>{"use strict";e.exports=require("path")},4259:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=s(5239),a=s(8088),i=s(8170),l=s.n(i),o=s(893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);s.d(t,n);let c={children:["",{children:["properties",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1606)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\properties\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\properties\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/properties/page",pathname:"/properties",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6697:(e,t,s)=>{Promise.resolve().then(s.bind(s,1606))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[771,814,604,317,540,870],()=>s(4259));module.exports=r})();