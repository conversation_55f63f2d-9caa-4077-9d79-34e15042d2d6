import Link from 'next/link';
import Image from 'next/image';

const categories = [
  {
    id: 1,
    name: 'Houses',
    description: 'Find your dream house',
    image: 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?q=80&w=1470&auto=format&fit=crop',
    link: '/properties?type=house'
  },
  {
    id: 2,
    name: 'Apartments',
    description: 'Modern apartment living',
    image: 'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?q=80&w=1035&auto=format&fit=crop',
    link: '/properties?type=apartment'
  },
  {
    id: 3,
    name: 'Condos',
    description: 'Luxury condominium units',
    image: 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?q=80&w=1470&auto=format&fit=crop',
    link: '/properties?type=condo'
  },
  {
    id: 4,
    name: 'Land',
    description: 'Build your future home',
    image: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?q=80&w=1032&auto=format&fit=crop',
    link: '/properties?type=land'
  },
];

export function PropertyCategories() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      {categories.map((category) => (
        <Link 
          key={category.id} 
          href={category.link}
          className="group overflow-hidden rounded-lg shadow-md transition-transform duration-300 hover:-translate-y-2"
        >
          <div className="relative h-60 w-full overflow-hidden">
            <Image
              src={category.image}
              alt={category.name}
              fill
              className="object-cover transition-transform duration-500 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent">
              <div className="absolute bottom-0 left-0 p-6 text-white">
                <h3 className="text-xl font-bold mb-1">{category.name}</h3>
                <p className="text-sm text-gray-200">{category.description}</p>
              </div>
            </div>
          </div>
        </Link>
      ))}
    </div>
  );
}