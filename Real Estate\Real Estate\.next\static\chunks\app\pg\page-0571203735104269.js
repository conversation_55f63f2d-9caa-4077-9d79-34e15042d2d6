(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[334],{4659:(e,t,s)=>{"use strict";s.d(t,{y:()=>n});var r=s(5155),a=s(6874),i=s.n(a);function l(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],s=t?"₹":"";if(e>=1e7){let t=e/1e7;return"".concat(s).concat(t.toFixed(+(t%1!=0))," Cr")}if(e>=1e5){let t=e/1e5;return"".concat(s).concat(t.toFixed(+(t%1!=0))," L")}if(!(e>=1e3))return"".concat(s).concat(e.toLocaleString("en-IN"));{let t=e/1e3;return"".concat(s).concat(t.toFixed(+(t%1!=0)),"K")}}function n(e){var t,s,a,n;let{property:c}=e,o=(e=>{try{return JSON.parse(e||"[]")}catch(e){return[]}})(c.images),d=o.length>0&&(t=o[0])?t.startsWith("http")?t:t.startsWith("/")?"https://housing.okayy.in".concat(t):"https://housing.okayy.in/php-backend/uploads/".concat(t):"https://via.placeholder.com/400x300/e5e7eb/6b7280?text=Property+Image",m=(e=>{let t=new Date(e);return 7>=Math.ceil(Math.abs(new Date().getTime()-t.getTime())/864e5)})(c.createdAt);return(0,r.jsxs)("div",{className:"card-elevated group overflow-hidden animate-fade-in",children:[(0,r.jsxs)("div",{className:"relative h-72 w-full overflow-hidden",children:[(0,r.jsx)("img",{src:d,alt:c.title,className:"w-full h-full object-cover transition-all duration-700 group-hover:scale-110 group-hover:brightness-110",onError:e=>{e.currentTarget.src="https://via.placeholder.com/400x300/e5e7eb/6b7280?text=Property+Image"}}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,r.jsx)("div",{className:"absolute top-4 left-4",children:(0,r.jsx)("span",{className:"px-4 py-2 rounded-xl text-sm font-semibold backdrop-blur-sm border border-white/20 ".concat("PG"===c.type||c.title.toLowerCase().includes("pg")?"bg-purple-500/90 text-white":c.title.toLowerCase().includes("rent")||c.price<1e5?"bg-accent-500/90 text-white":"bg-primary-500/90 text-white"," shadow-soft"),children:"PG"===c.type||c.title.toLowerCase().includes("pg")?"PG":c.title.toLowerCase().includes("rent")||c.price<1e5?"For Rent":"For Sale"})}),m&&(0,r.jsx)("div",{className:"absolute top-4 right-16",children:(0,r.jsx)("span",{className:"px-4 py-2 rounded-xl text-sm font-semibold bg-success-500/90 text-white backdrop-blur-sm border border-white/20 shadow-soft animate-bounce-subtle",children:"✨ New"})}),(0,r.jsx)("div",{className:"absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300",children:(0,r.jsx)("button",{className:"w-full py-3 bg-white/95 backdrop-blur-sm text-text-primary font-semibold rounded-xl shadow-soft hover:bg-white transition-all duration-300",children:"Quick View"})})]}),(0,r.jsxs)("div",{className:"p-6 space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-text-primary group-hover:text-primary-600 transition-colors duration-300 line-clamp-2",children:c.title}),(0,r.jsx)("p",{className:"text-text-tertiary text-sm flex items-center",children:(0,r.jsxs)("span",{className:"line-clamp-1",children:[c.address,", ",c.city,", ",c.state]})})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("span",{className:"text-2xl font-bold text-gradient",children:(s=c.price,a=c.title,n=c.type,a.toLowerCase().includes("rent")||"PG"===n||a.toLowerCase().includes("pg")||a.toLowerCase().includes("paying guest")||s<1e5?"".concat(l(s),"/month"):l(s))}),(c.title.toLowerCase().includes("rent")||"PG"===c.type||c.title.toLowerCase().includes("pg")||c.price<1e5)&&(0,r.jsx)("p",{className:"text-xs text-text-tertiary",children:"per month"})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-sm text-text-tertiary",children:"Price per sqft"}),(0,r.jsxs)("div",{className:"text-lg font-semibold text-text-secondary",children:["₹",Math.round(c.price/c.area).toLocaleString("en-IN")]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 py-4 border-t border-gray-100",children:[c.bedrooms&&(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-1",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary-50 rounded-lg flex items-center justify-center"}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-sm font-semibold text-text-primary",children:c.bedrooms}),(0,r.jsx)("div",{className:"text-xs text-text-tertiary",children:"Beds"})]})]}),c.bathrooms&&(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-1",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary-50 rounded-lg flex items-center justify-center"}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-sm font-semibold text-text-primary",children:c.bathrooms}),(0,r.jsx)("div",{className:"text-xs text-text-tertiary",children:"Baths"})]})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-1",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary-50 rounded-lg flex items-center justify-center"}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-sm font-semibold text-text-primary",children:c.area}),(0,r.jsx)("div",{className:"text-xs text-text-tertiary",children:"sqft"})]})]})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(i(),{href:"PG"===c.type?"/pg/".concat(c.id):"/properties/".concat(c.id),className:"btn-primary flex-1 text-center",children:"View Details"}),(0,r.jsx)("button",{className:"px-4 py-3 bg-primary-50 text-primary-600 font-semibold rounded-xl hover:bg-primary-100 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:"Call"})]})]})]},c.id)}},5695:(e,t,s)=>{"use strict";var r=s(8999);s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},6583:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(5155),a=s(2115),i=s(5695),l=s(5494),n=s(6821),c=s(4659);function o(e){let{onFilterChange:t}=e,s=(0,i.useSearchParams)(),[l,n]=(0,a.useState)({city:"",priceRange:"",gender:"",roomType:"",amenities:[]}),[c,o]=(0,a.useState)({location:!0,price:!0,gender:!0,roomType:!0,amenities:!0}),d=e=>{o(t=>({...t,[e]:!t[e]}))};(0,a.useEffect)(()=>{let e=s.get("city")||"",t=s.get("priceRange")||"";n({city:e,priceRange:t,gender:s.get("gender")||"",roomType:s.get("roomType")||"",amenities:[]})},[s]),(0,a.useEffect)(()=>{t(l)},[l]);let m=e=>{n(t=>({...t,amenities:t.amenities.includes(e)?t.amenities.filter(t=>t!==e):[...t.amenities,e]}))};return(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-soft border border-gray-100 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-text-primary",children:"Filter PG Options"}),(0,r.jsx)("button",{onClick:()=>{n({city:"",priceRange:"",gender:"",roomType:"",amenities:[]})},className:"text-sm text-primary-600 hover:text-primary-700 font-medium",children:"Clear All"})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("button",{onClick:()=>d("location"),className:"flex items-center justify-between w-full text-left font-medium text-text-primary mb-3",children:[(0,r.jsx)("span",{children:"Location"}),(0,r.jsx)("svg",{className:"h-5 w-5 transform transition-transform ".concat(c.location?"rotate-180":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),c.location&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("input",{type:"text",placeholder:"Enter city or area",value:l.city,onChange:e=>n(t=>({...t,city:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:["Hyderabad","Gachibowli","Hitech City","Madhapur","Kondapur","Kukatpally"].map(e=>(0,r.jsx)("button",{onClick:()=>n(t=>({...t,city:e})),className:"px-3 py-1 text-sm rounded-full border transition-colors ".concat(l.city===e?"bg-primary-600 text-white border-primary-600":"bg-white text-text-secondary border-gray-300 hover:border-primary-600"),children:e},e))})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("button",{onClick:()=>d("price"),className:"flex items-center justify-between w-full text-left font-medium text-text-primary mb-3",children:[(0,r.jsx)("span",{children:"Monthly Rent"}),(0,r.jsx)("svg",{className:"h-5 w-5 transform transition-transform ".concat(c.price?"rotate-180":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),c.price&&(0,r.jsx)("div",{className:"space-y-2",children:[{value:"",label:"Any Budget"},{value:"0-5000",label:"Under ₹5,000"},{value:"5000-10000",label:"₹5,000 - ₹10,000"},{value:"10000-15000",label:"₹10,000 - ₹15,000"},{value:"15000-20000",label:"₹15,000 - ₹20,000"},{value:"20000-",label:"Above ₹20,000"}].map(e=>(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"radio",name:"priceRange",value:e.value,checked:l.priceRange===e.value,onChange:e=>n(t=>({...t,priceRange:e.target.value})),className:"form-radio h-4 w-4 text-primary-600"}),(0,r.jsx)("span",{className:"text-sm text-text-secondary",children:e.label})]},e.value))})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("button",{onClick:()=>d("gender"),className:"flex items-center justify-between w-full text-left font-medium text-text-primary mb-3",children:[(0,r.jsx)("span",{children:"Gender Preference"}),(0,r.jsx)("svg",{className:"h-5 w-5 transform transition-transform ".concat(c.gender?"rotate-180":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),c.gender&&(0,r.jsx)("div",{className:"space-y-2",children:[{value:"",label:"Any"},{value:"male",label:"Male Only"},{value:"female",label:"Female Only"},{value:"mixed",label:"Co-ed"}].map(e=>(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"radio",name:"gender",value:e.value,checked:l.gender===e.value,onChange:e=>n(t=>({...t,gender:e.target.value})),className:"form-radio h-4 w-4 text-primary-600"}),(0,r.jsx)("span",{className:"text-sm text-text-secondary",children:e.label})]},e.value))})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("button",{onClick:()=>d("roomType"),className:"flex items-center justify-between w-full text-left font-medium text-text-primary mb-3",children:[(0,r.jsx)("span",{children:"Room Type"}),(0,r.jsx)("svg",{className:"h-5 w-5 transform transition-transform ".concat(c.roomType?"rotate-180":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),c.roomType&&(0,r.jsx)("div",{className:"space-y-2",children:[{value:"",label:"Any Type"},{value:"single",label:"Single Occupancy"},{value:"double",label:"Double Sharing"},{value:"triple",label:"Triple Sharing"},{value:"dormitory",label:"Dormitory"}].map(e=>(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"radio",name:"roomType",value:e.value,checked:l.roomType===e.value,onChange:e=>n(t=>({...t,roomType:e.target.value})),className:"form-radio h-4 w-4 text-primary-600"}),(0,r.jsx)("span",{className:"text-sm text-text-secondary",children:e.label})]},e.value))})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("button",{onClick:()=>d("amenities"),className:"flex items-center justify-between w-full text-left font-medium text-text-primary mb-3",children:[(0,r.jsx)("span",{children:"Amenities"}),(0,r.jsx)("svg",{className:"h-5 w-5 transform transition-transform ".concat(c.amenities?"rotate-180":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),c.amenities&&(0,r.jsx)("div",{className:"space-y-2",children:["WiFi","AC","Food Included","Laundry","Parking","Security","Power Backup","Water Supply","Gym","Common Area","TV","Fridge"].map(e=>(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",checked:l.amenities.includes(e),onChange:()=>m(e),className:"form-checkbox h-4 w-4 text-primary-600 rounded"}),(0,r.jsx)("span",{className:"text-sm text-text-secondary",children:e})]},e))})]})]})}function d(){let e=(0,i.useSearchParams)(),[t,s]=(0,a.useState)([]),[l,n]=(0,a.useState)(!0),[d,m]=(0,a.useState)({currentPage:1,totalPages:1,totalItems:0,hasNext:!1,hasPrev:!1}),[x,h]=(0,a.useState)({city:"",priceRange:"",gender:"",roomType:"",amenities:[]}),u=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:x,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;n(!0);try{let r=Object.entries(e).reduce((e,t)=>{let[s,r]=t;return r&&"string"==typeof r&&""!==r.trim()&&(e[s]=r),e},{}),a=new URLSearchParams({page:t.toString(),limit:"12",type:"PG",...r}).toString(),i=await fetch("/api/properties?".concat(a));if(!i.ok)throw Error("HTTP error! status: ".concat(i.status));let l=await i.json();s(l.properties),m(l.pagination)}catch(e){console.error("Error loading PG properties:",e)}finally{n(!1)}};return(0,a.useEffect)(()=>{let t=e.get("city")||"",s=e.get("priceRange")||"",r={city:t,priceRange:s,gender:e.get("gender")||"",roomType:e.get("roomType")||"",amenities:[]};h(r),u(r)},[e]),(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("section",{className:"py-12",children:(0,r.jsx)("div",{className:"container-custom",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,r.jsx)("div",{className:"lg:w-1/4",children:(0,r.jsx)("div",{className:"sticky top-6",children:(0,r.jsx)(o,{onFilterChange:e=>{h(e),u(e,1)}})})}),(0,r.jsxs)("div",{className:"lg:w-3/4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-text-primary",children:"Available PG Accommodations"}),(0,r.jsxs)("p",{className:"text-text-secondary",children:[t.length," PG",1!==t.length?"s":""," found"]})]}),l?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"bg-gray-200 h-48 rounded-t-xl"}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-b-xl",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2"})]})]},t))}):t.length>0?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6",children:t.map(e=>(0,r.jsx)(c.y,{property:e},e.id))}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-text-primary mb-2",children:"No PG accommodations found"}),(0,r.jsx)("p",{className:"text-text-secondary mb-6",children:"Try adjusting your filters or check back later for new listings."}),(0,r.jsx)("button",{onClick:()=>h({city:"",priceRange:"",gender:"",roomType:"",amenities:[]}),className:"btn-primary",children:"Clear Filters"})]})]})]})})})})}function m(){return(0,r.jsxs)("main",{className:"min-h-screen bg-background",children:[(0,r.jsx)(l.Navbar,{}),(0,r.jsxs)("section",{className:"relative bg-gradient-to-br from-primary-600 via-primary-700 to-accent-600 text-white py-20",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,r.jsx)("div",{className:"relative container-custom",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,r.jsx)("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold mb-6",children:"Find Your Perfect PG"}),(0,r.jsx)("p",{className:"text-xl md:text-2xl mb-8 text-white/90",children:"Discover comfortable and affordable paying guest accommodations in Hyderabad"}),(0,r.jsxs)("div",{className:"flex flex-wrap justify-center gap-4 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2",children:[(0,r.jsx)("svg",{className:"h-5 w-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Verified PG Owners"]}),(0,r.jsxs)("div",{className:"flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2",children:[(0,r.jsx)("svg",{className:"h-5 w-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Safe & Secure"]}),(0,r.jsxs)("div",{className:"flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2",children:[(0,r.jsx)("svg",{className:"h-5 w-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"All Amenities"]})]})]})})]}),(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{className:"py-12 container-custom",children:(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)("div",{className:"animate-pulse w-full max-w-4xl",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded mb-4 w-1/3"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"bg-gray-200 h-48 rounded-t-xl"}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-b-xl",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2"})]})]},t))})]})})}),children:(0,r.jsx)(d,{})}),(0,r.jsx)(n.w,{})]})}},8775:(e,t,s)=>{Promise.resolve().then(s.bind(s,6583))}},e=>{var t=t=>e(e.s=t);e.O(0,[874,494,821,441,684,358],()=>t(8775)),_N_E=e.O()}]);