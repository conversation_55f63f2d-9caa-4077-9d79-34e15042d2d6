'use client';

import { useTestimonials } from '@/hooks/useTestimonials';
import { Testimonial } from './Testimonial';

const testimonials = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Home Buyer',
    image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=1374&auto=format&fit=crop',
    quote: 'Working with RealEstate was an absolute pleasure. They helped me find my dream home in just a few weeks. The team was professional, knowledgeable, and always available to answer my questions. I couldn\'t be happier with my new home!',
    rating: 5
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Property Seller',
    image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=1374&auto=format&fit=crop',
    quote: 'I was impressed by how quickly RealEstate sold my property. Their marketing strategy was effective, and they managed to get me a better price than I expected. The entire process was smooth and stress-free. I highly recommend their services!',
    rating: 5
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Apartment Renter',
    image: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?q=80&w=1522&auto=format&fit=crop',
    quote: 'As someone new to the city, finding an apartment was daunting. RealEstate made it easy! They listened to my needs and showed me options that fit my budget and preferences. I found a great place in a neighborhood I love. Thank you!',
    rating: 4
  },
  {
    id: 4,
    name: 'David Wilson',
    role: 'Investment Property Buyer',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=1374&auto=format&fit=crop',
    quote: 'RealEstate\'s expertise in the investment property market is unmatched. They helped me identify properties with great ROI potential and guided me through the purchasing process. Their advice has been invaluable for building my real estate portfolio.',
    rating: 5
  },
];

export function TestimonialsSection() {
  const { 
    activeIndex, 
    activeTestimonial, 
    nextTestimonial, 
    prevTestimonial, 
    goToTestimonial 
  } = useTestimonials(testimonials);

  return (
    <div className="relative overflow-hidden py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Title */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">What Our Clients Say</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">Hear from our satisfied clients about their experience working with our real estate professionals.</p>
        </div>
        
        <div className="max-w-4xl mx-auto">
          {/* Testimonial Card */}
          <Testimonial 
            name={activeTestimonial.name}
            role={activeTestimonial.role}
            image={activeTestimonial.image}
            quote={activeTestimonial.quote}
            rating={activeTestimonial.rating}
          />

          {/* Navigation Buttons */}
          <div className="flex justify-center mt-8 space-x-4">
            <button
              onClick={prevTestimonial}
              className="w-12 h-12 rounded-full bg-white shadow-md flex items-center justify-center text-gray-700 hover:text-primary hover:shadow-lg transition-all duration-300"
              aria-label="Previous testimonial"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button
              onClick={nextTestimonial}
              className="w-12 h-12 rounded-full bg-white shadow-md flex items-center justify-center text-gray-700 hover:text-primary hover:shadow-lg transition-all duration-300"
              aria-label="Next testimonial"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* Indicators */}
          <div className="flex justify-center mt-6 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToTestimonial(index)}
                className={`w-3 h-3 rounded-full transition-colors duration-300 ${index === activeIndex ? 'bg-primary' : 'bg-gray-300'}`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}