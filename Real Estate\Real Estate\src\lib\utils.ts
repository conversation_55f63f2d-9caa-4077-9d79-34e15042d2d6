/**
 * Format currency in Indian Rupees
 * @param amount - The amount to format
 * @param currency - The currency code (default: 'INR')
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number, currency: string = 'INR'): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount).replace('₹', '₹');
}

/**
 * Format currency for display with proper Indian number formatting
 * @param amount - The amount to format
 * @param showSymbol - Whether to show the currency symbol (default: true)
 * @returns Formatted currency string
 */
export function formatIndianCurrency(amount: number, showSymbol: boolean = true): string {
  const formatted = new Intl.NumberFormat('en-IN', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
  
  return showSymbol ? `₹${formatted}` : formatted;
}

/**
 * Convert amount to lakhs/crores format commonly used in India
 * @param amount - The amount to convert
 * @param showSymbol - Whether to show the currency symbol (default: true)
 * @returns Formatted string in lakhs/crores
 */
export function formatIndianAmount(amount: number, showSymbol: boolean = true): string {
  const symbol = showSymbol ? '₹' : '';
  
  if (amount >= 10000000) { // 1 crore
    const crores = amount / 10000000;
    return `${symbol}${crores.toFixed(crores % 1 === 0 ? 0 : 1)} Cr`;
  } else if (amount >= 100000) { // 1 lakh
    const lakhs = amount / 100000;
    return `${symbol}${lakhs.toFixed(lakhs % 1 === 0 ? 0 : 1)} L`;
  } else if (amount >= 1000) { // 1 thousand
    const thousands = amount / 1000;
    return `${symbol}${thousands.toFixed(thousands % 1 === 0 ? 0 : 1)}K`;
  } else {
    return `${symbol}${amount.toLocaleString('en-IN')}`;
  }
}

/**
 * Parse JSON string safely
 * @param jsonString - The JSON string to parse
 * @returns Parsed array or empty array if parsing fails
 */
export function parseJsonSafely<T = any>(jsonString: string): T[] {
  try {
    return JSON.parse(jsonString);
  } catch {
    return [];
  }
}

/**
 * Check if a date is within the last N days
 * @param date - The date to check
 * @param days - Number of days to check against (default: 7)
 * @returns Boolean indicating if the date is recent
 */
export function isRecentDate(date: Date, days: number = 7): boolean {
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays <= days;
}

/**
 * Truncate text to specified length
 * @param text - The text to truncate
 * @param length - Maximum length (default: 100)
 * @returns Truncated text with ellipsis if needed
 */
export function truncateText(text: string, length: number = 100): string {
  if (text.length <= length) return text;
  return text.substring(0, length).trim() + '...';
}

/**
 * Generate a slug from a title
 * @param title - The title to convert to slug
 * @returns URL-friendly slug
 */
export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim();
}
