<?php
/**
 * Debug User Dashboard Issues
 * Tests user authentication and dashboard data after property listing
 */
?>
<!DOCTYPE html>
<html>
<head>
    <title>🐛 Debug User Dashboard - housing.okayy.in</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 20px auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .test-item { padding: 10px; margin: 5px 0; background: #f8f9fa; border-radius: 5px; }
        .btn { background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; max-height: 300px; }
        .json-output { background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 Debug User Dashboard Issues</h1>
        <p><strong>Purpose:</strong> Debug client-side exceptions in user dashboard after property listing</p>
        
        <?php
        // Test 1: Database Connection with New Credentials
        echo "<div class='test-section'>";
        echo "<h2>1. 🔗 Database Connection (Updated Credentials)</h2>";
        
        try {
            require_once 'php-backend/config/database.php';
            $database = new Database();
            $db = $database->getConnection();
            echo "<div class='test-item'><span class='success'>✅ Database connection: Working with u357173570_housingokayy</span></div>";
            
            // Test users table
            $stmt = $db->query("SELECT COUNT(*) as count FROM users");
            $result = $stmt->fetch();
            $userCount = $result['count'];
            echo "<div class='test-item'><span class='info'>👥 Total users: $userCount</span></div>";
            
            // Test properties table
            $stmt = $db->query("SELECT COUNT(*) as count FROM properties");
            $result = $stmt->fetch();
            $propertyCount = $result['count'];
            echo "<div class='test-item'><span class='info'>🏠 Total properties: $propertyCount</span></div>";
            
        } catch (Exception $e) {
            echo "<div class='test-item'><span class='error'>❌ Database error: " . $e->getMessage() . "</span></div>";
        }
        echo "</div>";
        
        // Test 2: User API Endpoints
        echo "<div class='test-section'>";
        echo "<h2>2. 🔌 User API Response Testing</h2>";
        
        // Test user properties endpoint
        echo "<div class='test-item'>";
        echo "<strong>Testing /php-backend/api/user/properties.php</strong><br>";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://' . $_SERVER['HTTP_HOST'] . '/php-backend/api/user/properties.php');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "HTTP Status: $httpCode<br>";
        
        if ($httpCode == 200) {
            echo "<span class='success'>✅ API accessible</span><br>";
            $jsonData = json_decode($response, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                echo "<span class='success'>✅ Valid JSON response</span><br>";
                echo "<div class='json-output'>";
                echo "<strong>Response structure:</strong><br>";
                echo "<pre>" . json_encode($jsonData, JSON_PRETTY_PRINT) . "</pre>";
                echo "</div>";
            } else {
                echo "<span class='error'>❌ Invalid JSON: " . json_last_error_msg() . "</span><br>";
                echo "<div class='json-output'>";
                echo "<strong>Raw response:</strong><br>";
                echo "<pre>" . htmlspecialchars($response) . "</pre>";
                echo "</div>";
            }
        } elseif ($httpCode == 401) {
            echo "<span class='info'>🔒 Authentication required (expected for logged-out user)</span><br>";
        } else {
            echo "<span class='error'>❌ HTTP Error: $httpCode</span><br>";
        }
        echo "</div>";
        
        // Test user inquiries endpoint
        echo "<div class='test-item'>";
        echo "<strong>Testing /php-backend/api/user/inquiries.php</strong><br>";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://' . $_SERVER['HTTP_HOST'] . '/php-backend/api/user/inquiries.php');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "HTTP Status: $httpCode<br>";
        
        if ($httpCode == 200) {
            echo "<span class='success'>✅ API accessible</span><br>";
        } elseif ($httpCode == 401) {
            echo "<span class='info'>🔒 Authentication required (expected)</span><br>";
        } else {
            echo "<span class='error'>❌ HTTP Error: $httpCode</span><br>";
        }
        echo "</div>";
        echo "</div>";
        
        // Test 3: Sample Property Data Structure
        echo "<div class='test-section'>";
        echo "<h2>3. 🏠 Property Data Structure Analysis</h2>";
        
        try {
            // Get a sample property to check data structure
            $stmt = $db->query("SELECT * FROM properties LIMIT 1");
            $sampleProperty = $stmt->fetch();
            
            if ($sampleProperty) {
                echo "<div class='test-item'><span class='success'>✅ Sample property found</span></div>";
                echo "<div class='json-output'>";
                echo "<strong>Property fields available:</strong><br>";
                echo "<pre>";
                foreach ($sampleProperty as $key => $value) {
                    $type = gettype($value);
                    $preview = is_string($value) && strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value;
                    echo "$key: ($type) " . htmlspecialchars($preview) . "\n";
                }
                echo "</pre>";
                echo "</div>";
                
                // Check for potential problematic fields
                echo "<div class='test-item'>";
                echo "<strong>Potential Issues Check:</strong><br>";
                
                // Check images field
                if (isset($sampleProperty['images'])) {
                    $images = json_decode($sampleProperty['images'], true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        echo "✅ Images field: Valid JSON (" . count($images) . " images)<br>";
                    } else {
                        echo "❌ Images field: Invalid JSON - " . json_last_error_msg() . "<br>";
                    }
                } else {
                    echo "⚠️ Images field: Missing<br>";
                }
                
                // Check amenities field
                if (isset($sampleProperty['amenities'])) {
                    $amenities = json_decode($sampleProperty['amenities'], true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        echo "✅ Amenities field: Valid JSON<br>";
                    } else {
                        echo "❌ Amenities field: Invalid JSON - " . json_last_error_msg() . "<br>";
                    }
                } else {
                    echo "⚠️ Amenities field: Missing<br>";
                }
                
                // Check required fields
                $requiredFields = ['id', 'title', 'price', 'type', 'approval_status'];
                foreach ($requiredFields as $field) {
                    if (isset($sampleProperty[$field])) {
                        echo "✅ $field: Present<br>";
                    } else {
                        echo "❌ $field: Missing<br>";
                    }
                }
                echo "</div>";
                
            } else {
                echo "<div class='test-item'><span class='warning'>⚠️ No properties found in database</span></div>";
            }
        } catch (Exception $e) {
            echo "<div class='test-item'><span class='error'>❌ Error: " . $e->getMessage() . "</span></div>";
        }
        echo "</div>";
        
        // Test 4: JavaScript Error Simulation
        echo "<div class='test-section'>";
        echo "<h2>4. 🐛 JavaScript Error Detection</h2>";
        echo "<div id='js-errors'></div>";
        echo "<div class='test-item'><span class='info'>ℹ️ JavaScript errors will appear above (if any)</span></div>";
        echo "</div>";
        ?>
        
        <!-- JavaScript Error Catcher and API Tester -->
        <script>
            let errorCount = 0;
            const errorContainer = document.getElementById('js-errors');
            
            // Catch JavaScript errors
            window.addEventListener('error', function(e) {
                errorCount++;
                const errorDiv = document.createElement('div');
                errorDiv.className = 'test-item';
                errorDiv.innerHTML = `<span class="error">❌ JS Error ${errorCount}: ${e.message} at ${e.filename}:${e.lineno}</span>`;
                errorContainer.appendChild(errorDiv);
            });
            
            // Catch unhandled promise rejections
            window.addEventListener('unhandledrejection', function(e) {
                errorCount++;
                const errorDiv = document.createElement('div');
                errorDiv.className = 'test-item';
                errorDiv.innerHTML = `<span class="error">❌ Promise Rejection ${errorCount}: ${e.reason}</span>`;
                errorContainer.appendChild(errorDiv);
            });
            
            // Test API calls that might cause dashboard errors
            async function testDashboardAPIs() {
                const apiTests = [
                    { name: 'User Properties', url: '/php-backend/api/user/properties.php' },
                    { name: 'User Inquiries', url: '/php-backend/api/user/inquiries.php' },
                    { name: 'Check Session', url: '/php-backend/api/auth/check-session.php' }
                ];
                
                for (const test of apiTests) {
                    try {
                        console.log(`Testing ${test.name}...`);
                        const response = await fetch(test.url);
                        const data = await response.text();
                        
                        // Try to parse as JSON
                        try {
                            const jsonData = JSON.parse(data);
                            console.log(`✅ ${test.name}: Valid JSON response`, jsonData);
                        } catch (jsonError) {
                            console.warn(`⚠️ ${test.name}: Non-JSON response`, data);
                        }
                        
                    } catch (error) {
                        console.error(`❌ ${test.name}: ${error.message}`);
                        errorCount++;
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'test-item';
                        errorDiv.innerHTML = `<span class="error">❌ API Error: ${test.name} - ${error.message}</span>`;
                        errorContainer.appendChild(errorDiv);
                    }
                }
                
                // Show success message if no errors
                if (errorCount === 0) {
                    setTimeout(() => {
                        if (errorCount === 0) {
                            const successDiv = document.createElement('div');
                            successDiv.className = 'test-item';
                            successDiv.innerHTML = `<span class="success">✅ No JavaScript errors detected in API tests!</span>`;
                            errorContainer.appendChild(successDiv);
                        }
                    }, 2000);
                }
            }
            
            // Run tests when page loads
            document.addEventListener('DOMContentLoaded', testDashboardAPIs);
        </script>
        
        <div class="test-section">
            <h2>5. 🔧 Recommended Fixes</h2>
            <div class="test-item">
                <strong>Based on common dashboard errors:</strong>
                <ol>
                    <li>✅ <strong>Enhanced error handling</strong> - Added try-catch for JSON parsing</li>
                    <li>✅ <strong>Fallback values</strong> - Added default values for missing fields</li>
                    <li>✅ <strong>Image error handling</strong> - Added onError handlers for images</li>
                    <li>✅ <strong>Type safety</strong> - Added null checks for all property fields</li>
                    <li>✅ <strong>Database credentials</strong> - Updated to u357173570_housingokayy</li>
                </ol>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="/dashboard" class="btn">📊 Test User Dashboard</a>
            <a href="/login" class="btn">👤 User Login</a>
            <a href="/properties/create" class="btn">➕ Create Property</a>
        </div>
        
        <div class="test-section">
            <h2>🗑️ Cleanup</h2>
            <p><strong>After testing, delete this file:</strong> <code>debug-user-dashboard.php</code></p>
        </div>
    </div>
</body>
</html>
