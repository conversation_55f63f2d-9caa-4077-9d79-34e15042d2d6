<?php
/**
 * Simple Debug Script for housing.okayy.in
 * Copy and paste this entire content into a new file called debug.php on your server
 */
?>
<!DOCTYPE html>
<html>
<head>
    <title>Simple Debug - housing.okayy.in</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .info { color: #007bff; font-weight: bold; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .test-btn { background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px; display: inline-block; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Simple Debug for housing.okayy.in</h1>
        
        <!-- PHP Basic Info -->
        <div class="section">
            <h2>1. PHP Configuration</h2>
            <?php
            echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
            echo "<p><strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
            echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";
            echo "<p><strong>Current File:</strong> " . __FILE__ . "</p>";
            echo "<p><strong>Server Name:</strong> " . ($_SERVER['SERVER_NAME'] ?? 'Unknown') . "</p>";
            ?>
        </div>

        <!-- File Structure Check -->
        <div class="section">
            <h2>2. File Structure Check</h2>
            <?php
            $checkFiles = [
                'php-backend directory' => 'php-backend',
                'database config' => 'php-backend/config/database.php',
                'auth login' => 'php-backend/api/auth/login.php',
                'properties api' => 'php-backend/api/properties/index.php',
                'uploads directory' => 'php-backend/uploads'
            ];
            
            foreach ($checkFiles as $name => $path) {
                $fullPath = $_SERVER['DOCUMENT_ROOT'] . '/' . $path;
                echo "<p><strong>$name:</strong> ";
                
                if (file_exists($fullPath)) {
                    if (is_dir($fullPath)) {
                        echo "<span class='success'>✅ Directory exists</span>";
                    } else {
                        echo "<span class='success'>✅ File exists</span>";
                    }
                } else {
                    echo "<span class='error'>❌ Missing: $fullPath</span>";
                }
                echo "</p>";
            }
            ?>
        </div>

        <!-- Database Connection Test -->
        <div class="section">
            <h2>3. Database Connection Test</h2>
            <?php
            try {
                // Try to include database config
                $dbConfigPath = $_SERVER['DOCUMENT_ROOT'] . '/php-backend/config/database.php';
                
                if (file_exists($dbConfigPath)) {
                    echo "<p class='info'>📁 Database config file found</p>";
                    
                    require_once $dbConfigPath;
                    
                    if (class_exists('Database')) {
                        echo "<p class='success'>✅ Database class loaded</p>";
                        
                        $database = new Database();
                        $db = $database->getConnection();
                        
                        if ($db) {
                            echo "<p class='success'>✅ Database connection successful!</p>";
                            
                            // Test if users table exists
                            try {
                                $stmt = $db->query("SHOW TABLES LIKE 'users'");
                                if ($stmt && $stmt->rowCount() > 0) {
                                    echo "<p class='success'>✅ Users table exists</p>";
                                    
                                    // Count users
                                    $stmt = $db->query("SELECT COUNT(*) as count FROM users");
                                    $result = $stmt->fetch();
                                    echo "<p class='info'>👥 Total users: " . ($result['count'] ?? 0) . "</p>";
                                    
                                    // Check for admin users
                                    $stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE role = 'ADMIN'");
                                    $result = $stmt->fetch();
                                    echo "<p class='info'>👑 Admin users: " . ($result['count'] ?? 0) . "</p>";
                                    
                                } else {
                                    echo "<p class='error'>❌ Users table does not exist</p>";
                                }
                            } catch (Exception $e) {
                                echo "<p class='error'>❌ Table check failed: " . $e->getMessage() . "</p>";
                            }
                        } else {
                            echo "<p class='error'>❌ Database connection failed</p>";
                        }
                    } else {
                        echo "<p class='error'>❌ Database class not found</p>";
                    }
                } else {
                    echo "<p class='error'>❌ Database config file not found: $dbConfigPath</p>";
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>

        <!-- API Endpoint Tests -->
        <div class="section">
            <h2>4. API Endpoint Tests</h2>
            <?php
            $baseUrl = 'https://' . $_SERVER['HTTP_HOST'];
            $endpoints = [
                'Check Session' => '/php-backend/api/auth/check-session.php',
                'Login' => '/php-backend/api/auth/login.php',
                'Properties' => '/php-backend/api/properties/index.php',
                'Contact' => '/php-backend/api/contact/index.php'
            ];
            
            foreach ($endpoints as $name => $endpoint) {
                $fullUrl = $baseUrl . $endpoint;
                $filePath = $_SERVER['DOCUMENT_ROOT'] . $endpoint;
                
                echo "<p><strong>$name:</strong> ";
                
                if (file_exists($filePath)) {
                    echo "<span class='success'>✅ File exists</span> - ";
                    echo "<a href='$fullUrl' target='_blank' class='test-btn'>Test</a>";
                } else {
                    echo "<span class='error'>❌ File missing</span>";
                }
                echo "</p>";
            }
            ?>
        </div>

        <!-- Quick Actions -->
        <div class="section">
            <h2>5. Quick Actions</h2>
            <p><strong>Test your main site:</strong> <a href="/" target="_blank" class="test-btn">Visit Homepage</a></p>
            <p><strong>Test admin login:</strong> <a href="/admin/login" target="_blank" class="test-btn">Admin Login</a></p>
            <p><strong>Reset admin password:</strong> <a href="/reset-admin-production.php" target="_blank" class="test-btn">Reset Admin</a></p>
        </div>

        <!-- Instructions -->
        <div class="section">
            <h2>6. Next Steps</h2>
            <ol>
                <li>If files are missing, upload them to the correct directories</li>
                <li>If database connection fails, check credentials in <code>php-backend/config/database.php</code></li>
                <li>If admin users = 0, run the admin reset script</li>
                <li>Test the API endpoints by clicking the "Test" buttons above</li>
                <li><strong>Delete this debug file when done!</strong></li>
            </ol>
        </div>
    </div>
</body>
</html>
