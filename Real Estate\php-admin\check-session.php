<?php
session_start();

// Add CORS headers
header('Access-Control-Allow-Origin: http://localhost:3000');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    echo json_encode([
        'user' => [
            'id' => $_SESSION['admin_id'],
            'role' => 'ADMIN',
            'name' => $_SESSION['admin_name'] ?? 'Admin'
        ]
    ]);
} else {
    echo json_encode(['user' => null]);
}
?>