<?php
require_once '../../config/database.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendError('Method not allowed', 405);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $slug = $_GET['slug'] ?? '';
    if (!$slug) {
        sendError('Blog post slug is required', 400);
    }
    
    // Get blog post by slug
    $query = "SELECT bp.*, u.name as author_name 
              FROM blog_posts bp 
              LEFT JOIN users u ON bp.author_id = u.id 
              WHERE bp.slug = :slug AND bp.published = 1";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':slug', $slug);
    $stmt->execute();
    
    $post = $stmt->fetch();
    
    if (!$post) {
        sendError('Blog post not found', 404);
    }
    
    // Process post data
    $post['tags'] = json_decode($post['tags'], true) ?: [];
    $post['author'] = [
        'name' => $post['author_name'] ?: 'Admin'
    ];
    unset($post['author_name'], $post['author_id']);
    
    sendResponse($post);
    
} catch (Exception $e) {
    error_log("Blog get error: " . $e->getMessage());
    sendError('Failed to fetch blog post', 500);
}
?>
