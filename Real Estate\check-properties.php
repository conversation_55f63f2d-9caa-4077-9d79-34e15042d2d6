<?php
try {
    $db = new PDO('sqlite:Real Estate/prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>Properties in Database</h1>";
    
    $properties = $db->query("SELECT id, title, price, type, city, approvalStatus, isApproved, ownerId, createdAt FROM Property ORDER BY createdAt DESC")->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($properties)) {
        echo "<p>No properties found in database.</p>";
        
        // Create a test property
        $stmt = $db->prepare("INSERT INTO Property (id, title, description, price, currency, type, listingType, address, city, state, pincode, images, amenities, ownerId, approvalStatus, isApproved) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        $propertyId = 'test-prop-' . uniqid();
        $stmt->execute([
            $propertyId,
            'Test Property',
            'A beautiful test property for demonstration',
            500000,
            'INR',
            'APARTMENT',
            'SALE',
            '123 Test Street',
            'Mumbai',
            'Maharashtra',
            '400001',
            '[]',
            '[]',
            'test-user-id',
            'PENDING',
            0
        ]);
        
        echo "<p>Created test property with ID: $propertyId</p>";
        
        // Fetch again to show the created property
        $properties = $db->query("SELECT id, title, price, type, city, approvalStatus, isApproved, ownerId, createdAt FROM Property ORDER BY createdAt DESC")->fetchAll(PDO::FETCH_ASSOC);
    }
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th style='padding: 8px;'>ID</th>";
    echo "<th style='padding: 8px;'>Title</th>";
    echo "<th style='padding: 8px;'>Price</th>";
    echo "<th style='padding: 8px;'>Type</th>";
    echo "<th style='padding: 8px;'>City</th>";
    echo "<th style='padding: 8px;'>Approval Status</th>";
    echo "<th style='padding: 8px;'>Is Approved</th>";
    echo "<th style='padding: 8px;'>Owner ID</th>";
    echo "<th style='padding: 8px;'>Created At</th>";
    echo "<th style='padding: 8px;'>Actions</th>";
    echo "</tr>";
    
    foreach ($properties as $property) {
        $statusColor = $property['approvalStatus'] === 'APPROVED' ? 'green' : 
                      ($property['approvalStatus'] === 'REJECTED' ? 'red' : 'orange');
        
        echo "<tr>";
        echo "<td style='padding: 8px; font-size: 12px;'>" . htmlspecialchars(substr($property['id'], 0, 10)) . "...</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($property['title']) . "</td>";
        echo "<td style='padding: 8px;'>₹" . number_format($property['price']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($property['type']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($property['city']) . "</td>";
        echo "<td style='padding: 8px; color: $statusColor; font-weight: bold;'>" . htmlspecialchars($property['approvalStatus']) . "</td>";
        echo "<td style='padding: 8px;'>" . ($property['isApproved'] ? 'Yes' : 'No') . "</td>";
        echo "<td style='padding: 8px; font-size: 12px;'>" . htmlspecialchars(substr($property['ownerId'], 0, 10)) . "...</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($property['createdAt']) . "</td>";
        echo "<td style='padding: 8px;'>";
        if ($property['approvalStatus'] === 'PENDING') {
            echo "<a href='?approve=" . $property['id'] . "' style='color: green; margin-right: 10px;'>Approve</a>";
            echo "<a href='?reject=" . $property['id'] . "' style='color: red;'>Reject</a>";
        }
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Handle approval/rejection
    if (isset($_GET['approve'])) {
        $propertyId = $_GET['approve'];
        $stmt = $db->prepare("UPDATE Property SET approvalStatus = 'APPROVED', isApproved = 1, approvedAt = CURRENT_TIMESTAMP, approvedBy = 'admin' WHERE id = ?");
        $stmt->execute([$propertyId]);
        echo "<script>alert('Property approved!'); window.location.href = window.location.pathname;</script>";
    }
    
    if (isset($_GET['reject'])) {
        $propertyId = $_GET['reject'];
        $stmt = $db->prepare("UPDATE Property SET approvalStatus = 'REJECTED', isApproved = 0, rejectionReason = 'Rejected via test page' WHERE id = ?");
        $stmt->execute([$propertyId]);
        echo "<script>alert('Property rejected!'); window.location.href = window.location.pathname;</script>";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage();
}
?>