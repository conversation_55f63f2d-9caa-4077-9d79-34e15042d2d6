"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[604],{9604:(e,r,a)=>{a.d(r,{f:()=>n});var s=a(5155),l=a(2115);function n(e){let{onFilterChange:r}=e,[a,n]=(0,l.useState)({type:"",minPrice:"",maxPrice:"",bedrooms:"",city:"",roomType:"",sharing:"",foodIncluded:"",gender:""});(0,l.useEffect)(()=>{{let e=new URLSearchParams(window.location.search),a={type:e.get("type")||"",minPrice:e.get("minPrice")||"",maxPrice:e.get("maxPrice")||"",bedrooms:e.get("bedrooms")||"",city:e.get("city")||"",roomType:e.get("roomType")||"",sharing:e.get("sharing")||"",foodIncluded:e.get("foodIncluded")||"",gender:e.get("gender")||""};n(a),r(a)}},[r]);let t=(e,s)=>{console.log("Filter change:",e,"=",s);let l={...a,[e]:s};console.log("New filters:",l),n(l),r(l)},[o,c]=(0,l.useState)({propertyType:!0,price:!0,bedrooms:!0,bathrooms:!0,amenities:!0,roomType:!0,sharing:!0,foodIncluded:!0,gender:!0}),i=e=>{c(r=>({...r,[e]:!r[e]}))};return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-bold mb-6",children:"Filters"}),(0,s.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-6",children:[(0,s.jsx)("div",{className:"flex justify-between items-center cursor-pointer",onClick:()=>i("propertyType"),children:(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Property Type"})}),o.propertyType&&(0,s.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"propertyType",value:"",checked:""===a.type,onChange:e=>r({...a,type:e.target.value}),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"All Types"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"propertyType",value:"HOUSE",checked:"HOUSE"===a.type,onChange:e=>t("type",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"House"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"propertyType",value:"APARTMENT",checked:"APARTMENT"===a.type,onChange:e=>t("type",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Apartment"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"propertyType",value:"VILLA",checked:"VILLA"===a.type,onChange:e=>t("type",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Villa"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"propertyType",value:"PLOT",checked:"PLOT"===a.type,onChange:e=>t("type",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Plot"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"propertyType",value:"COMMERCIAL",checked:"COMMERCIAL"===a.type,onChange:e=>t("type",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Commercial"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"propertyType",value:"OFFICE",checked:"OFFICE"===a.type,onChange:e=>t("type",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Office"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"propertyType",value:"PG",checked:"PG"===a.type,onChange:e=>t("type",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"PG (Paying Guest)"})]})]})]}),(0,s.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-6",children:[(0,s.jsx)("div",{className:"flex justify-between items-center cursor-pointer",onClick:()=>i("price"),children:(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Price Range"})}),o.price&&(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsxs)("div",{className:"flex justify-between mt-4 space-x-4",children:[(0,s.jsxs)("div",{className:"w-1/2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Min Price"}),(0,s.jsxs)("select",{className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",value:a.minPrice,onChange:e=>t("minPrice",e.target.value),children:[(0,s.jsx)("option",{value:"",children:"No Min"}),(0,s.jsx)("option",{value:"500000",children:"₹5 Lakh"}),(0,s.jsx)("option",{value:"1000000",children:"₹10 Lakh"}),(0,s.jsx)("option",{value:"2000000",children:"₹20 Lakh"}),(0,s.jsx)("option",{value:"3000000",children:"₹30 Lakh"}),(0,s.jsx)("option",{value:"5000000",children:"₹50 Lakh"}),(0,s.jsx)("option",{value:"7500000",children:"₹75 Lakh"}),(0,s.jsx)("option",{value:"10000000",children:"₹1 Crore"}),(0,s.jsx)("option",{value:"20000000",children:"₹2 Crore"})]})]}),(0,s.jsxs)("div",{className:"w-1/2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Max Price"}),(0,s.jsxs)("select",{className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",value:a.maxPrice,onChange:e=>t("maxPrice",e.target.value),children:[(0,s.jsx)("option",{value:"",children:"No Max"}),(0,s.jsx)("option",{value:"1000000",children:"₹10 Lakh"}),(0,s.jsx)("option",{value:"2000000",children:"₹20 Lakh"}),(0,s.jsx)("option",{value:"3000000",children:"₹30 Lakh"}),(0,s.jsx)("option",{value:"5000000",children:"₹50 Lakh"}),(0,s.jsx)("option",{value:"7500000",children:"₹75 Lakh"}),(0,s.jsx)("option",{value:"10000000",children:"₹1 Crore"}),(0,s.jsx)("option",{value:"20000000",children:"₹2 Crore"}),(0,s.jsx)("option",{value:"50000000",children:"₹5 Crore"})]})]})]})})]}),(0,s.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-6",children:[(0,s.jsx)("div",{className:"flex justify-between items-center cursor-pointer",onClick:()=>i("bedrooms"),children:(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Bedrooms"})}),o.bedrooms&&(0,s.jsx)("div",{className:"mt-4 flex flex-wrap gap-2",children:["","1","2","3","4","5"].map(e=>(0,s.jsx)("button",{onClick:()=>t("bedrooms",e),className:"px-4 py-2 border rounded-md transition-colors ".concat(a.bedrooms===e?"bg-primary text-white border-primary":"border-gray-300 hover:bg-primary hover:text-white hover:border-primary"),children:""===e?"Any":"".concat(e,"+")},e))})]}),(0,s.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-6",children:[(0,s.jsx)("div",{className:"flex justify-between items-center cursor-pointer",onClick:()=>i("bathrooms"),children:(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Bathrooms"})}),o.bathrooms&&(0,s.jsxs)("div",{className:"mt-4 flex flex-wrap gap-2",children:[(0,s.jsx)("button",{className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors",children:"Any"}),(0,s.jsx)("button",{className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors",children:"1+"}),(0,s.jsx)("button",{className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors",children:"2+"}),(0,s.jsx)("button",{className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors",children:"3+"}),(0,s.jsx)("button",{className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors",children:"4+"})]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("div",{className:"flex justify-between items-center cursor-pointer",onClick:()=>i("amenities"),children:(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Amenities"})}),o.amenities&&(0,s.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",className:"form-checkbox h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Air Conditioning"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",className:"form-checkbox h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Swimming Pool"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",className:"form-checkbox h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Gym"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",className:"form-checkbox h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Balcony"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",className:"form-checkbox h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Parking"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",className:"form-checkbox h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Furnished"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",className:"form-checkbox h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Pet Friendly"})]})]})]}),(0,s.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"City"}),(0,s.jsx)("input",{type:"text",placeholder:"Enter city name",value:a.city,onChange:e=>t("city",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"})]}),"PG"===a.type&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-6",children:[(0,s.jsx)("div",{className:"flex justify-between items-center cursor-pointer",onClick:()=>i("roomType"),children:(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Room Type"})}),o.roomType&&(0,s.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"roomType",value:"",checked:""===a.roomType,onChange:e=>t("roomType",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Any"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"roomType",value:"SINGLE",checked:"SINGLE"===a.roomType,onChange:e=>t("roomType",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Single Room"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"roomType",value:"DOUBLE",checked:"DOUBLE"===a.roomType,onChange:e=>t("roomType",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Double Room"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"roomType",value:"TRIPLE",checked:"TRIPLE"===a.roomType,onChange:e=>t("roomType",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Triple Sharing"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"roomType",value:"FOUR_SHARING",checked:"FOUR_SHARING"===a.roomType,onChange:e=>t("roomType",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Four Sharing"})]})]})]}),(0,s.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-6",children:[(0,s.jsx)("div",{className:"flex justify-between items-center cursor-pointer",onClick:()=>i("sharing"),children:(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Sharing"})}),o.sharing&&(0,s.jsx)("div",{className:"mt-4 flex flex-wrap gap-2",children:["","1","2","3","4+"].map(e=>(0,s.jsx)("button",{onClick:()=>t("sharing",e),className:"px-4 py-2 border rounded-md transition-colors ".concat(a.sharing===e?"bg-primary text-white border-primary":"border-gray-300 hover:bg-primary hover:text-white hover:border-primary"),children:""===e?"Any":"1"===e?"Single":"".concat(e," Sharing")},e))})]}),(0,s.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-6",children:[(0,s.jsx)("div",{className:"flex justify-between items-center cursor-pointer",onClick:()=>i("foodIncluded"),children:(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Food"})}),o.foodIncluded&&(0,s.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"foodIncluded",value:"",checked:""===a.foodIncluded,onChange:e=>t("foodIncluded",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Any"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"foodIncluded",value:"YES",checked:"YES"===a.foodIncluded,onChange:e=>t("foodIncluded",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Food Included"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"foodIncluded",value:"NO",checked:"NO"===a.foodIncluded,onChange:e=>t("foodIncluded",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"No Food"})]})]})]}),(0,s.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-6",children:[(0,s.jsx)("div",{className:"flex justify-between items-center cursor-pointer",onClick:()=>i("gender"),children:(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Gender Preference"})}),o.gender&&(0,s.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"gender",value:"",checked:""===a.gender,onChange:e=>t("gender",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Any"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"gender",value:"MALE",checked:"MALE"===a.gender,onChange:e=>t("gender",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Male Only"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"gender",value:"FEMALE",checked:"FEMALE"===a.gender,onChange:e=>t("gender",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Female Only"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",name:"gender",value:"MIXED",checked:"MIXED"===a.gender,onChange:e=>t("gender",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Mixed"})]})]})]})]}),(0,s.jsx)("button",{onClick:()=>{let e={type:"",minPrice:"",maxPrice:"",bedrooms:"",city:"",roomType:"",sharing:"",foodIncluded:"",gender:""};n(e),r(e)},className:"btn-secondary w-full",children:"Reset Filters"})]})}}}]);