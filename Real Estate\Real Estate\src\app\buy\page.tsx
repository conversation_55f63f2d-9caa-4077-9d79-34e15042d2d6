'use client'

import { useState, useEffect, useCallback } from 'react';
import { Navbar } from '@/components/Navbar';
import { Footer } from '@/components/Footer';
import { SearchBar } from '@/components/SearchBar';
import { PropertyCard } from '@/components/PropertyCard';
import { PropertyFilters } from '@/components/PropertyFilters';
import Link from 'next/link';
import Image from 'next/image';



export default function BuyPage() {
  const [filters, setFilters] = useState({
    type: '',
    minPrice: '',
    maxPrice: '',
    bedrooms: '',
    city: ''
  });
  const [properties, setProperties] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    hasNext: false,
    hasPrev: false
  });

  const fetchProperties = async (filterParams = filters, page = 1) => {
    setLoading(true);
    try {
      // Filter out empty values from filters
      const cleanFilters = Object.entries(filterParams).reduce((acc, [key, value]) => {
        if (value && value.trim() !== '') {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, string>);

      const query = new URLSearchParams({
        page: page.toString(),
        limit: '12',
        ...cleanFilters,
      }).toString();

      const response = await fetch(`/api/properties?${query}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();

      setProperties(data.properties);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error loading properties:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load properties on component mount
  useEffect(() => {
    fetchProperties();
  }, []);

  const handleFilterChange = useCallback((newFilters: any) => {
    setFilters(newFilters);
    fetchProperties(newFilters, 1); // Reset to page 1 when filters change
  }, []);

  return (
    <main className="min-h-screen">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative h-[500px] flex items-center justify-center">
        <div className="absolute inset-0 z-0">
          <Image 
            src="https://images.unsplash.com/photo-1560518883-ce09059eeffa?q=80&w=1473&auto=format&fit=crop" 
            alt="Buy Properties" 
            fill 
            className="object-cover brightness-50"
          />
        </div>
        <div className="container-custom relative z-10 text-center text-white">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Find Your Dream Home</h1>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Discover a wide range of properties for sale that match your lifestyle and budget
          </p>
          <SearchBar />
        </div>
      </section>
      
      {/* Buying Guide */}
      <section className="py-16 bg-gray-100">
        <div className="container-custom">
          <h2 className="text-3xl font-bold mb-12 text-center">Your Home Buying Journey</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">1</div>
              <h3 className="text-xl font-bold mb-3">Get Pre-Approved</h3>
              <p className="text-text-secondary">
                Start by getting pre-approved for a mortgage to understand your budget and show sellers you're serious.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">2</div>
              <h3 className="text-xl font-bold mb-3">Find Your Home</h3>
              <p className="text-text-secondary">
                Browse our listings, schedule viewings, and find the perfect property that meets your needs.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">3</div>
              <h3 className="text-xl font-bold mb-3">Make an Offer</h3>
              <p className="text-text-secondary">
                Work with our agents to make a competitive offer and negotiate the best terms for your purchase.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">4</div>
              <h3 className="text-xl font-bold mb-3">Close the Deal</h3>
              <p className="text-text-secondary">
                Complete inspections, secure financing, and sign the final paperwork to become a homeowner.
              </p>
            </div>
          </div>
          <div className="text-center mt-10">
            <Link href="/buying-guide" className="btn-primary">
              View Complete Buying Guide
            </Link>
          </div>
        </div>
      </section>
      
      {/* Properties Section */}
      <section className="py-16">
        <div className="container-custom">
          <h2 className="text-3xl font-bold mb-12 text-center">Properties For Sale</h2>
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Filters Sidebar */}
            <div className="lg:w-1/4">
              <PropertyFilters onFilterChange={handleFilterChange} />
            </div>
            
            {/* Properties Grid */}
            <div className="lg:w-3/4">
              <div className="flex justify-between items-center mb-6">
                <p className="text-text-secondary">
                  Showing <span className="font-semibold">{properties.length}</span> of <span className="font-semibold">{pagination.totalItems}</span> properties
                </p>
                <div className="flex items-center space-x-2">
                  <label htmlFor="sort" className="text-text-secondary">Sort by:</label>
                  <select
                    id="sort"
                    className="border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    <option value="newest">Newest</option>
                    <option value="price-asc">Price (Low to High)</option>
                    <option value="price-desc">Price (High to Low)</option>
                    <option value="popular">Most Popular</option>
                  </select>
                </div>
              </div>

              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-4 text-gray-600">Loading properties...</p>
                </div>
              ) : properties.length === 0 ? (
                <div className="col-span-full text-center py-8">
                  <p className="text-gray-500">No properties found matching your criteria</p>
                  <Link href="/properties" className="text-primary hover:underline mt-2 inline-block">
                    Browse All Properties
                  </Link>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {properties.map((property: any) => (
                    <PropertyCard key={property.id} property={property} />
                  ))}
                </div>
              )}

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <div className="mt-12 flex justify-center">
                  <nav className="flex items-center space-x-2">
                    <button
                      onClick={() => pagination.hasPrev && fetchProperties(filters, pagination.currentPage - 1)}
                      disabled={!pagination.hasPrev}
                      className="w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>

                    {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                      const page = i + 1;
                      return (
                        <button
                          key={page}
                          onClick={() => fetchProperties(filters, page)}
                          className={`w-10 h-10 rounded-md flex items-center justify-center ${
                            pagination.currentPage === page
                              ? 'bg-primary text-white'
                              : 'border border-gray-300 hover:bg-gray-100'
                          }`}
                        >
                          {page}
                        </button>
                      );
                    })}

                    <button
                      onClick={() => pagination.hasNext && fetchProperties(filters, pagination.currentPage + 1)}
                      disabled={!pagination.hasNext}
                      className="w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </nav>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>
      
      {/* Buying Tips */}
      <section className="py-16 bg-gray-100">
        <div className="container-custom">
          <h2 className="text-3xl font-bold mb-12 text-center">Home Buying Tips</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="relative h-48">
                <Image 
                  src="https://images.unsplash.com/photo-1560520031-3a4dc4e9de0c?q=80&w=1473&auto=format&fit=crop" 
                  alt="Financial Planning" 
                  fill 
                  className="object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-3">Financial Planning</h3>
                <p className="text-text-secondary mb-4">
                  Learn how to budget for your home purchase, including down payment, closing costs, and ongoing expenses.
                </p>
                <Link href="/blog/financial-planning" className="text-primary font-semibold hover:underline">
                  Read More
                </Link>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="relative h-48">
                <Image 
                  src="https://images.unsplash.com/photo-1628744448840-55bdb2497bd4?q=80&w=1470&auto=format&fit=crop" 
                  alt="Home Inspection" 
                  fill 
                  className="object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-3">Home Inspection Guide</h3>
                <p className="text-text-secondary mb-4">
                  What to look for during a home inspection and red flags that could save you from a costly mistake.
                </p>
                <Link href="/blog/home-inspection" className="text-primary font-semibold hover:underline">
                  Read More
                </Link>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="relative h-48">
                <Image 
                  src="https://images.unsplash.com/photo-1450101499163-c8848c66ca85?q=80&w=1470&auto=format&fit=crop" 
                  alt="Negotiation Strategies" 
                  fill 
                  className="object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-3">Negotiation Strategies</h3>
                <p className="text-text-secondary mb-4">
                  Expert tips on negotiating the best price and terms when making an offer on your dream home.
                </p>
                <Link href="/blog/negotiation-strategies" className="text-primary font-semibold hover:underline">
                  Read More
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-16 bg-primary text-white">
        <div className="container-custom text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Find Your Dream Home?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Our expert agents are ready to help you navigate the buying process from start to finish.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/contact" className="btn-white">
              Contact an Agent
            </Link>
            <Link href="/properties" className="btn-outline-white">
              Browse All Properties
            </Link>
          </div>
        </div>
      </section>
      
      <Footer />
    </main>
  );
}