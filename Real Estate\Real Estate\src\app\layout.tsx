import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'], variable: '--font-inter' });

export const metadata: Metadata = {
  title: {
    default: 'Real Estate India - Buy, Sell, and Rent Properties',
    template: '%s | Real Estate India'
  },
  description: 'Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities, or list your property with us. Expert guidance for all your property needs.',
  keywords: ['real estate India', 'property for sale', 'property for rent', 'buy property', 'sell property', 'apartments', 'houses', 'villas', 'commercial property'],
  authors: [{ name: 'Real Estate India' }],
  creator: 'Real Estate India',
  publisher: 'Real Estate India',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://realestate-india.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_IN',
    url: 'https://realestate-india.com',
    title: 'Real Estate India - Buy, Sell, and Rent Properties',
    description: 'Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities.',
    siteName: 'Real Estate India',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Real Estate India - Buy, Sell, and Rent Properties',
    description: 'Find your dream home in India with our comprehensive real estate platform.',
    creator: '@realestateindia',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.variable} font-sans bg-background text-text-primary antialiased`}>
        {children}
      </body>
    </html>
  );
}