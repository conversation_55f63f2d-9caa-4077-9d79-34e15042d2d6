<?php
/**
 * API Debug Script for housing.okayy.in
 * This script helps debug API connectivity and database issues
 * 
 * Access via: https://housing.okayy.in/debug-api.php
 * IMPORTANT: Delete this file after debugging for security!
 */

header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>API Debug - housing.okayy.in</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 API Debug for housing.okayy.in</h1>
    
    <?php
    echo "<div class='section'>";
    echo "<h2>1. PHP Configuration</h2>";
    echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
    echo "<p><strong>Server:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
    echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
    echo "<p><strong>Current Script:</strong> " . __FILE__ . "</p>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>2. Database Connection Test</h2>";
    
    // Test database connection
    try {
        require_once 'Real Estate/php-backend/config/database.php';
        $database = new Database();
        $db = $database->getConnection();
        
        echo "<p class='success'>✅ Database connection successful!</p>";
        
        // Test if users table exists
        $stmt = $db->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() > 0) {
            echo "<p class='success'>✅ Users table exists</p>";
            
            // Count users
            $stmt = $db->query("SELECT COUNT(*) as count FROM users");
            $result = $stmt->fetch();
            echo "<p class='info'>👥 Total users: " . $result['count'] . "</p>";
            
            // Check for admin users
            $stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE role = 'ADMIN'");
            $result = $stmt->fetch();
            echo "<p class='info'>👑 Admin users: " . $result['count'] . "</p>";
            
        } else {
            echo "<p class='error'>❌ Users table does not exist</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>3. API Endpoints Test</h2>";
    
    $endpoints = [
        'Check Session' => '/php-backend/api/auth/check-session.php',
        'Properties' => '/php-backend/api/properties/index.php',
        'Contact' => '/php-backend/api/contact/index.php'
    ];
    
    foreach ($endpoints as $name => $endpoint) {
        $fullUrl = 'https://' . $_SERVER['HTTP_HOST'] . $endpoint;
        echo "<p><strong>$name:</strong> ";
        
        // Test if file exists
        $filePath = $_SERVER['DOCUMENT_ROOT'] . $endpoint;
        if (file_exists($filePath)) {
            echo "<span class='success'>✅ File exists</span> - ";
            echo "<a href='$fullUrl' target='_blank'>Test URL</a>";
        } else {
            echo "<span class='error'>❌ File missing: $filePath</span>";
        }
        echo "</p>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>4. File Permissions</h2>";
    
    $checkPaths = [
        'php-backend' => 'Real Estate/php-backend',
        'uploads' => 'Real Estate/php-backend/uploads',
        'config' => 'Real Estate/php-backend/config'
    ];
    
    foreach ($checkPaths as $name => $path) {
        $fullPath = $_SERVER['DOCUMENT_ROOT'] . '/' . $path;
        echo "<p><strong>$name:</strong> ";
        
        if (is_dir($fullPath)) {
            $perms = substr(sprintf('%o', fileperms($fullPath)), -4);
            echo "<span class='success'>✅ Directory exists</span> (Permissions: $perms)";
        } else {
            echo "<span class='error'>❌ Directory missing: $fullPath</span>";
        }
        echo "</p>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>5. Environment Variables</h2>";
    echo "<pre>";
    echo "HTTP_HOST: " . ($_SERVER['HTTP_HOST'] ?? 'Not set') . "\n";
    echo "REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "\n";
    echo "HTTPS: " . ($_SERVER['HTTPS'] ?? 'Not set') . "\n";
    echo "NODE_ENV: " . ($_ENV['NODE_ENV'] ?? 'Not set') . "\n";
    echo "</pre>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>6. Error Log Check</h2>";
    $errorLog = ini_get('error_log');
    echo "<p><strong>Error Log Location:</strong> " . ($errorLog ?: 'Default system log') . "</p>";
    echo "<p class='info'>💡 Check your hosting control panel for PHP error logs</p>";
    echo "</div>";
    ?>
    
    <div class='section'>
        <h2>7. Next Steps</h2>
        <ol>
            <li>If database connection fails, update credentials in <code>php-backend/config/database.php</code></li>
            <li>If files are missing, ensure all files were uploaded correctly</li>
            <li>Check browser console for JavaScript errors</li>
            <li>Review hosting error logs for PHP errors</li>
            <li><strong>Delete this debug file after use!</strong></li>
        </ol>
    </div>
    
    <div class='section'>
        <h2>8. Quick Admin Reset</h2>
        <p>If admin login still doesn't work, run: <code>php reset-admin-production.php</code></p>
        <p>Then try logging in with the credentials shown in the output.</p>
    </div>
    
</body>
</html>
