import Link from 'next/link';
import Image from 'next/image';
import { formatIndianAmount } from '@/lib/utils';

interface Property {
  id: string;
  title: string;
  description: string;
  price: number;
  currency: string;
  type: string;
  bedrooms: number | null;
  bathrooms: number | null;
  area: number;
  address: string;
  city: string;
  state: string;
  images: string;
  amenities: string;
  owner: {
    name: string;
    email: string;
    phone?: string;
  };
  createdAt: string;
}

interface PropertyCardProps {
  property: Property; // Changed to single property
}

export function PropertyCard({ property }: PropertyCardProps) { // Changed to single property
  // Format price with Indian Rupee
  const formatPrice = (price: number, title: string, type: string) => {
    // Check if it's a rental property, PG, or based on price range
    const isRental = title.toLowerCase().includes('rent') ||
                    type === 'PG' ||
                    title.toLowerCase().includes('pg') ||
                    title.toLowerCase().includes('paying guest') ||
                    price < 100000;
    if (isRental) {
      return `${formatIndianAmount(price)}/month`;
    }
    return formatIndianAmount(price);
  };

  const parseImages = (imagesString: string): string[] => {
    try {
      return JSON.parse(imagesString || '[]');
    } catch {
      return [];
    }
  };

  const processImageUrl = (imageUrl: string): string => {
    if (!imageUrl) return 'https://via.placeholder.com/400x300/e5e7eb/6b7280?text=Property+Image';
    if (imageUrl.startsWith('http')) return imageUrl;
    if (imageUrl.startsWith('/')) return `https://housing.okayy.in${imageUrl}`;
    return `https://housing.okayy.in/php-backend/uploads/${imageUrl}`;
  };

  const isNew = (createdAt: string): boolean => {
    const created = new Date(createdAt);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - created.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 7; // Consider new if created within 7 days
  };

  const images = parseImages(property.images);
  const mainImage = images.length > 0 ? processImageUrl(images[0]) : 'https://via.placeholder.com/400x300/e5e7eb/6b7280?text=Property+Image';
  const propertyIsNew = isNew(property.createdAt);

  return (
    <div key={property.id} className="card-elevated group overflow-hidden animate-fade-in">
      {/* Property Image */}
      <div className="relative h-72 w-full overflow-hidden">
        <img
          src={mainImage}
          alt={property.title}
          className="w-full h-full object-cover transition-all duration-700 group-hover:scale-110 group-hover:brightness-110"
          onError={(e) => {
            e.currentTarget.src = 'https://via.placeholder.com/400x300/e5e7eb/6b7280?text=Property+Image';
          }}
        />

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* Property Type Badge */}
        <div className="absolute top-4 left-4">
          <span className={`px-4 py-2 rounded-xl text-sm font-semibold backdrop-blur-sm border border-white/20 ${
            property.type === 'PG' || property.title.toLowerCase().includes('pg')
              ? 'bg-purple-500/90 text-white'
              : property.title.toLowerCase().includes('rent') || property.price < 100000
              ? 'bg-accent-500/90 text-white'
              : 'bg-primary-500/90 text-white'
          } shadow-soft`}>
            {property.type === 'PG' || property.title.toLowerCase().includes('pg')
              ? 'PG'
              : property.title.toLowerCase().includes('rent') || property.price < 100000
              ? 'For Rent'
              : 'For Sale'}
          </span>
        </div>

        {/* New Badge */}
        {propertyIsNew && (
          <div className="absolute top-4 right-16">
            <span className="px-4 py-2 rounded-xl text-sm font-semibold bg-success-500/90 text-white backdrop-blur-sm border border-white/20 shadow-soft animate-bounce-subtle">
              ✨ New
            </span>
          </div>
        )}

        {/* Save Button */}
        {/* <button className="absolute top-4 right-4 w-12 h-12 rounded-xl bg-white/90 backdrop-blur-sm shadow-soft flex items-center justify-center text-text-secondary hover:text-error-500 hover:bg-white transition-all duration-300 transform hover:scale-110 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </button> */}

        {/* Quick View Button - appears on hover */}
        <div className="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300">
          <button className="w-full py-3 bg-white/95 backdrop-blur-sm text-text-primary font-semibold rounded-xl shadow-soft hover:bg-white transition-all duration-300">
            Quick View
          </button>
        </div>
      </div>
    
      {/* Property Details */}
      <div className="p-6 space-y-4">
        <div className="space-y-2">
          <h3 className="text-xl font-bold text-text-primary group-hover:text-primary-600 transition-colors duration-300 line-clamp-2">
            {property.title}
          </h3>
          <p className="text-text-tertiary text-sm flex items-center">
            {/* <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-text-tertiary flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg> */}
            <span className="line-clamp-1">{property.address}, {property.city}, {property.state}</span>
          </p>
        </div>

        <div className="flex justify-between items-center">
          <div className="space-y-1">
            <span className="text-2xl font-bold text-gradient">
              {formatPrice(property.price, property.title, property.type)}
            </span>
            {(property.title.toLowerCase().includes('rent') ||
              property.type === 'PG' ||
              property.title.toLowerCase().includes('pg') ||
              property.price < 100000) && (
              <p className="text-xs text-text-tertiary">per month</p>
            )}
          </div>
          <div className="text-right">
            <div className="text-sm text-text-tertiary">Price per sqft</div>
            <div className="text-lg font-semibold text-text-secondary">
              ₹{Math.round(property.price / property.area).toLocaleString('en-IN')}
            </div>
          </div>
        </div>
      
        <div className="grid grid-cols-3 gap-4 py-4 border-t border-gray-100">
          {property.bedrooms && (
            <div className="flex flex-col items-center space-y-2">
              <div className="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center">
                <span className="text-lg font-bold text-blue-600">{property.bedrooms}</span>
              </div>
              <div className="text-xs text-text-tertiary">Beds</div>
            </div>
          )}
          {property.bathrooms && (
            <div className="flex flex-col items-center space-y-2">
              <div className="w-12 h-12 bg-green-50 rounded-lg flex items-center justify-center">
                <span className="text-lg font-bold text-green-600">{property.bathrooms}</span>
              </div>
              <div className="text-xs text-text-tertiary">Baths</div>
            </div>
          )}
          <div className="flex flex-col items-center space-y-2">
            <div className="w-12 h-12 bg-purple-50 rounded-lg flex items-center justify-center">
              <span className="text-lg font-bold text-purple-600">{property.area}</span>
            </div>
            <div className="text-xs text-text-tertiary">sqft</div>
          </div>
        </div>

        <div className="flex space-x-3">
          <Link 
            href={property.type === 'PG' ? `/pg/${property.id}` : `/properties/${property.id}`} 
            className="btn-primary flex-1 text-center"
          >
            View Details
          </Link>
          <button className="px-4 py-3 bg-primary-50 text-primary-600 font-semibold rounded-xl hover:bg-primary-100 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
            {/* <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg> */}
            Call
          </button>
        </div>
      </div>
    </div>
  );
}
