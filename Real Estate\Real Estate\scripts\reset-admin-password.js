const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  try {
    // Find admin user
    const admin = await prisma.user.findFirst({
      where: {
        role: 'ADMIN',
      },
    });

    if (!admin) {
      console.log('No admin user found');
      return;
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash('admin123', 10);

    // Update admin password
    const updatedAdmin = await prisma.user.update({
      where: {
        id: admin.id,
      },
      data: {
        password: hashedPassword,
      },
    });

    console.log('Admin password reset successfully for:', updatedAdmin.email);
    console.log('New password: admin123');
  } catch (error) {
    console.error('Error resetting admin password:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();