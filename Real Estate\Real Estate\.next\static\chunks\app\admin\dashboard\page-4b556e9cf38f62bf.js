(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[957],{1008:(e,s,t)=>{"use strict";t.d(s,{Eo:()=>o,Er:()=>c,M5:()=>i,R2:()=>l,hh:()=>d});let r="/php-backend/api",a={LOGIN:"".concat(r,"/auth/login.php"),SIGNUP:"".concat(r,"/auth/signup.php"),LOGOUT:"".concat(r,"/auth/logout.php"),CHECK_SESSION:"".concat(r,"/auth/check-session.php"),PROPERTIES:"".concat(r,"/properties/index.php"),PROPERTY_BY_ID:e=>"".concat(r,"/properties/get.php?id=").concat(e),BLOG_POSTS:"".concat(r,"/blog/index.php"),BLOG_POST_BY_SLUG:e=>"".concat(r,"/blog/get.php?slug=").concat(e),USER_PROPERTIES:"".concat(r,"/user/properties.php"),USER_INQUIRIES:"".concat(r,"/user/inquiries.php"),ADMIN_PROPERTIES:"".concat(r,"/admin/properties.php"),APPROVE_PROPERTY:e=>"".concat(r,"/admin/approve.php?id=").concat(e),REJECT_PROPERTY:e=>"".concat(r,"/admin/reject.php?id=").concat(e)},n=async function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t={headers:{"Content-Type":"application/json"},credentials:"include"},r={...t,...s,headers:{...t.headers,...s.headers}};try{let s;console.log("API Request:",{url:e,options:r});let t=await fetch(e,r),a=t.headers.get("content-type");if(a&&a.includes("application/json"))s=await t.json();else{let e=await t.text();throw console.error("Non-JSON response:",e),Error("Server returned non-JSON response: ".concat(e.substring(0,200)))}if(console.log("API Response:",{status:t.status,data:s}),!t.ok)throw Error(s.error||"HTTP error! status: ".concat(t.status));return s}catch(s){throw console.error("API request failed:",{url:e,error:s}),s}},l={login:async(e,s)=>n(a.LOGIN,{method:"POST",body:JSON.stringify({email:e,password:s})}),signup:async(e,s,t,r)=>n(a.SIGNUP,{method:"POST",body:JSON.stringify({name:e,email:s,password:t,phone:r})}),logout:async()=>n(a.LOGOUT,{method:"POST"}),checkSession:async()=>n(a.CHECK_SESSION)},i={getProperties:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=new URLSearchParams;return Object.entries(e).forEach(e=>{let[t,r]=e;null!=r&&""!==r&&s.append(t,r.toString())}),n("".concat(a.PROPERTIES,"?").concat(s.toString()))},createProperty:async e=>n(a.PROPERTIES,{method:"POST",body:JSON.stringify(e)}),getPropertyById:async e=>n(a.PROPERTY_BY_ID(e))},c={getProperties:async e=>n(e?"".concat(a.ADMIN_PROPERTIES,"?status=").concat(e):a.ADMIN_PROPERTIES),approveProperty:async e=>n(a.APPROVE_PROPERTY(e),{method:"PUT"}),rejectProperty:async(e,s)=>n(a.REJECT_PROPERTY(e),{method:"PUT",body:JSON.stringify({reason:s})})},d={getPosts:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=new URLSearchParams;return Object.entries(e).forEach(e=>{let[t,r]=e;null!=r&&""!==r&&s.append(t,r.toString())}),n("".concat(a.BLOG_POSTS,"?").concat(s.toString()))},getPostBySlug:async e=>n(a.BLOG_POST_BY_SLUG(e))},o={getProperties:async()=>n(a.USER_PROPERTIES),getInquiries:async()=>n(a.USER_INQUIRIES)}},4163:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(5155),a=t(2115),n=t(5695),l=t(1008);function i(){let[e,s]=(0,a.useState)(null),[t,i]=(0,a.useState)([]),[c,d]=(0,a.useState)({total:0,pending:0,approved:0,rejected:0}),[o,x]=(0,a.useState)(!0),[h,p]=(0,a.useState)("overview"),m=(0,n.useRouter)();(0,a.useEffect)(()=>{u()},[]);let u=async()=>{try{let e=await l.R2.checkSession();if(!e.user||"ADMIN"!==e.user.role)return void m.push("/login");s(e.user),await g()}catch(e){console.error("Auth check failed:",e),m.push("/login")}},g=async()=>{try{var e,s,t,r;let a=await l.Er.getProperties("all");i(a.properties||[]);let n=(null==(e=a.properties)?void 0:e.length)||0,c=(null==(s=a.properties)?void 0:s.filter(e=>"PENDING"===e.approval_status).length)||0,o=(null==(t=a.properties)?void 0:t.filter(e=>"APPROVED"===e.approval_status).length)||0,x=(null==(r=a.properties)?void 0:r.filter(e=>"REJECTED"===e.approval_status).length)||0;d({total:n,pending:c,approved:o,rejected:x})}catch(e){console.error("Failed to load dashboard data:",e)}finally{x(!1)}},N=async e=>{try{await l.Er.approveProperty(e),await g(),alert("Property approved successfully!")}catch(e){alert(e.message||"Failed to approve property")}},j=async e=>{let s=prompt("Enter rejection reason:");if(s)try{await l.Er.rejectProperty(e,s),await g(),alert("Property rejected successfully!")}catch(e){alert(e.message||"Failed to reject property")}},y=async()=>{try{await l.R2.logout(),m.push("/login")}catch(e){console.error("Logout failed:",e)}};if(o)return(0,r.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading admin dashboard..."})]})});let v=t.filter(e=>"PENDING"===e.approval_status);return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,r.jsx)("header",{className:"bg-white shadow",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Admin Dashboard"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Welcome back, ",null==e?void 0:e.name]})]}),(0,r.jsx)("button",{onClick:y,className:"bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700",children:"Logout"})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"border-b border-gray-200 mb-8",children:(0,r.jsx)("nav",{className:"-mb-px flex space-x-8",children:[{id:"overview",name:"Overview"},{id:"pending",name:"Pending Approvals"},{id:"all",name:"All Properties"}].map(e=>(0,r.jsx)("button",{onClick:()=>p(e.id),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(h===e.id?"border-red-500 text-red-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:e.name},e.id))})}),"overview"===h&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold",children:"T"})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Properties"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:c.total})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold",children:"P"})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Pending"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:c.pending})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold",children:"A"})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Approved"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:c.approved})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold",children:"R"})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Rejected"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:c.rejected})]})})]})})})]}),"pending"===h&&(0,r.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:[(0,r.jsx)("div",{className:"px-4 py-5 sm:px-6",children:(0,r.jsxs)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:["Pending Property Approvals (",v.length,")"]})}),(0,r.jsxs)("ul",{className:"divide-y divide-gray-200",children:[v.map(e=>(0,r.jsx)("li",{className:"px-4 py-4 sm:px-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["₹",e.price.toLocaleString()," • ",e.type," • Owner: ",e.owner.name]}),(0,r.jsxs)("p",{className:"text-xs text-gray-400",children:["Submitted: ",new Date(e.created_at).toLocaleDateString()]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>N(e.id),className:"bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700",children:"Approve"}),(0,r.jsx)("button",{onClick:()=>j(e.id),className:"bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700",children:"Reject"})]})]})},e.id)),0===v.length&&(0,r.jsx)("li",{className:"px-4 py-8 text-center text-gray-500",children:"No pending properties to review"})]})]}),"all"===h&&(0,r.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:[(0,r.jsx)("div",{className:"px-4 py-5 sm:px-6",children:(0,r.jsxs)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:["All Properties (",t.length,")"]})}),(0,r.jsx)("ul",{className:"divide-y divide-gray-200",children:t.map(e=>(0,r.jsx)("li",{className:"px-4 py-4 sm:px-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["₹",e.price.toLocaleString()," • ",e.type," • Owner: ",e.owner.name]}),(0,r.jsxs)("p",{className:"text-xs text-gray-400",children:["Created: ",new Date(e.created_at).toLocaleDateString()]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("APPROVED"===e.approval_status?"bg-green-100 text-green-800":"PENDING"===e.approval_status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:e.approval_status}),"PENDING"===e.approval_status&&(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("button",{onClick:()=>N(e.id),className:"bg-green-600 text-white px-2 py-1 rounded text-xs hover:bg-green-700",children:"Approve"}),(0,r.jsx)("button",{onClick:()=>j(e.id),className:"bg-red-600 text-white px-2 py-1 rounded text-xs hover:bg-red-700",children:"Reject"})]})]})]})},e.id))})]})]})]})}},5695:(e,s,t)=>{"use strict";var r=t(8999);t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},8505:(e,s,t)=>{Promise.resolve().then(t.bind(t,4163))}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(8505)),_N_E=e.O()}]);