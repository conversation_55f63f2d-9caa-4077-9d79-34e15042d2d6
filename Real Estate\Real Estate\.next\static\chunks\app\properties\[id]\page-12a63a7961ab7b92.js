(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[968],{1469:(e,s,r)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,s){for(var r in s)Object.defineProperty(e,r,{enumerable:!0,get:s[r]})}(s,{default:function(){return d},getImageProps:function(){return n}});let t=r(8229),a=r(8883),l=r(3063),i=t._(r(1193));function n(e){let{props:s}=(0,a.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(s))void 0===r&&delete s[e];return{props:s}}let d=l.Image},4039:(e,s,r)=>{Promise.resolve().then(r.bind(r,8362))},6766:(e,s,r)=>{"use strict";r.d(s,{default:()=>a.a});var t=r(1469),a=r.n(t)},8362:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>x});var t=r(5155),a=r(2115),l=r(6766),i=r(6874),n=r.n(i),d=r(5494),c=r(6821),o=r(1008);function x(e){let s,{params:r}=e,[i,x]=(0,a.useState)(null),[m,p]=(0,a.useState)(!0),[h,g]=(0,a.useState)(null);if((0,a.useEffect)(()=>{(async()=>{try{p(!0);let e=await o.M5.getPropertyById(r.id);e.success&&e.property?"APPROVED"===e.property.approval_status?x(e.property):g("Property not found or not approved"):g("Property not found")}catch(e){console.error("Error fetching property:",e),g("Failed to load property details")}finally{p(!1)}})()},[r.id]),m)return(0,t.jsxs)("main",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(d.Navbar,{}),(0,t.jsxs)("div",{className:"container-custom py-16 text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading property details..."})]}),(0,t.jsx)(c.w,{})]});if(h||!i)return(0,t.jsxs)("main",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(d.Navbar,{}),(0,t.jsxs)("div",{className:"container-custom py-16 text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Property Not Found"}),(0,t.jsx)("p",{className:"text-gray-600 mb-8",children:h||"The property you are looking for does not exist or is not available."}),(0,t.jsx)(n(),{href:"/properties",className:"btn-primary",children:"Browse Properties"})]}),(0,t.jsx)(c.w,{})]});let u="string"==typeof i.images?JSON.parse(i.images):i.images,j="string"==typeof i.amenities?JSON.parse(i.amenities):i.amenities;return(0,t.jsxs)("main",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(d.Navbar,{}),(0,t.jsx)("section",{className:"relative",children:(0,t.jsx)("div",{className:"container-custom py-8",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:[(0,t.jsx)("div",{className:"relative h-96 lg:h-[500px] rounded-2xl overflow-hidden",children:(0,t.jsx)(l.default,{src:u[0]||"/placeholder-property.jpg",alt:i.title,fill:!0,className:"object-cover",priority:!0})}),(0,t.jsx)("div",{className:"grid grid-cols-2 gap-4",children:u.slice(1,5).map((e,s)=>(0,t.jsx)("div",{className:"relative h-44 lg:h-60 rounded-xl overflow-hidden",children:(0,t.jsx)(l.default,{src:e,alt:"".concat(i.title," - Image ").concat(s+2),fill:!0,className:"object-cover"})},s))})]})})}),(0,t.jsx)("section",{className:"py-12",children:(0,t.jsx)("div",{className:"container-custom",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-12",children:[(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-large p-8",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:i.title}),(0,t.jsxs)("div",{className:"flex items-center text-gray-600 mb-4",children:[(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,t.jsxs)("span",{children:[i.address,", ",i.city,", ",i.state]})]}),(0,t.jsx)("div",{className:"text-4xl font-bold text-primary mb-6",children:(s=i.price,new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:0}).format(s))})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6 mb-8",children:[(0,t.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-primary",children:i.bedrooms}),(0,t.jsx)("div",{className:"text-gray-600",children:"Bedrooms"})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-primary",children:i.bathrooms}),(0,t.jsx)("div",{className:"text-gray-600",children:"Bathrooms"})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-primary",children:i.area}),(0,t.jsx)("div",{className:"text-gray-600",children:"Sq Ft"})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-primary",children:i.type}),(0,t.jsx)("div",{className:"text-gray-600",children:"Type"})]})]}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Description"}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed",children:i.description})]}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Amenities"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:j.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center p-3 bg-gray-50 rounded-xl",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-primary mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,t.jsx)("span",{className:"text-gray-700",children:e})]},s))})]})]})}),(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-large p-6 sticky top-8",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"Contact Agent"}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)("div",{className:"flex items-center mb-3",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-3",children:(0,t.jsx)("span",{className:"text-white font-semibold",children:i.owner.name?i.owner.name.charAt(0):"U"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold text-gray-900",children:i.owner.name||"Unknown"}),(0,t.jsx)("div",{className:"text-gray-600 text-sm",children:"Property Agent"})]})]})}),(0,t.jsxs)("form",{className:"space-y-4",children:[(0,t.jsx)("div",{children:(0,t.jsx)("input",{type:"text",placeholder:"Your Name",className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"})}),(0,t.jsx)("div",{children:(0,t.jsx)("input",{type:"email",placeholder:"Your Email",className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"})}),(0,t.jsx)("div",{children:(0,t.jsx)("input",{type:"tel",placeholder:"Your Phone",className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"})}),(0,t.jsx)("div",{children:(0,t.jsx)("textarea",{rows:4,placeholder:"I'm interested in this property. Please contact me with more information.",className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"})}),(0,t.jsx)("button",{type:"submit",className:"w-full btn-primary",children:"Send Message"})]}),(0,t.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[(0,t.jsx)("span",{children:"Listed on:"}),(0,t.jsx)("span",{children:new Date(i.createdAt).toLocaleDateString()})]})})]})})]})})}),(0,t.jsx)("section",{className:"py-12 bg-gray-100",children:(0,t.jsxs)("div",{className:"container-custom",children:[(0,t.jsx)("h2",{className:"text-2xl md:text-3xl font-bold mb-8",children:"Similar Properties"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:(0,t.jsxs)("div",{className:"col-span-full text-center py-8",children:[(0,t.jsx)("p",{className:"text-gray-500",children:"Similar properties will be displayed here"}),(0,t.jsx)(n(),{href:"/properties/",className:"text-primary hover:underline mt-2 inline-block",children:"Browse All Properties"})]})})]})}),(0,t.jsx)(c.w,{})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[874,63,494,821,441,684,358],()=>s(4039)),_N_E=e.O()}]);