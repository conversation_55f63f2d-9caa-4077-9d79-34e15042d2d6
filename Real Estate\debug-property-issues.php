<?php
/**
 * Debug Property Images and Details Pages
 * Tests property API, image loading, and detail page routing
 */
?>
<!DOCTYPE html>
<html>
<head>
    <title>🐛 Debug Property Issues - housing.okayy.in</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 20px auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .test-item { padding: 10px; margin: 5px 0; background: #f8f9fa; border-radius: 5px; }
        .btn { background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; max-height: 300px; }
        .json-output { background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; margin: 10px 0; }
        .property-card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .property-image { width: 200px; height: 150px; object-fit: cover; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 Debug Property Images & Details Pages</h1>
        <p><strong>Purpose:</strong> Test property API, image loading, and detail page routing</p>
        
        <?php
        // Test 1: Database Connection and Property Count
        echo "<div class='test-section'>";
        echo "<h2>1. 🔗 Database & Properties Check</h2>";
        
        try {
            require_once 'php-backend/config/database.php';
            $database = new Database();
            $db = $database->getConnection();
            echo "<div class='test-item'><span class='success'>✅ Database connection: Working</span></div>";
            
            // Count total properties
            $stmt = $db->query("SELECT COUNT(*) as count FROM properties");
            $result = $stmt->fetch();
            $totalProperties = $result['count'];
            echo "<div class='test-item'><span class='info'>🏠 Total properties: $totalProperties</span></div>";
            
            // Count approved properties
            $stmt = $db->query("SELECT COUNT(*) as count FROM properties WHERE approval_status = 'APPROVED'");
            $result = $stmt->fetch();
            $approvedProperties = $result['count'];
            echo "<div class='test-item'><span class='info'>✅ Approved properties: $approvedProperties</span></div>";
            
            // Count rental properties
            $stmt = $db->query("SELECT COUNT(*) as count FROM properties WHERE listing_type = 'RENT' AND approval_status = 'APPROVED'");
            $result = $stmt->fetch();
            $rentalProperties = $result['count'];
            echo "<div class='test-item'><span class='info'>🏠 Rental properties: $rentalProperties</span></div>";
            
        } catch (Exception $e) {
            echo "<div class='test-item'><span class='error'>❌ Database error: " . $e->getMessage() . "</span></div>";
        }
        echo "</div>";
        
        // Test 2: Property Search API
        echo "<div class='test-section'>";
        echo "<h2>2. 🔌 Property Search API Test</h2>";
        
        $apiTests = [
            'All Properties' => '/php-backend/api/properties/search.php?limit=3',
            'Rental Properties' => '/php-backend/api/properties/search.php?listingType=RENT&limit=3',
            'Sale Properties' => '/php-backend/api/properties/search.php?listingType=SALE&limit=3',
            'PG Properties' => '/php-backend/api/properties/search.php?type=PG&limit=3'
        ];
        
        foreach ($apiTests as $testName => $endpoint) {
            echo "<div class='test-item'>";
            echo "<strong>Testing: $testName</strong><br>";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://' . $_SERVER['HTTP_HOST'] . $endpoint);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            echo "HTTP Status: $httpCode<br>";
            
            if ($httpCode == 200) {
                echo "<span class='success'>✅ API accessible</span><br>";
                $jsonData = json_decode($response, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    echo "<span class='success'>✅ Valid JSON response</span><br>";
                    if (isset($jsonData['properties'])) {
                        $count = count($jsonData['properties']);
                        echo "<span class='info'>📊 Properties returned: $count</span><br>";
                    }
                } else {
                    echo "<span class='error'>❌ Invalid JSON: " . json_last_error_msg() . "</span><br>";
                }
            } else {
                echo "<span class='error'>❌ HTTP Error: $httpCode</span><br>";
            }
            echo "</div>";
        }
        echo "</div>";
        
        // Test 3: Sample Property Data & Images
        echo "<div class='test-section'>";
        echo "<h2>3. 🖼️ Property Images Test</h2>";
        
        try {
            // Get sample properties with images
            $query = "SELECT p.*, u.name as owner_name, u.email as owner_email 
                      FROM properties p 
                      JOIN users u ON p.owner_id = u.id 
                      WHERE p.approval_status = 'APPROVED' 
                      AND p.images IS NOT NULL 
                      AND p.images != '' 
                      AND p.images != '[]'
                      LIMIT 3";
            
            $stmt = $db->prepare($query);
            $stmt->execute();
            $properties = $stmt->fetchAll();
            
            if (count($properties) > 0) {
                echo "<div class='test-item'><span class='success'>✅ Found " . count($properties) . " properties with images</span></div>";
                
                foreach ($properties as $property) {
                    echo "<div class='property-card'>";
                    echo "<h4>" . htmlspecialchars($property['title']) . "</h4>";
                    echo "<p><strong>ID:</strong> " . $property['id'] . "</p>";
                    echo "<p><strong>Type:</strong> " . $property['type'] . " | <strong>Listing:</strong> " . $property['listing_type'] . "</p>";
                    
                    // Test images
                    $images = json_decode($property['images'], true);
                    if (is_array($images) && count($images) > 0) {
                        echo "<p><strong>Images found:</strong> " . count($images) . "</p>";
                        
                        foreach (array_slice($images, 0, 2) as $index => $image) {
                            $imageUrl = '';
                            if (strpos($image, 'http') === 0) {
                                $imageUrl = $image;
                            } elseif (strpos($image, '/') === 0) {
                                $imageUrl = 'https://housing.okayy.in' . $image;
                            } else {
                                $imageUrl = 'https://housing.okayy.in/php-backend/uploads/' . $image;
                            }
                            
                            echo "<div style='margin: 10px 0;'>";
                            echo "<p><strong>Image " . ($index + 1) . ":</strong> " . htmlspecialchars($image) . "</p>";
                            echo "<p><strong>Full URL:</strong> " . htmlspecialchars($imageUrl) . "</p>";
                            echo "<img src='$imageUrl' class='property-image' onerror=\"this.src='https://via.placeholder.com/200x150/e5e7eb/6b7280?text=Image+Error'; this.style.border='2px solid red';\" />";
                            echo "</div>";
                        }
                    } else {
                        echo "<p><span class='warning'>⚠️ No valid images in JSON</span></p>";
                        echo "<p><strong>Raw images data:</strong> " . htmlspecialchars($property['images']) . "</p>";
                    }
                    
                    // Test detail page links
                    $detailUrl = ($property['type'] === 'PG') ? "/pg/{$property['id']}" : "/properties/{$property['id']}";
                    echo "<p><strong>Detail Page:</strong> <a href='$detailUrl' target='_blank'>$detailUrl</a></p>";
                    echo "</div>";
                }
            } else {
                echo "<div class='test-item'><span class='warning'>⚠️ No properties with images found</span></div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='test-item'><span class='error'>❌ Error: " . $e->getMessage() . "</span></div>";
        }
        echo "</div>";
        
        // Test 4: Property Detail API
        echo "<div class='test-section'>";
        echo "<h2>4. 📄 Property Detail API Test</h2>";
        
        try {
            // Get a sample property ID
            $stmt = $db->query("SELECT id FROM properties WHERE approval_status = 'APPROVED' LIMIT 1");
            $sampleProperty = $stmt->fetch();
            
            if ($sampleProperty) {
                $propertyId = $sampleProperty['id'];
                echo "<div class='test-item'>";
                echo "<strong>Testing property detail API for ID: $propertyId</strong><br>";
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, 'https://' . $_SERVER['HTTP_HOST'] . "/php-backend/api/properties/get.php?id=$propertyId");
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                echo "HTTP Status: $httpCode<br>";
                
                if ($httpCode == 200) {
                    echo "<span class='success'>✅ Property detail API working</span><br>";
                    $jsonData = json_decode($response, true);
                    if (json_last_error() === JSON_ERROR_NONE && isset($jsonData['property'])) {
                        echo "<span class='success'>✅ Property data returned</span><br>";
                    } else {
                        echo "<span class='error'>❌ Invalid response format</span><br>";
                    }
                } else {
                    echo "<span class='error'>❌ Property detail API error: $httpCode</span><br>";
                }
                echo "</div>";
            } else {
                echo "<div class='test-item'><span class='warning'>⚠️ No approved properties found for testing</span></div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='test-item'><span class='error'>❌ Error: " . $e->getMessage() . "</span></div>";
        }
        echo "</div>";
        ?>
        
        <!-- JavaScript API Test -->
        <div class="test-section">
            <h2>5. 🔧 Frontend API Test</h2>
            <div id="js-test-results"></div>
            <button onclick="testFrontendAPIs()" class="btn">🧪 Test Frontend APIs</button>
        </div>
        
        <script>
            async function testFrontendAPIs() {
                const resultsDiv = document.getElementById('js-test-results');
                resultsDiv.innerHTML = '<p>Testing APIs...</p>';
                
                const tests = [
                    { name: 'Property Search API', url: '/php-backend/api/properties/search.php?limit=2' },
                    { name: 'Rental Properties', url: '/php-backend/api/properties/search.php?listingType=RENT&limit=2' }
                ];
                
                let results = '';
                
                for (const test of tests) {
                    try {
                        const response = await fetch(test.url);
                        const data = await response.json();
                        
                        if (response.ok && data.success) {
                            results += `<div class="test-item"><span class="success">✅ ${test.name}: Working (${data.properties?.length || 0} properties)</span></div>`;
                            
                            // Test first property image if available
                            if (data.properties && data.properties.length > 0) {
                                const property = data.properties[0];
                                if (property.images && property.images.length > 0) {
                                    const imageUrl = property.images[0].startsWith('http') 
                                        ? property.images[0] 
                                        : `https://housing.okayy.in/php-backend/uploads/${property.images[0]}`;
                                    
                                    results += `<div class="test-item">
                                        <p><strong>Sample Image Test:</strong></p>
                                        <p>URL: ${imageUrl}</p>
                                        <img src="${imageUrl}" style="width: 150px; height: 100px; object-fit: cover;" 
                                             onerror="this.style.border='2px solid red'; this.alt='Image failed to load';" />
                                    </div>`;
                                }
                            }
                        } else {
                            results += `<div class="test-item"><span class="error">❌ ${test.name}: Failed</span></div>`;
                        }
                    } catch (error) {
                        results += `<div class="test-item"><span class="error">❌ ${test.name}: ${error.message}</span></div>`;
                    }
                }
                
                resultsDiv.innerHTML = results;
            }
        </script>
        
        <div class="test-section">
            <h2>6. 🔧 Recommended Actions</h2>
            <div class="test-item">
                <strong>Based on test results:</strong>
                <ol>
                    <li>If images show "Image Error": Check image file paths and uploads folder</li>
                    <li>If API returns 0 properties: Add some approved properties to database</li>
                    <li>If detail pages don't work: Check Next.js routing and build</li>
                    <li>If search API fails: Check PHP backend deployment</li>
                </ol>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="/rent" class="btn">🏠 Test Rent Page</a>
            <a href="/buy" class="btn">🏢 Test Buy Page</a>
            <a href="/pg" class="btn">🏠 Test PG Page</a>
        </div>
        
        <div class="test-section">
            <h2>🗑️ Cleanup</h2>
            <p><strong>After testing, delete this file:</strong> <code>debug-property-issues.php</code></p>
        </div>
    </div>
</body>
</html>
