<!DOCTYPE html><html lang="en" class="scroll-smooth"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/6650b15f75106e01.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-22534597e9bd48b5.js"/><script src="/_next/static/chunks/4bd1b696-aa487eca046639b4.js" async=""></script><script src="/_next/static/chunks/684-034bfc1bb224a087.js" async=""></script><script src="/_next/static/chunks/main-app-f73c5f7d4ff60b44.js" async=""></script><script src="/_next/static/chunks/874-8e9a565f7eb17c9e.js" async=""></script><script src="/_next/static/chunks/app/okayy/page-18b372a33a2ff156.js" async=""></script><title>Real Estate India - Buy, Sell, and Rent Properties</title><meta name="description" content="Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities, or list your property with us. Expert guidance for all your property needs."/><meta name="author" content="Real Estate India"/><meta name="keywords" content="real estate India,property for sale,property for rent,buy property,sell property,apartments,houses,villas,commercial property"/><meta name="creator" content="Real Estate India"/><meta name="publisher" content="Real Estate India"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><link rel="canonical" href="https://realestate-india.com/"/><meta name="format-detection" content="telephone=no, address=no, email=no"/><meta property="og:title" content="Real Estate India - Buy, Sell, and Rent Properties"/><meta property="og:description" content="Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities."/><meta property="og:url" content="https://realestate-india.com/"/><meta property="og:site_name" content="Real Estate India"/><meta property="og:locale" content="en_IN"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:creator" content="@realestateindia"/><meta name="twitter:title" content="Real Estate India - Buy, Sell, and Rent Properties"/><meta name="twitter:description" content="Find your dream home in India with our comprehensive real estate platform."/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_e8ce0c font-sans bg-background text-text-primary antialiased"><div hidden=""><!--$--><!--/$--></div><main class="min-h-screen bg-gradient-to-br from-gray-50 to-white"><header class="bg-white shadow-sm border-b"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between items-center h-16"><div class="flex items-center"><a class="text-2xl font-bold text-gray-900" href="/okayy/">Okayy<span class="text-blue-600">.in</span></a></div><nav class="hidden md:flex space-x-8"><a class="text-gray-600 hover:text-gray-900" href="#services">Services</a><a class="text-gray-600 hover:text-gray-900" href="#about">About</a><a class="text-gray-600 hover:text-gray-900" href="#contact">Contact</a></nav></div></div></header><section class="relative py-20 px-4 sm:px-6 lg:px-8"><div class="max-w-7xl mx-auto text-center"><h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">Your Gateway to<span class="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">Everything You Need</span></h1><p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">From finding your dream home to getting groceries delivered, discovering career opportunities, and accessing powerful online tools - all in one place.</p><div class="flex flex-col sm:flex-row gap-4 justify-center"><a class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors" href="#services">Explore Services</a><a class="border border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors" href="#about">Learn More</a></div></div></section><section id="services" class="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50"><div class="max-w-7xl mx-auto"><div class="text-center mb-16"><h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Services</h2><p class="text-xl text-gray-600 max-w-2xl mx-auto">Comprehensive solutions for your daily needs, all under one roof</p></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16"><div class="group relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden cursor-pointer transform hover:-translate-y-2 ring-2 ring-blue-500"><div class="h-48 bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center"><span class="text-6xl">🏠</span></div><div class="p-6"><h3 class="text-xl font-bold text-gray-900 mb-2">Real Estate</h3><p class="text-gray-600 mb-4">Find your dream home or investment property</p><ul class="space-y-1 mb-4"><li class="text-sm text-gray-500 flex items-center"><span class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>Buy Properties</li><li class="text-sm text-gray-500 flex items-center"><span class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>Rent Homes</li><li class="text-sm text-gray-500 flex items-center"><span class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>PG Accommodation</li><li class="text-sm text-gray-500 flex items-center"><span class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>Sell Property</li></ul><a class="inline-flex items-center text-white bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300" href="/">Explore <!-- -->Real Estate<svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></a></div></div><div class="group relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden cursor-pointer transform hover:-translate-y-2 "><div class="h-48 bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center"><span class="text-6xl">🛒</span></div><div class="p-6"><h3 class="text-xl font-bold text-gray-900 mb-2">Groceries</h3><p class="text-gray-600 mb-4">Fresh groceries delivered to your doorstep</p><ul class="space-y-1 mb-4"><li class="text-sm text-gray-500 flex items-center"><span class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>Fresh Vegetables</li><li class="text-sm text-gray-500 flex items-center"><span class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>Daily Essentials</li><li class="text-sm text-gray-500 flex items-center"><span class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>Organic Products</li><li class="text-sm text-gray-500 flex items-center"><span class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>Quick Delivery</li></ul><a class="inline-flex items-center text-white bg-gradient-to-r from-green-500 to-green-600 px-4 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300" href="/groceries/">Explore <!-- -->Groceries<svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></a></div></div><div class="group relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden cursor-pointer transform hover:-translate-y-2 "><div class="h-48 bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center"><span class="text-6xl">💼</span></div><div class="p-6"><h3 class="text-xl font-bold text-gray-900 mb-2">Jobs</h3><p class="text-gray-600 mb-4">Discover career opportunities that match your skills</p><ul class="space-y-1 mb-4"><li class="text-sm text-gray-500 flex items-center"><span class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>Job Search</li><li class="text-sm text-gray-500 flex items-center"><span class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>Career Guidance</li><li class="text-sm text-gray-500 flex items-center"><span class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>Resume Builder</li><li class="text-sm text-gray-500 flex items-center"><span class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>Interview Prep</li></ul><a class="inline-flex items-center text-white bg-gradient-to-r from-purple-500 to-purple-600 px-4 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300" href="/jobs/">Explore <!-- -->Jobs<svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></a></div></div><div class="group relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden cursor-pointer transform hover:-translate-y-2 "><div class="h-48 bg-gradient-to-br from-orange-500 to-orange-600 flex items-center justify-center"><span class="text-6xl">🛠️</span></div><div class="p-6"><h3 class="text-xl font-bold text-gray-900 mb-2">Online Tools</h3><p class="text-gray-600 mb-4">Powerful tools to boost your productivity</p><ul class="space-y-1 mb-4"><li class="text-sm text-gray-500 flex items-center"><span class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>PDF Tools</li><li class="text-sm text-gray-500 flex items-center"><span class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>Image Editor</li><li class="text-sm text-gray-500 flex items-center"><span class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>Calculators</li><li class="text-sm text-gray-500 flex items-center"><span class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>Converters</li></ul><a class="inline-flex items-center text-white bg-gradient-to-r from-orange-500 to-orange-600 px-4 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300" href="/tools/">Explore <!-- -->Online Tools<svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></a></div></div></div><div class="bg-white rounded-2xl shadow-xl overflow-hidden"><div class="grid grid-cols-1 lg:grid-cols-2"><div class="p-8 lg:p-12"><div class="flex items-center mb-4"><span class="text-4xl mr-4">🏠</span><h3 class="text-2xl font-bold text-gray-900">Real Estate</h3></div><p class="text-gray-600 mb-6 text-lg">Find your dream home or investment property</p><div class="grid grid-cols-2 gap-4 mb-8"><div class="flex items-center"><div class="w-3 h-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full mr-3"></div><span class="text-gray-700">Buy Properties</span></div><div class="flex items-center"><div class="w-3 h-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full mr-3"></div><span class="text-gray-700">Rent Homes</span></div><div class="flex items-center"><div class="w-3 h-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full mr-3"></div><span class="text-gray-700">PG Accommodation</span></div><div class="flex items-center"><div class="w-3 h-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full mr-3"></div><span class="text-gray-700">Sell Property</span></div></div><a class="inline-flex items-center text-white bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300" href="/">Get Started<svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path></svg></a></div><div class="bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center min-h-[300px]"><span class="text-8xl opacity-20">🏠</span></div></div></div></div></section><section class="py-16 px-4 sm:px-6 lg:px-8 bg-white"><div class="max-w-7xl mx-auto"><div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center"><div><div class="text-3xl font-bold text-blue-600 mb-2">10K+</div><div class="text-gray-600">Properties Listed</div></div><div><div class="text-3xl font-bold text-green-600 mb-2">5K+</div><div class="text-gray-600">Grocery Orders</div></div><div><div class="text-3xl font-bold text-purple-600 mb-2">2K+</div><div class="text-gray-600">Jobs Posted</div></div><div><div class="text-3xl font-bold text-orange-600 mb-2">50+</div><div class="text-gray-600">Online Tools</div></div></div></div></section><footer class="bg-gray-900 text-white py-16 px-4 sm:px-6 lg:px-8"><div class="max-w-7xl mx-auto"><div class="grid grid-cols-1 md:grid-cols-4 gap-8"><div class="md:col-span-1"><a class="text-2xl font-bold mb-4 block" href="/okayy/">Okayy<span class="text-blue-400">.in</span></a><p class="text-gray-400 mb-4">Your one-stop destination for real estate, groceries, jobs, and online tools.</p><div class="flex space-x-4"><a href="#" class="text-gray-400 hover:text-white"><svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"></path></svg></a><a href="#" class="text-gray-400 hover:text-white"><svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"></path></svg></a></div></div><div><h3 class="text-lg font-semibold mb-4">Services</h3><ul class="space-y-2"><li><a class="text-gray-400 hover:text-white" href="/">Real Estate</a></li><li><a class="text-gray-400 hover:text-white" href="/groceries/">Groceries</a></li><li><a class="text-gray-400 hover:text-white" href="/jobs/">Jobs</a></li><li><a class="text-gray-400 hover:text-white" href="/tools/">Online Tools</a></li></ul></div><div><h3 class="text-lg font-semibold mb-4">Company</h3><ul class="space-y-2"><li><a class="text-gray-400 hover:text-white" href="/about/">About Us</a></li><li><a class="text-gray-400 hover:text-white" href="/contact/">Contact</a></li><li><a class="text-gray-400 hover:text-white" href="/careers/">Careers</a></li><li><a class="text-gray-400 hover:text-white" href="/privacy/">Privacy Policy</a></li></ul></div><div><h3 class="text-lg font-semibold mb-4">Contact</h3><ul class="space-y-2 text-gray-400"><li>📧 <EMAIL></li><li>📞 +91 9876543210</li><li>📍 Hyderabad, India</li></ul></div></div><div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400"><p>© 2024 Okayy.in. All rights reserved.</p></div></div></footer></main><!--$--><!--/$--><script src="/_next/static/chunks/webpack-22534597e9bd48b5.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n4:I[894,[],\"ClientPageRoot\"]\n5:I[6824,[\"874\",\"static/chunks/874-8e9a565f7eb17c9e.js\",\"606\",\"static/chunks/app/okayy/page-18b372a33a2ff156.js\"],\"default\"]\n8:I[9665,[],\"OutletBoundary\"]\nb:I[4911,[],\"AsyncMetadataOutlet\"]\nd:I[9665,[],\"ViewportBoundary\"]\nf:I[9665,[],\"MetadataBoundary\"]\n11:I[6614,[],\"\"]\n:HL[\"/_next/static/css/6650b15f75106e01.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"If7WSLwPzINw5uwLfUYWD\",\"p\":\"\",\"c\":[\"\",\"okayy\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"okayy\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/6650b15f75106e01.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"scroll-smooth\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_e8ce0c font-sans bg-background text-text-primary antialiased\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"okayy\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@6\",\"$@7\"]}],null,[\"$\",\"$L8\",null,{\"children\":[\"$L9\",\"$La\",[\"$\",\"$Lb\",null,{\"promise\":\"$@c\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"jOZU-lYBATXRhyGoklhuOv\",{\"children\":[[\"$\",\"$Ld\",null,{\"children\":\"$Le\"}],null]}],[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"12:\"$Sreact.suspense\"\n13:I[4911,[],\"AsyncMetadata\"]\n6:{}\n7:{}\n10:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$12\",null,{\"fallback\":null,\"children\":[\"$\",\"$L13\",null,{\"promise\":\"$@14\"}]}]}]\n"])</script><script>self.__next_f.push([1,"a:null\n"])</script><script>self.__next_f.push([1,"e:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,"c:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Real Estate India - Buy, Sell, and Rent Properties\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities, or list your property with us. Expert guidance for all your property needs.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"Real Estate India\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"real estate India,property for sale,property for rent,buy property,sell property,apartments,houses,villas,commercial property\"}],[\"$\",\"meta\",\"4\",{\"name\":\"creator\",\"content\":\"Real Estate India\"}],[\"$\",\"meta\",\"5\",{\"name\":\"publisher\",\"content\":\"Real Estate India\"}],[\"$\",\"meta\",\"6\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"7\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"link\",\"8\",{\"rel\":\"canonical\",\"href\":\"https://realestate-india.com/\"}],[\"$\",\"meta\",\"9\",{\"name\":\"format-detection\",\"content\":\"telephone=no, address=no, email=no\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:title\",\"content\":\"Real Estate India - Buy, Sell, and Rent Properties\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:description\",\"content\":\"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities.\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:url\",\"content\":\"https://realestate-india.com/\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:site_name\",\"content\":\"Real Estate India\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:locale\",\"content\":\"en_IN\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:creator\",\"content\":\"@realestateindia\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:title\",\"content\":\"Real Estate India - Buy, Sell, and Rent Properties\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:description\",\"content\":\"Find your dream home in India with our comprehensive real estate platform.\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"14:{\"metadata\":\"$c:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>