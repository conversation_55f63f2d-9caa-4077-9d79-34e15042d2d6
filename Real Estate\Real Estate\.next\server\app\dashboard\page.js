(()=>{var e={};e.id=105,e.ids=[105],e.modules={559:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Music\\\\Real Estate\\\\Real Estate\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\dashboard\\page.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1324:(e,t,s)=>{Promise.resolve().then(s.bind(s,8061))},1957:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>o});var r=s(5239),a=s(8088),l=s(8170),i=s.n(l),n=s(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let o={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,559)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\dashboard\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4892:(e,t,s)=>{Promise.resolve().then(s.bind(s,559))},6189:(e,t,s)=>{"use strict";var r=s(5773);s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},8061:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(687),a=s(3210),l=s(6189),i=s(9190),n=s(1317),d=s(5814),o=s.n(d);function c(){(0,l.useRouter)();let[e,t]=(0,a.useState)(null),[s,d]=(0,a.useState)([]),[c,p]=(0,a.useState)([]),[x,m]=(0,a.useState)([]),[h,u]=(0,a.useState)(0),[g,b]=(0,a.useState)("all");return null===e?(0,r.jsx)("main",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})}):e.user?(0,r.jsxs)("main",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(i.Navbar,{}),(0,r.jsxs)("div",{className:"container-custom py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Welcome to Your Dashboard!"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"Manage your properties, inquiries, and saved listings here."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Total Properties"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-primary",children:s.length}),(0,r.jsx)("p",{className:"text-gray-600",children:"Your listed properties"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Approved Properties"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-green-600",children:s.filter(e=>"APPROVED"===e.approvalStatus).length}),(0,r.jsx)("p",{className:"text-gray-600",children:"Properties approved by admin"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Pending Properties"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-yellow-600",children:s.filter(e=>"PENDING"===e.approvalStatus).length}),(0,r.jsx)("p",{className:"text-gray-600",children:"Properties awaiting approval"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Rejected Properties"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-red-600",children:s.filter(e=>"REJECTED"===e.approvalStatus).length}),(0,r.jsx)("p",{className:"text-gray-600",children:"Properties rejected by admin"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Inquiries Received"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-primary",children:c.length}),(0,r.jsx)("p",{className:"text-gray-600",children:"Inquiries on your properties"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Saved Properties"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-primary",children:x.length}),(0,r.jsx)("p",{className:"text-gray-600",children:"Properties you have saved"})]})]}),(0,r.jsxs)("div",{className:"mt-8 bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Your Listed Properties"}),(0,r.jsxs)("div",{className:"flex space-x-4 mb-4",children:[(0,r.jsxs)("button",{onClick:()=>b("all"),className:`px-4 py-2 rounded-md text-sm font-medium ${"all"===g?"bg-primary text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:["All (",s.length,")"]}),(0,r.jsxs)("button",{onClick:()=>b("approved"),className:`px-4 py-2 rounded-md text-sm font-medium ${"approved"===g?"bg-primary text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:["Approved (",s.filter(e=>"APPROVED"===e.approvalStatus).length,")"]}),(0,r.jsxs)("button",{onClick:()=>b("pending"),className:`px-4 py-2 rounded-md text-sm font-medium ${"pending"===g?"bg-primary text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:["Pending (",s.filter(e=>"PENDING"===e.approvalStatus).length,")"]}),(0,r.jsxs)("button",{onClick:()=>b("rejected"),className:`px-4 py-2 rounded-md text-sm font-medium ${"rejected"===g?"bg-primary text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:["Rejected (",s.filter(e=>"REJECTED"===e.approvalStatus).length,")"]})]}),0===s.filter(e=>"approved"===g?"APPROVED"===e.approvalStatus:"pending"===g?"PENDING"===e.approvalStatus:"rejected"!==g||"REJECTED"===e.approvalStatus).length?(0,r.jsx)("p",{className:"text-gray-600",children:"No properties found for the selected status."}):(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:s.filter(e=>"approved"===g?"APPROVED"===e.approvalStatus:"pending"===g?"PENDING"===e.approvalStatus:"rejected"!==g||"REJECTED"===e.approvalStatus).map(e=>{let t="string"==typeof e.images?JSON.parse(e.images):e.images;return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,r.jsx)("img",{src:t[0],alt:e.title,className:"w-full h-48 object-cover rounded-md mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-1",children:e.title}),(0,r.jsxs)("p",{className:"text-primary font-bold",children:[e.currency," ",e.price.toLocaleString()]}),(0,r.jsxs)("p",{className:"text-gray-600 text-sm",children:[e.type," in ",e.city,", ",e.state]}),(0,r.jsxs)("p",{className:"text-gray-500 text-xs mt-2",children:["Status: ",e.approvalStatus]}),(0,r.jsxs)("p",{className:"text-gray-500 text-xs",children:["Views: ",e.viewCount]}),(0,r.jsxs)("p",{className:"text-gray-500 text-xs",children:["Inquiries: ",e._count.inquiries]}),(0,r.jsxs)("p",{className:"text-gray-500 text-xs",children:["Saved By: ",e._count.savedBy]}),(0,r.jsx)(o(),{href:`/properties/${e.id}`,className:"text-blue-500 hover:underline text-sm mt-2 block",children:"View Details"})]},e.id)})})]}),(0,r.jsxs)("div",{className:"mt-8 bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(o(),{href:"/properties/create/",className:"btn-primary text-center",children:"List New Property"}),(0,r.jsx)(o(),{href:"/properties/",className:"btn-secondary text-center",children:"Browse Properties"}),(0,r.jsx)("button",{onClick:()=>u(e=>e+1),className:"btn-secondary text-center",children:"Refresh Dashboard Data"})]})]})]}),(0,r.jsx)(n.w,{})]}):null}s(216)},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[771,814,604,317],()=>s(1957));module.exports=r})();