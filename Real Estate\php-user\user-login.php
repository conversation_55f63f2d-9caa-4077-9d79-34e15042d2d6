<?php
session_start();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';

    try {
        $db = new PDO('sqlite:../Real Estate/prisma/dev.db');
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $stmt = $db->prepare('SELECT * FROM User WHERE email = :email');
        $stmt->execute(['email' => $email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user && password_verify($password, $user['password'])) {
            if ($user['role'] === 'ADMIN') {
                header('Location: http://localhost:3000/login?error=Admins%20must%20use%20the%20admin%20login%20page.');
                exit;
            }
            $_SESSION['user_logged_in'] = true;
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['user_name'] = $user['name'];
            
            // Debug: Log the redirect
            error_log('User login successful, redirecting to: http://localhost:8000/redirect-to-dashboard.php');
            
            header('Location: http://localhost:8000/redirect-to-dashboard.php');
            exit;
        } else {
            error_log('Login failed for email: ' . $email . ' - Invalid credentials');
            header('Location: http://localhost:3000/login?error=Invalid%20credentials');
            exit;
        }
    } catch (PDOException $e) {
        error_log('Database error during login for email: ' . $email . ' - ' . $e->getMessage());
        header('Location: http://localhost:3000/login?error=Database%20error');
        exit;
    }
} else {
    header('Location: http://localhost:3000/login');
    exit;
}
?>