{"name": "real-estate-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:seed": "tsx prisma/seed.ts"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.10.1", "autoprefixer": "^10.4.16", "bcryptjs": "^3.0.2", "eslint": "^8.54.0", "eslint-config-next": "^15.0.0", "jsonwebtoken": "^9.0.2", "next": "^15.0.0", "next-auth": "^4.24.11", "postcss": "^8.4.31", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwindcss": "^3.3.5", "typescript": "^5.3.0"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "24.0.3", "@types/react": "19.1.8", "prisma": "^6.10.1", "tsx": "^4.20.3"}}