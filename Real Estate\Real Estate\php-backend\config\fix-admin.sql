-- Fix Admin User for Real Estate Website
-- Run this SQL in phpMyAdmin to create/update admin user

-- First, delete any existing admin user to avoid conflicts
DELETE FROM users WHERE email = '<EMAIL>';

-- Create new admin user with correct password hash
INSERT INTO users (id, name, email, password, role, is_active, created_at) VALUES 
(
    'admin-user-2024', 
    'Admin User', 
    '<EMAIL>', 
    '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VJunLVlm2', 
    'ADMIN', 
    1, 
    NOW()
);

-- Verify the admin user was created
SELECT id, name, email, role, is_active FROM users WHERE email = '<EMAIL>';

-- Admin Login Credentials:
-- Email: <EMAIL>
-- Password: admin123
