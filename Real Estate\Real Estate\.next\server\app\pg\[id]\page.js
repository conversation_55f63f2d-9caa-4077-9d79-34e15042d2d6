(()=>{var e={};e.id=172,e.ids=[172],e.modules={163:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=t(1042).unstable_rethrow;("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},899:(e,r,t)=>{"use strict";function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"unauthorized",{enumerable:!0,get:function(){return n}}),t(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},1042:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"unstable_rethrow",{enumerable:!0,get:function(){return function e(r){if((0,a.isNextRouterError)(r)||(0,i.isBailoutToCSRError)(r)||(0,l.isDynamicServerError)(r)||(0,o.isDynamicPostpone)(r)||(0,s.isPostpone)(r)||(0,n.isHangingPromiseRejectionError)(r))throw r;r instanceof Error&&"cause"in r&&e(r.cause)}}});let n=t(8388),s=t(2637),i=t(1846),a=t(1162),o=t(4971),l=t(8479);("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},2549:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.t.bind(t,9603,23)),Promise.resolve().then(t.bind(t,4))},2765:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"notFound",{enumerable:!0,get:function(){return s}});let n=""+t(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function s(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},2821:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,5814,23)),Promise.resolve().then(t.t.bind(t,6533,23)),Promise.resolve().then(t.bind(t,9190))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4705:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f,generateMetadata:()=>u,generateStaticParams:()=>c});var n=t(7413),s=t(3384),i=t(4536),a=t.n(i),o=t(4),l=t(8659),d=t(9916);async function c(){return[{id:"cmcyaig6u0001uoi4x0osc5ik"},{id:"prop_6876410a3fbaf"},{id:"prop_6876410a44045"}]}async function u({params:e}){let r=await e,t=await p(r.id);return t?{title:`${t.title} - PG Accommodation - Real Estate`,description:t.description}:{title:"PG Accommodation Not Found"}}async function p(e){try{let r=await fetch(`https://housing.okayy.in/php-backend/api/properties/get.php?id=${e}`,{method:"GET",headers:{"Content-Type":"application/json"},cache:"no-store"});if(!r.ok)return null;let t=await r.json();if(t.success&&t.property&&"PG"===t.property.type)return t.property;return null}catch(e){return console.error("Error fetching PG property:",e),null}}async function f({params:e}){let r,t=await e,i=await p(t.id);i||(0,d.notFound)();let c="string"==typeof i.images?JSON.parse(i.images):i.images,u="string"==typeof i.amenities?JSON.parse(i.amenities):i.amenities;return(0,n.jsxs)("main",{className:"min-h-screen bg-gray-50",children:[(0,n.jsx)(o.Navbar,{}),(0,n.jsx)("section",{className:"relative",children:(0,n.jsx)("div",{className:"container-custom py-8",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:[(0,n.jsx)("div",{className:"relative h-96 lg:h-[500px] rounded-2xl overflow-hidden",children:(0,n.jsx)(s.default,{src:c[0]||"/placeholder-property.jpg",alt:i.title,fill:!0,className:"object-cover",priority:!0})}),(0,n.jsx)("div",{className:"grid grid-cols-2 gap-4",children:c.slice(1,5).map((e,r)=>(0,n.jsx)("div",{className:"relative h-44 lg:h-60 rounded-xl overflow-hidden",children:(0,n.jsx)(s.default,{src:e,alt:`${i.title} - Image ${r+2}`,fill:!0,className:"object-cover"})},r))})]})})}),(0,n.jsx)("section",{className:"py-12",children:(0,n.jsx)("div",{className:"container-custom",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-12",children:[(0,n.jsx)("div",{className:"lg:col-span-2",children:(0,n.jsxs)("div",{className:"bg-white rounded-2xl shadow-large p-8",children:[(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:i.title}),(0,n.jsxs)("div",{className:"flex items-center text-gray-600 mb-4",children:[(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,n.jsxs)("span",{children:[i.address,", ",i.city,", ",i.state]})]}),(0,n.jsxs)("div",{className:"text-4xl font-bold text-primary mb-6",children:[(r=i.price,new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:0}).format(r)),"/month"]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6 mb-8",children:[(0,n.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-primary",children:i.view_count}),(0,n.jsx)("div",{className:"text-gray-600",children:"Views"})]}),i.area&&(0,n.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-primary",children:i.area}),(0,n.jsx)("div",{className:"text-gray-600",children:"Sq Ft"})]}),(0,n.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-primary",children:i.type}),(0,n.jsx)("div",{className:"text-gray-600",children:"Type"})]})]}),(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Description"}),(0,n.jsx)("p",{className:"text-gray-600 leading-relaxed",children:i.description})]}),(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Amenities"}),(0,n.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:u.map((e,r)=>(0,n.jsxs)("div",{className:"flex items-center p-3 bg-gray-50 rounded-xl",children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-primary mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,n.jsx)("span",{className:"text-gray-700",children:e})]},r))})]})]})}),(0,n.jsx)("div",{className:"lg:col-span-1",children:(0,n.jsxs)("div",{className:"bg-white rounded-2xl shadow-large p-6 sticky top-8",children:[(0,n.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"Contact Agent"}),(0,n.jsx)("div",{className:"mb-6",children:(0,n.jsxs)("div",{className:"flex items-center mb-3",children:[(0,n.jsx)("div",{className:"w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-3",children:(0,n.jsx)("span",{className:"text-white font-semibold",children:i.owner_name?.charAt(0)||"A"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"font-semibold text-gray-900",children:i.owner_name||"N/A"}),(0,n.jsx)("div",{className:"text-gray-600 text-sm",children:"Property Agent"})]})]})}),(0,n.jsxs)("form",{className:"space-y-4",children:[(0,n.jsx)("div",{children:(0,n.jsx)("input",{type:"text",placeholder:"Your Name",className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"})}),(0,n.jsx)("div",{children:(0,n.jsx)("input",{type:"email",placeholder:"Your Email",className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"})}),(0,n.jsx)("div",{children:(0,n.jsx)("input",{type:"tel",placeholder:"Your Phone",className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"})}),(0,n.jsx)("div",{children:(0,n.jsx)("textarea",{rows:4,placeholder:"I'm interested in this property. Please contact me with more information.",className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"})}),(0,n.jsx)("button",{type:"submit",className:"w-full btn-primary",children:"Send Message"})]}),(0,n.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:(0,n.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[(0,n.jsx)("span",{children:"Listed on:"}),(0,n.jsx)("span",{children:new Date(i.createdAt).toLocaleDateString()})]})})]})})]})})}),(0,n.jsx)("section",{className:"py-12 bg-gray-100",children:(0,n.jsxs)("div",{className:"container-custom",children:[(0,n.jsx)("h2",{className:"text-2xl md:text-3xl font-bold mb-8",children:"Similar PG Accommodations"}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:(0,n.jsxs)("div",{className:"col-span-full text-center py-8",children:[(0,n.jsx)("p",{className:"text-gray-500",children:"Similar PG accommodations will be displayed here"}),(0,n.jsx)(a(),{href:"/pg/",className:"text-primary hover:underline mt-2 inline-block",children:"Browse All PG Accommodations"})]})})]})}),(0,n.jsx)(l.w,{})]})}},6897:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{getRedirectError:function(){return a},getRedirectStatusCodeFromError:function(){return u},getRedirectTypeFromError:function(){return c},getURLFromRedirectError:function(){return d},permanentRedirect:function(){return l},redirect:function(){return o}});let n=t(2836),s=t(9026),i=t(9121).actionAsyncStorage;function a(e,r,t){void 0===t&&(t=n.RedirectStatusCode.TemporaryRedirect);let i=Object.defineProperty(Error(s.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return i.digest=s.REDIRECT_ERROR_CODE+";"+r+";"+e+";"+t+";",i}function o(e,r){var t;throw null!=r||(r=(null==i||null==(t=i.getStore())?void 0:t.isAction)?s.RedirectType.push:s.RedirectType.replace),a(e,r,n.RedirectStatusCode.TemporaryRedirect)}function l(e,r){throw void 0===r&&(r=s.RedirectType.replace),a(e,r,n.RedirectStatusCode.PermanentRedirect)}function d(e){return(0,s.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function c(e){if(!(0,s.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function u(e){if(!(0,s.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},7576:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{ReadonlyURLSearchParams:function(){return c},RedirectType:function(){return s.RedirectType},forbidden:function(){return a.forbidden},notFound:function(){return i.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return o.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let n=t(6897),s=t(9026),i=t(2765),a=t(8976),o=t(899),l=t(163);class d extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class c extends URLSearchParams{append(){throw new d}delete(){throw new d}set(){throw new d}sort(){throw new d}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},8976:(e,r,t)=>{"use strict";function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"forbidden",{enumerable:!0,get:function(){return n}}),t(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9687:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var n=t(5239),s=t(8088),i=t(8170),a=t.n(i),o=t(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["pg",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4705)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\pg\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\pg\\[id]\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/pg/[id]/page",pathname:"/pg/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9916:(e,r,t)=>{"use strict";var n=t(7576);t.o(n,"notFound")&&t.d(r,{notFound:function(){return n.notFound}})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[771,814,533,940,604,722],()=>t(9687));module.exports=n})();