'use client'

import { useState, useEffect, useCallback, useRef } from 'react';
import { Navbar } from '@/components/Navbar';
import { Footer } from '@/components/Footer';
import { SearchBar } from '@/components/SearchBar';
import { PropertyCard } from '@/components/PropertyCard';
import { PropertyFilters } from '@/components/PropertyFilters';
import { propertiesAPI } from '@/config/api';
// import propertiesData from '@/data/properties.json';

interface Property {
  id: string;
  title: string;
  description: string;
  price: number;
  currency: string;
  type: string;
  bedrooms: number | null;
  bathrooms: number | null;
  area: number;
  address: string;
  city: string;
  state: string;
  images: string;
  amenities: string;
  owner: {
    name: string;
    email: string;
    phone?: string;
  };
  createdAt: string;
}

interface PropertiesResponse {
  properties: Property[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export default function PropertiesPage() {
  const [properties, setProperties] = useState<Property[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 12,
    total: 0,
    pages: 0
  });
  const [filters, setFilters] = useState({
    type: '',
    minPrice: '',
    maxPrice: '',
    bedrooms: '',
    city: '',
    // PG-specific filters
    roomType: '',
    sharing: '',
    foodIncluded: '',
    gender: ''
  });

  const resultsRef = useRef<HTMLElement>(null);

  // Read URL parameters on component mount and fetch properties
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const initialFilters = {
        type: urlParams.get('type') || '',
        minPrice: urlParams.get('minPrice') || '',
        maxPrice: urlParams.get('maxPrice') || '',
        bedrooms: urlParams.get('bedrooms') || '',
        city: urlParams.get('city') || '',
        // PG-specific filters
        roomType: urlParams.get('roomType') || '',
        sharing: urlParams.get('sharing') || '',
        foodIncluded: urlParams.get('foodIncluded') || '',
        gender: urlParams.get('gender') || ''
      };
      setFilters(initialFilters);

      // Fetch properties with initial filters
      fetchProperties(initialFilters, 1);

      // If there are search parameters, scroll to results after a delay
      const hasSearchParams = Array.from(urlParams.values()).some(value => value !== '');
      if (hasSearchParams) {
        setTimeout(() => {
          scrollToResults();
        }, 500); // Delay to allow page to render
      }
    }
  }, []);

  // Fetch properties when filters change
  useEffect(() => {
    if (pagination.page && pagination.limit) {
      console.log('Filters changed, fetching properties:', filters);
      fetchProperties();
    }
  }, [filters]);

  // Fetch properties when pagination changes (filters are handled by PropertyFilters component)
  useEffect(() => {
    if (pagination.page && pagination.limit && pagination.page > 1) {
      fetchProperties();
    }
  }, [pagination.page]);

  // Listen for search updates from SearchBar component
  useEffect(() => {
    const handleSearchUpdate = (event: CustomEvent) => {
      const url = new URL(event.detail.url, window.location.origin);
      const urlParams = new URLSearchParams(url.search);
      const newFilters = {
        type: urlParams.get('type') || '',
        minPrice: urlParams.get('minPrice') || '',
        maxPrice: urlParams.get('maxPrice') || '',
        bedrooms: urlParams.get('bedrooms') || '',
        city: urlParams.get('city') || '',
        // PG-specific filters
        roomType: urlParams.get('roomType') || '',
        sharing: urlParams.get('sharing') || '',
        foodIncluded: urlParams.get('foodIncluded') || '',
        gender: urlParams.get('gender') || ''
      };
      setFilters(newFilters);
      setPagination(prev => ({ ...prev, page: 1 }));
    };

    window.addEventListener('searchUpdate', handleSearchUpdate as EventListener);
    return () => {
      window.removeEventListener('searchUpdate', handleSearchUpdate as EventListener);
    };
  }, []);

  const fetchProperties = async (customFilters?: any, customPage?: number) => {
    // Use custom parameters or fall back to state
    const currentFilters = customFilters || filters;
    const currentPage = customPage || pagination.page;
    const currentLimit = pagination.limit;

    // Guard clause to ensure pagination is initialized
    if (!currentPage || !currentLimit) {
      return;
    }

    console.log('Fetching properties with filters:', currentFilters, 'page:', currentPage);
    setLoading(true);
    try {
      // Filter out empty values from filters
      const cleanFilters = Object.entries(currentFilters).reduce((acc, [key, value]) => {
        if (value && typeof value === 'string' && value.trim() !== '') {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, string>);

      console.log('Clean filters being sent to API:', cleanFilters);

      const filterParams = {
        page: currentPage,
        limit: currentLimit,
        ...cleanFilters,
      };

      console.log('API filter params:', filterParams);

      const data: PropertiesResponse = await propertiesAPI.getProperties(filterParams);

      console.log('API response:', data);

      setProperties(data.properties);
      setPagination(data.pagination);
    } catch (error: any) {
      console.error('Error loading properties:', error);
      setError(error.message || 'Failed to load properties.');
    } finally {
      setLoading(false);
    }
  };

  const scrollToResults = () => {
    if (resultsRef.current) {
      // Calculate the offset to account for navbar height
      const navbarHeight = 80; // Approximate navbar height
      const elementPosition = resultsRef.current.offsetTop - navbarHeight;

      window.scrollTo({
        top: elementPosition,
        behavior: 'smooth'
      });
    }
  };

  const handleFilterChange = useCallback((newFilters: any) => {
    setFilters(newFilters);
    setPagination(prev => ({ ...prev, page: 1 }));

    // Immediately fetch properties with new filters
    fetchProperties(newFilters, 1);

    // Scroll to results after a short delay
    setTimeout(() => {
      scrollToResults();
    }, 100);
  }, []);

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
    // Scroll to top of results when changing pages
    setTimeout(() => {
      scrollToResults();
    }, 100);
  };

  const formatCurrency = (amount: number, currency: string = 'INR') => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace('₹', '₹');
  };
  return (
    <main className="min-h-screen">
      <Navbar />
      
      {/* Page Header */}
      <section className="bg-gray-100 py-12">
        <div className="container-custom">
          <h1 className="text-3xl md:text-4xl font-bold mb-6">Properties</h1>
          <p className="text-lg text-text-secondary mb-8">
            Browse our extensive collection of properties for sale and rent. Use the filters to find your perfect match.
          </p>
          <SearchBar />
        </div>
      </section>
      
      {/* Properties Section */}
      <section ref={resultsRef} data-results className="py-12">
        <div className="container-custom">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Filters Sidebar */}
            <div className="lg:w-1/4">
              <PropertyFilters onFilterChange={handleFilterChange} />
            </div>
            
            {/* Properties Grid */}
            <div className="lg:w-3/4">
              <div className="flex justify-between items-center mb-6">
                <p className="text-text-secondary">
                  Showing <span className="font-semibold">{properties.length}</span> of <span className="font-semibold">{pagination.total}</span> properties
                </p>
                <div className="flex items-center space-x-2">
                  <label htmlFor="sort" className="text-text-secondary">Sort by:</label>
                  <select
                    id="sort"
                    className="border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    <option value="newest">Newest</option>
                    <option value="price-asc">Price (Low to High)</option>
                    <option value="price-desc">Price (High to Low)</option>
                  </select>
                </div>
              </div>

              {loading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="bg-gray-300 h-48 rounded-lg mb-4"></div>
                      <div className="h-4 bg-gray-300 rounded mb-2"></div>
                      <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                    </div>
                  ))}
                </div>
              ) : properties.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-gray-500 text-lg mb-4">No properties found</div>
                  <p className="text-gray-400">Try adjusting your search filters</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {properties.map((property) => (
                    <PropertyCard key={property.id} property={property} />
                  ))}
                </div>
              )}
              
              {/* Pagination */}
              {pagination.pages > 1 && (
                <div className="mt-12 flex justify-center">
                  <nav className="flex items-center space-x-2">
                    <button
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={pagination.page === 1}
                      className="w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>

                    {[...Array(Math.min(5, pagination.pages))].map((_, i) => {
                      const pageNum = i + 1;
                      return (
                        <button
                          key={pageNum}
                          onClick={() => handlePageChange(pageNum)}
                          className={`w-10 h-10 rounded-md flex items-center justify-center ${
                            pagination.page === pageNum
                              ? 'bg-primary text-white'
                              : 'border border-gray-300 hover:bg-gray-100'
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    })}

                    {pagination.pages > 5 && (
                      <>
                        <span className="text-gray-500">...</span>
                        <button
                          onClick={() => handlePageChange(pagination.pages)}
                          className="w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center hover:bg-gray-100"
                        >
                          {pagination.pages}
                        </button>
                      </>
                    )}

                    <button
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={pagination.page === pagination.pages}
                      className="w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </nav>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>
      
      <Footer />
    </main>
  );
}
