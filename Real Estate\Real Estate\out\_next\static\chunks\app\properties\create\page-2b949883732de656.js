(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[559],{3329:(e,r,s)=>{Promise.resolve().then(s.bind(s,9520))},5695:(e,r,s)=>{"use strict";var a=s(8999);s.o(a,"useRouter")&&s.d(r,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(r,{useSearchParams:function(){return a.useSearchParams}})},9520:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>y});var a=s(5155),l=s(5494),t=s(6821),n=s(2115),i=s(5695),o=s(1008);let c=[{value:"RENT",label:"For Rent"},{value:"SALE",label:"For Sale"}],d=[{value:"APARTMENT",label:"Apartment"},{value:"HOUSE",label:"House"},{value:"VILLA",label:"Villa"},{value:"PLOT",label:"Plot"},{value:"COMMERCIAL",label:"Commercial"},{value:"OFFICE",label:"Office"},{value:"PG",label:"PG (Paying Guest)"}],m=[{value:"FULL_HOUSE",label:"Full House"},{value:"FLAT",label:"Flat"}],u=[{value:"ONE_BHK",label:"1BHK"},{value:"TWO_BHK",label:"2BHK"},{value:"THREE_BHK",label:"3BHK"},{value:"FOUR_BHK",label:"4BHK"}],p=[{value:"SINGLE",label:"Single Occupancy"},{value:"DOUBLE",label:"Double Sharing"},{value:"TRIPLE",label:"Triple Sharing"},{value:"FOUR_SHARING",label:"Four Sharing"},{value:"DORMITORY",label:"Dormitory"}],x=[{value:"MALE",label:"Male Only"},{value:"FEMALE",label:"Female Only"},{value:"MIXED",label:"Co-ed"}],g=["Parking","Swimming Pool","Gym","Garden","Security","Elevator","Power Backup","Water Supply","Internet","Air Conditioning","Balcony","Terrace","Furnished","Semi-Furnished","Unfurnished","WiFi","AC","Food Included","Laundry","TV","Fridge","Common Area"];function h(){let e=(0,i.useRouter)(),[r,s]=(0,n.useState)(!1),[l,t]=(0,n.useState)({title:"",description:"",price:"",currency:"INR",listingType:"",type:"",accommodationType:"",bedrooms:"1",bathrooms:"1",area:"",address:"",city:"",state:"",pincode:"",images:[],amenities:[],pgRoomType:"",pgGenderPreference:""}),h=e=>{let{name:r,value:s}=e.target;t(e=>({...e,[r]:s}))},y=e=>{t(r=>({...r,amenities:r.amenities.includes(e)?r.amenities.filter(r=>r!==e):[...r.amenities,e]}))},b=async e=>{let r=e.target.files;if(r)try{let e=await Promise.all(Array.from(r).map(async e=>{let r=new FormData;r.append("file",e);let s=await fetch("https://housing.okayy.in/php-backend/api/upload/index.php",{method:"POST",body:r,credentials:"include"});if(!s.ok)throw Error("Image upload failed: ".concat(s.status));let a=await s.json();return a.url.startsWith("http")?a.url:"https://housing.okayy.in".concat(a.url)}));t(r=>({...r,images:[...r.images,...e]}))}catch(e){console.error("Image upload error:",e),alert("Failed to upload images. Please try again.")}},f=e=>{t(r=>({...r,images:r.images.filter((r,s)=>s!==e)}))},j=async r=>{if(r.preventDefault(),s(!0),!l.listingType){alert("Please select a listing type (Rent or Sale)"),s(!1);return}if(!l.type){alert("Please select a property type"),s(!1);return}if("PG"===l.type){if(!l.pgRoomType){alert("Please select a PG room type"),s(!1);return}if(!l.pgGenderPreference){alert("Please select a PG gender preference"),s(!1);return}}else if(!l.accommodationType){alert("Please select an accommodation type"),s(!1);return}try{let r={...l,price:parseInt(l.price)||0,bedrooms:parseInt(l.bedrooms)||1,bathrooms:parseInt(l.bathrooms)||1,area:parseInt(l.area)||0};"PG"===l.type?delete r.accommodationType:(delete r.pgRoomType,delete r.pgGenderPreference),console.log("Submitting property data:",r);let s=await o.M5.createProperty(r);console.log("Response data:",s),s.success?(alert("Property listing created successfully!"),"PG"===l.type?e.push("/pg?success=property-listed"):e.push("/dashboard?success=property-listed")):(console.error("Error response:",s),alert(s.error||s.details||"Failed to create property listing"))}catch(r){console.error("Error creating property:",r),r.message&&r.message.includes("Authentication required")?(alert("Please login to create a property listing"),e.push("/login")):alert(r.message||"Failed to create property listing")}finally{s(!1)}};return(0,a.jsxs)("form",{onSubmit:j,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-2",children:"Property Title *"}),(0,a.jsx)("input",{type:"text",id:"title",name:"title",required:!0,value:l.title,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"e.g., Beautiful 3BHK Apartment in Downtown"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"listingType",className:"block text-sm font-medium text-gray-700 mb-2",children:"Listing Type *"}),(0,a.jsxs)("select",{id:"listingType",name:"listingType",required:!0,value:l.listingType,onChange:e=>{let{value:r}=e.target;t(e=>({...e,listingType:r,accommodationType:"",pgRoomType:"",pgGenderPreference:""}))},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"Select Listing Type"}),c.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"type",className:"block text-sm font-medium text-gray-700 mb-2",children:"Property Type *"}),(0,a.jsxs)("select",{id:"type",name:"type",required:!0,value:l.type,onChange:e=>{let{value:r}=e.target;t(e=>({...e,type:r,accommodationType:"",pgRoomType:"",pgGenderPreference:""}))},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"Select Property Type"}),d.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]})]})]}),"PG"===l.type?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"pgRoomType",className:"block text-sm font-medium text-gray-700 mb-2",children:"Room Type *"}),(0,a.jsxs)("select",{id:"pgRoomType",name:"pgRoomType",required:!0,value:l.pgRoomType,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"Select Room Type"}),p.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"pgGenderPreference",className:"block text-sm font-medium text-gray-700 mb-2",children:"Gender Preference *"}),(0,a.jsxs)("select",{id:"pgGenderPreference",name:"pgGenderPreference",required:!0,value:l.pgGenderPreference,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"Select Gender Preference"}),x.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]})]})]}):l.listingType&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"accommodationType",className:"block text-sm font-medium text-gray-700 mb-2",children:"RENT"===l.listingType?"Accommodation Type *":"BHK Type *"}),(0,a.jsxs)("select",{id:"accommodationType",name:"accommodationType",required:!0,value:l.accommodationType,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"RENT"===l.listingType?"Select Accommodation Type":"Select BHK Type"}),("RENT"===l.listingType?m:"SALE"===l.listingType?u:[]).map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"Description *"}),(0,a.jsx)("textarea",{id:"description",name:"description",required:!0,rows:4,value:l.description,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"Describe your property in detail..."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"price",className:"block text-sm font-medium text-gray-700 mb-2",children:"Price (₹) *"}),(0,a.jsx)("input",{type:"number",id:"price",name:"price",required:!0,min:"0",value:l.price,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"Enter price in ₹"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"bedrooms",className:"block text-sm font-medium text-gray-700 mb-2",children:"Bedrooms"}),(0,a.jsx)("input",{type:"number",id:"bedrooms",name:"bedrooms",min:"0",value:l.bedrooms,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"bathrooms",className:"block text-sm font-medium text-gray-700 mb-2",children:"Bathrooms"}),(0,a.jsx)("input",{type:"number",id:"bathrooms",name:"bathrooms",min:"0",value:l.bathrooms,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"area",className:"block text-sm font-medium text-gray-700 mb-2",children:"Area (sq ft) *"}),(0,a.jsx)("input",{type:"number",id:"area",name:"area",required:!0,min:"0",value:l.area,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"0"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Location Details"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"address",className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Address *"}),(0,a.jsx)("textarea",{id:"address",name:"address",required:!0,rows:2,value:l.address,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"Enter complete address"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"city",className:"block text-sm font-medium text-gray-700 mb-2",children:"City *"}),(0,a.jsx)("input",{type:"text",id:"city",name:"city",required:!0,value:l.city,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"City"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"state",className:"block text-sm font-medium text-gray-700 mb-2",children:"State *"}),(0,a.jsx)("input",{type:"text",id:"state",name:"state",required:!0,value:l.state,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"State"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"pincode",className:"block text-sm font-medium text-gray-700 mb-2",children:"Pincode *"}),(0,a.jsx)("input",{type:"text",id:"pincode",name:"pincode",required:!0,pattern:"[0-9]{6}",value:l.pincode,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"000000"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Property Images"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"images",className:"block text-sm font-medium text-gray-700 mb-2",children:"Upload Images"}),(0,a.jsx)("input",{type:"file",id:"images",multiple:!0,accept:"image/*",onChange:b,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Upload multiple images of your property"})]}),l.images.length>0&&(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:l.images.map((e,r)=>(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("img",{src:e,alt:"Property ".concat(r+1),className:"w-full h-24 object-cover rounded-md"}),(0,a.jsx)("button",{type:"button",onClick:()=>f(r),className:"absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600",children:"\xd7"})]},r))})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Amenities"}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3",children:g.map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:l.amenities.includes(e),onChange:()=>y(e),className:"rounded border-gray-300 text-primary-600 focus:ring-primary-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e})]},e))})]}),(0,a.jsxs)("div",{className:"border-t pt-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)("input",{type:"checkbox",id:"terms",required:!0,className:"rounded border-gray-300 text-primary-600 focus:ring-primary-500"}),(0,a.jsxs)("label",{htmlFor:"terms",className:"text-sm text-gray-700",children:["I agree to the ",(0,a.jsx)("a",{href:"/terms",className:"text-primary-600 hover:underline",children:"Terms and Conditions"})," and confirm that all information provided is accurate."]})]}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-blue-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsxs)("p",{className:"text-sm text-blue-700",children:[(0,a.jsx)("strong",{children:"Note:"})," Your property listing will be reviewed by our team before it goes live. You will receive a notification once it's approved or if any changes are needed."]})})]})}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)("button",{type:"submit",disabled:r,className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:r?"Creating Listing...":"Create Property Listing"})})]})]})}function y(){let[e,r]=(0,n.useState)(null);(0,n.useEffect)(()=>{(async()=>{try{let e=await o.R2.checkSession();r(e)}catch(e){console.error("Session fetch error:",e),r({user:null})}})()},[]);let s=(0,i.useRouter)();return((0,n.useEffect)(()=>{e&&!e.user&&s.push("/login?error=Please%20login%20to%20list%20a%20property")},[e,s]),null===e)?(0,a.jsxs)("main",{className:"min-h-screen",children:[(0,a.jsx)(l.Navbar,{}),(0,a.jsx)("div",{className:"container-custom py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Checking session..."})]})}),(0,a.jsx)(t.w,{})]}):e.user?(0,a.jsxs)("main",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(l.Navbar,{}),(0,a.jsx)("section",{className:"bg-white py-12 border-b",children:(0,a.jsx)("div",{className:"container-custom",children:(0,a.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[(0,a.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"List Your Property for FREE"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6",children:"Reach thousands of potential buyers and renters. Create your property listing in just a few minutes."}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-6 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("svg",{className:"h-5 w-5 text-green-500 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Free to list"]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("svg",{className:"h-5 w-5 text-green-500 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Wide reach"]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("svg",{className:"h-5 w-5 text-green-500 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Easy process"]})]})]})})}),(0,a.jsx)("section",{className:"py-12",children:(0,a.jsx)("div",{className:"container-custom",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Property Details"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Please provide accurate information about your property. All listings are subject to admin approval."})]}),(0,a.jsx)(h,{})]})})})}),(0,a.jsx)(t.w,{})]}):(0,a.jsxs)("main",{className:"min-h-screen",children:[(0,a.jsx)(l.Navbar,{}),(0,a.jsx)("div",{className:"container-custom py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})}),(0,a.jsx)(t.w,{})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[874,494,821,441,684,358],()=>r(3329)),_N_E=e.O()}]);