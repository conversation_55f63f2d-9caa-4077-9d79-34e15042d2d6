import { PrismaClient, PropertyType, ApprovalStatus, ListingType, PGRoomType, PGGenderPreference } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 12)
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin User',
      password: adminPassword,
      role: 'ADMIN',
    },
  })

  // Create sample user
  const userPassword = await bcrypt.hash('user123', 12)
  const user = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON>',
      password: userPassword,
      role: 'USER',
      phone: '+91 9876543210',
    },
  })

  // Sample property data
  const sampleProperties = [
    {
      title: 'Luxury 3BHK Apartment in Bandra West',
      description: 'Spacious 3BHK apartment with modern amenities, sea view, and prime location in Bandra West. Perfect for families looking for comfort and convenience.',
      price: 25000000,
      type: PropertyType.APARTMENT,
      bedrooms: 3,
      bathrooms: 2,
      area: 1200,
      address: '123 Hill Road, Bandra West',
      city: 'Mumbai',
      state: 'Maharashtra',
      pincode: '400050',
      images: JSON.stringify([
        'https://images.unsplash.com/photo-1560518883-ce09059eeffa?q=80&w=1973&auto=format&fit=crop',
        'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?q=80&w=1973&auto=format&fit=crop'
      ]),
      amenities: JSON.stringify(['Swimming Pool', 'Gym', 'Parking', 'Security', 'Garden']),
      isFeatured: true,
      isApproved: true,
      approvalStatus: ApprovalStatus.APPROVED,
      ownerId: user.id,
    },
    {
      title: 'Modern 2BHK Villa in Gurgaon',
      description: 'Beautiful 2BHK villa with garden, modern kitchen, and excellent connectivity to Delhi NCR. Ideal for small families.',
      price: 8500000,
      type: PropertyType.VILLA,
      bedrooms: 2,
      bathrooms: 2,
      area: 1500,
      address: 'Sector 45, DLF Phase 2',
      city: 'Gurgaon',
      state: 'Haryana',
      pincode: '122002',
      images: JSON.stringify([
        'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?q=80&w=1975&auto=format&fit=crop',
        'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?q=80&w=1953&auto=format&fit=crop'
      ]),
      amenities: JSON.stringify(['Garden', 'Parking', 'Security', 'Power Backup']),
      isFeatured: true,
      isApproved: true,
      approvalStatus: ApprovalStatus.APPROVED,
      ownerId: user.id,
    },
    {
      title: 'Spacious 4BHK House in Koramangala',
      description: 'Large 4BHK independent house in the heart of Koramangala, Bangalore. Great for tech professionals with easy access to major IT hubs.',
      price: 15000000,
      type: PropertyType.HOUSE,
      bedrooms: 4,
      bathrooms: 3,
      area: 2000,
      address: '5th Block, Koramangala',
      city: 'Bangalore',
      state: 'Karnataka',
      pincode: '560095',
      images: JSON.stringify([
        'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?q=80&w=1970&auto=format&fit=crop',
        'https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?q=80&w=1970&auto=format&fit=crop'
      ]),
      amenities: JSON.stringify(['Parking', 'Garden', 'Terrace', 'Security']),
      isFeatured: false,
      isApproved: true,
      approvalStatus: ApprovalStatus.APPROVED,
      ownerId: user.id,
    },
    {
      title: 'Commercial Office Space in Connaught Place',
      description: 'Prime commercial office space in the heart of Delhi. Perfect for businesses looking for a prestigious address.',
      price: 35000000,
      type: PropertyType.OFFICE,
      bedrooms: null,
      bathrooms: 2,
      area: 800,
      address: 'Connaught Place, Block A',
      city: 'New Delhi',
      state: 'Delhi',
      pincode: '110001',
      images: JSON.stringify([
        'https://images.unsplash.com/photo-1497366216548-37526070297c?q=80&w=1969&auto=format&fit=crop',
        'https://images.unsplash.com/photo-1497366811353-6870744d04b2?q=80&w=1969&auto=format&fit=crop'
      ]),
      amenities: JSON.stringify(['Elevator', 'Parking', 'Security', 'Power Backup', 'AC']),
      isFeatured: true,
      isApproved: true,
      approvalStatus: ApprovalStatus.APPROVED,
      ownerId: user.id,
    },
    {
      title: 'Affordable 1BHK Apartment in Pune',
      description: 'Compact and well-designed 1BHK apartment perfect for young professionals. Located in a developing area with good connectivity.',
      price: 3500000,
      type: PropertyType.APARTMENT,
      bedrooms: 1,
      bathrooms: 1,
      area: 600,
      address: 'Wakad, Near IT Park',
      city: 'Pune',
      state: 'Maharashtra',
      pincode: '411057',
      images: JSON.stringify([
        'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?q=80&w=1470&auto=format&fit=crop',
        'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?q=80&w=1980&auto=format&fit=crop'
      ]),
      amenities: JSON.stringify(['Gym', 'Parking', 'Security']),
      isFeatured: false,
      isApproved: true,
      approvalStatus: ApprovalStatus.APPROVED,
      ownerId: user.id,
    },
    {
      title: 'Luxury Plot in Hyderabad',
      description: 'Premium residential plot in HITEC City area. Perfect for building your dream home in one of Hyderabad\'s most sought-after locations.',
      price: 12000000,
      type: PropertyType.PLOT,
      bedrooms: null,
      bathrooms: null,
      area: 2400,
      address: 'HITEC City, Madhapur',
      city: 'Hyderabad',
      state: 'Telangana',
      pincode: '500081',
      images: JSON.stringify([
        'https://images.unsplash.com/photo-1500382017468-9049fed747ef?q=80&w=1932&auto=format&fit=crop',
        'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?q=80&w=1971&auto=format&fit=crop'
      ]),
      amenities: JSON.stringify(['Gated Community', 'Security', 'Water Supply', 'Electricity']),
      isFeatured: true,
      isApproved: true,
      approvalStatus: ApprovalStatus.APPROVED,
      ownerId: user.id,
    },
    {
      title: 'Luxury 4BHK Villa in Jubilee Hills',
      description: 'Stunning 4BHK villa with modern amenities in the prestigious Jubilee Hills area. Features include a private garden, swimming pool, and premium finishes.',
      price: 25000000,
      type: PropertyType.VILLA,
      bedrooms: 4,
      bathrooms: 4,
      area: 3500,
      address: 'Road No. 45, Jubilee Hills',
      city: 'Hyderabad',
      state: 'Telangana',
      pincode: '500033',
      images: JSON.stringify([
        'https://images.unsplash.com/photo-1613490493576-7fde63acd811?q=80&w=1471&auto=format&fit=crop',
        'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?q=80&w=1475&auto=format&fit=crop'
      ]),
      amenities: JSON.stringify(['Swimming Pool', 'Garden', 'Parking', 'Security', 'Power Backup']),
      isFeatured: true,
      isApproved: true,
      approvalStatus: ApprovalStatus.APPROVED,
      ownerId: user.id,
    },
    {
      title: 'Modern 3BHK Apartment in Banjara Hills',
      description: 'Contemporary 3BHK apartment in the heart of Banjara Hills with excellent connectivity and modern amenities.',
      price: 15000000,
      type: PropertyType.APARTMENT,
      bedrooms: 3,
      bathrooms: 3,
      area: 2200,
      address: 'Road No. 12, Banjara Hills',
      city: 'Hyderabad',
      state: 'Telangana',
      pincode: '500034',
      images: JSON.stringify([
        'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?q=80&w=1470&auto=format&fit=crop',
        'https://images.unsplash.com/photo-1560518883-ce09059eeffa?q=80&w=1973&auto=format&fit=crop'
      ]),
      amenities: JSON.stringify(['Gym', 'Parking', 'Security', 'Elevator', 'Garden']),
      isFeatured: false,
      isApproved: true,
      approvalStatus: ApprovalStatus.APPROVED,
      ownerId: user.id,
    },
    {
      title: 'Spacious 2BHK Villa in Gachibowli',
      description: 'Well-designed 2BHK villa in the IT hub of Gachibowli, perfect for tech professionals.',
      price: 8500000,
      type: PropertyType.VILLA,
      bedrooms: 2,
      bathrooms: 2,
      area: 1500,
      address: 'HITEC City, Gachibowli',
      city: 'Hyderabad',
      state: 'Telangana',
      pincode: '500032',
      images: JSON.stringify([
        'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?q=80&w=1475&auto=format&fit=crop',
        'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?q=80&w=1970&auto=format&fit=crop'
      ]),
      amenities: JSON.stringify(['Parking', 'Security', 'Garden', 'Power Backup']),
      isFeatured: false,
      isApproved: true,
      approvalStatus: ApprovalStatus.APPROVED,
      ownerId: user.id,
    },
    {
      title: 'Premium 3BHK Apartment in Kondapur (For Rent)',
      description: 'Premium apartment for rent in Kondapur with modern amenities and excellent connectivity.',
      price: 45000,
      type: PropertyType.APARTMENT,
      bedrooms: 3,
      bathrooms: 3,
      area: 1800,
      address: 'Botanical Garden Road, Kondapur',
      city: 'Hyderabad',
      state: 'Telangana',
      pincode: '500084',
      images: JSON.stringify([
        'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?q=80&w=1470&auto=format&fit=crop',
        'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?q=80&w=1980&auto=format&fit=crop'
      ]),
      amenities: JSON.stringify(['Gym', 'Swimming Pool', 'Parking', 'Security', 'Clubhouse']),
      isFeatured: false,
      isApproved: true,
      approvalStatus: ApprovalStatus.APPROVED,
      ownerId: user.id,
    },
    {
      title: 'Modern 1BHK Studio in Madhapur (For Rent)',
      description: 'Compact and modern 1BHK studio apartment in Madhapur, perfect for young professionals.',
      price: 25000,
      type: PropertyType.APARTMENT,
      listingType: ListingType.RENT,
      bedrooms: 1,
      bathrooms: 1,
      area: 650,
      address: 'Ayyappa Society, Madhapur',
      city: 'Hyderabad',
      state: 'Telangana',
      pincode: '500081',
      images: JSON.stringify([
        'https://images.unsplash.com/photo-1493809842364-78817add7ffb?q=80&w=1470&auto=format&fit=crop',
        'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?q=80&w=1470&auto=format&fit=crop'
      ]),
      amenities: JSON.stringify(['Gym', 'Parking', 'Security', 'Wi-Fi']),
      isFeatured: false,
      isApproved: true,
      approvalStatus: ApprovalStatus.APPROVED,
      ownerId: user.id,
    },
    // PG Properties
    {
      title: 'Premium PG for Boys in Gachibowli',
      description: 'Fully furnished PG accommodation for working professionals and students. Includes meals, Wi-Fi, and all amenities.',
      price: 12000,
      type: PropertyType.PG,
      listingType: ListingType.RENT,
      pgRoomType: PGRoomType.DOUBLE,
      pgGenderPreference: PGGenderPreference.MALE,
      bedrooms: 1,
      bathrooms: 1,
      area: 150,
      address: 'Near HITEC City, Gachibowli',
      city: 'Hyderabad',
      state: 'Telangana',
      pincode: '500032',
      images: JSON.stringify([
        'https://images.unsplash.com/photo-1555854877-bab0e564b8d5?q=80&w=1469&auto=format&fit=crop',
        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?q=80&w=1458&auto=format&fit=crop'
      ]),
      amenities: JSON.stringify(['Wi-Fi', 'Food Included', 'Laundry', 'AC', 'Security', 'Parking']),
      isFeatured: true,
      isApproved: true,
      approvalStatus: ApprovalStatus.APPROVED,
      ownerId: user.id,
    },
    {
      title: 'Ladies PG in Madhapur',
      description: 'Safe and secure PG accommodation for working women. Single and double sharing rooms available.',
      price: 15000,
      type: PropertyType.PG,
      listingType: ListingType.RENT,
      pgRoomType: PGRoomType.SINGLE,
      pgGenderPreference: PGGenderPreference.FEMALE,
      bedrooms: 1,
      bathrooms: 1,
      area: 120,
      address: 'Ayyappa Society, Madhapur',
      city: 'Hyderabad',
      state: 'Telangana',
      pincode: '500081',
      images: JSON.stringify([
        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?q=80&w=1458&auto=format&fit=crop',
        'https://images.unsplash.com/photo-1555854877-bab0e564b8d5?q=80&w=1469&auto=format&fit=crop'
      ]),
      amenities: JSON.stringify(['Wi-Fi', 'Food Included', 'Laundry', 'AC', 'Security', 'Common Area']),
      isFeatured: false,
      isApproved: true,
      approvalStatus: ApprovalStatus.APPROVED,
      ownerId: user.id,
    },
    {
      title: 'Co-ed PG in Kukatpally',
      description: 'Modern co-ed PG with separate floors for boys and girls. Great for students and professionals.',
      price: 10000,
      type: PropertyType.PG,
      listingType: ListingType.RENT,
      pgRoomType: PGRoomType.TRIPLE,
      pgGenderPreference: PGGenderPreference.MIXED,
      bedrooms: 1,
      bathrooms: 1,
      area: 100,
      address: 'JNTU Road, Kukatpally',
      city: 'Hyderabad',
      state: 'Telangana',
      pincode: '500085',
      images: JSON.stringify([
        'https://images.unsplash.com/photo-1555854877-bab0e564b8d5?q=80&w=1469&auto=format&fit=crop',
        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?q=80&w=1458&auto=format&fit=crop'
      ]),
      amenities: JSON.stringify(['Wi-Fi', 'Food Included', 'Laundry', 'TV', 'Security', 'Fridge']),
      isFeatured: false,
      isApproved: true,
      approvalStatus: ApprovalStatus.APPROVED,
      ownerId: user.id,
    }
  ]

  // Create sample properties
  for (const propertyData of sampleProperties) {
    const existingProperty = await prisma.property.findFirst({
      where: { title: propertyData.title }
    })

    if (!existingProperty) {
      await prisma.property.create({
        data: propertyData,
      })
    }
  }

  // Create sample blog posts
  const sampleBlogPosts = [
    {
      title: 'Top 10 Tips for First-Time Home Buyers in India',
      slug: 'top-10-tips-first-time-home-buyers-india',
      content: '<p>Buying your first home is an exciting milestone, but it can also be overwhelming. Here are the top 10 tips to help you navigate the Indian real estate market...</p><h2>1. Determine Your Budget</h2><p>Before you start looking at properties, it\'s crucial to determine how much you can afford...</p>',
      excerpt: 'Essential tips and advice for first-time home buyers navigating the Indian real estate market.',
      featuredImage: 'https://images.unsplash.com/photo-1560518883-ce09059eeffa?q=80&w=1973&auto=format&fit=crop',
      published: true,
      tags: JSON.stringify(['Home Buying', 'First Time Buyers', 'Real Estate Tips']),
      category: 'Buying Guide',
      authorId: admin.id,
    },
    {
      title: 'Real Estate Market Trends in Mumbai 2024',
      slug: 'real-estate-market-trends-mumbai-2024',
      content: '<p>Mumbai\'s real estate market continues to evolve in 2024. Here\'s what buyers and investors need to know...</p><h2>Price Trends</h2><p>Property prices in Mumbai have shown steady growth...</p>',
      excerpt: 'Comprehensive analysis of Mumbai\'s real estate market trends and predictions for 2024.',
      featuredImage: 'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?q=80&w=1975&auto=format&fit=crop',
      published: true,
      tags: JSON.stringify(['Market Trends', 'Mumbai', 'Investment']),
      category: 'Market Analysis',
      authorId: admin.id,
    },
    {
      title: 'How to Choose the Right Neighborhood',
      slug: 'how-to-choose-right-neighborhood',
      content: '<p>Choosing the right neighborhood is just as important as choosing the right property. Here\'s what to consider...</p><h2>Location Factors</h2><p>Consider proximity to work, schools, and amenities...</p>',
      excerpt: 'A comprehensive guide to selecting the perfect neighborhood for your new home.',
      featuredImage: 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?q=80&w=1970&auto=format&fit=crop',
      published: true,
      tags: JSON.stringify(['Neighborhood', 'Location', 'Home Buying']),
      category: 'Buying Guide',
      authorId: admin.id,
    }
  ]

  // Create sample blog posts
  for (const blogData of sampleBlogPosts) {
    await prisma.blogPost.upsert({
      where: { slug: blogData.slug },
      update: {},
      create: blogData,
    })
  }

  console.log('Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
