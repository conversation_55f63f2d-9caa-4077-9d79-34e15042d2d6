'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'

interface PGFiltersProps {
  onFilterChange: (filters: any) => void
}

export function PGFilters({ onFilterChange }: PGFiltersProps) {
  const searchParams = useSearchParams()

  const [filters, setFilters] = useState({
    city: '',
    priceRange: '',
    gender: '',
    roomType: '',
    amenities: [] as string[]
  })

  const [expanded, setExpanded] = useState({
    location: true,
    price: true,
    gender: true,
    roomType: true,
    amenities: true,
  })

  const toggleSection = (section: keyof typeof expanded) => {
    setExpanded(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  // Initialize filters from URL parameters on mount
  useEffect(() => {
    const urlCity = searchParams.get('city') || ''
    const urlPriceRange = searchParams.get('priceRange') || ''
    const urlGender = searchParams.get('gender') || ''
    const urlRoomType = searchParams.get('roomType') || ''

    setFilters({
      city: urlCity,
      priceRange: urlPriceRange,
      gender: urlGender,
      roomType: urlRoomType,
      amenities: []
    })
  }, [searchParams])

  // Call onFilterChange when filters change
  useEffect(() => {
    onFilterChange(filters)
  }, [filters])

  const handleAmenityToggle = (amenity: string) => {
    setFilters(prev => ({
      ...prev,
      amenities: prev.amenities.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...prev.amenities, amenity]
    }))
  }

  const clearFilters = () => {
    setFilters({
      city: '',
      priceRange: '',
      gender: '',
      roomType: '',
      amenities: []
    })
  }

  return (
    <div className="bg-white rounded-2xl shadow-soft border border-gray-100 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-text-primary">Filter PG Options</h3>
        <button
          onClick={clearFilters}
          className="text-sm text-primary-600 hover:text-primary-700 font-medium"
        >
          Clear All
        </button>
      </div>

      {/* Location Filter */}
      <div className="mb-6">
        <button
          onClick={() => toggleSection('location')}
          className="flex items-center justify-between w-full text-left font-medium text-text-primary mb-3"
        >
          <span>Location</span>
          <svg
            className={`h-5 w-5 transform transition-transform ${expanded.location ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        
        {expanded.location && (
          <div className="space-y-2">
            <input
              type="text"
              placeholder="Enter city or area"
              value={filters.city}
              onChange={(e) => setFilters(prev => ({ ...prev, city: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
            <div className="flex flex-wrap gap-2 mt-2">
              {['Hyderabad', 'Gachibowli', 'Hitech City', 'Madhapur', 'Kondapur', 'Kukatpally'].map(city => (
                <button
                  key={city}
                  onClick={() => setFilters(prev => ({ ...prev, city }))}
                  className={`px-3 py-1 text-sm rounded-full border transition-colors ${
                    filters.city === city
                      ? 'bg-primary-600 text-white border-primary-600'
                      : 'bg-white text-text-secondary border-gray-300 hover:border-primary-600'
                  }`}
                >
                  {city}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Price Range Filter */}
      <div className="mb-6">
        <button
          onClick={() => toggleSection('price')}
          className="flex items-center justify-between w-full text-left font-medium text-text-primary mb-3"
        >
          <span>Monthly Rent</span>
          <svg
            className={`h-5 w-5 transform transition-transform ${expanded.price ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        
        {expanded.price && (
          <div className="space-y-2">
            {[
              { value: '', label: 'Any Budget' },
              { value: '0-5000', label: 'Under ₹5,000' },
              { value: '5000-10000', label: '₹5,000 - ₹10,000' },
              { value: '10000-15000', label: '₹10,000 - ₹15,000' },
              { value: '15000-20000', label: '₹15,000 - ₹20,000' },
              { value: '20000-', label: 'Above ₹20,000' }
            ].map(range => (
              <label key={range.value} className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="priceRange"
                  value={range.value}
                  checked={filters.priceRange === range.value}
                  onChange={(e) => setFilters(prev => ({ ...prev, priceRange: e.target.value }))}
                  className="form-radio h-4 w-4 text-primary-600"
                />
                <span className="text-sm text-text-secondary">{range.label}</span>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Gender Preference Filter */}
      <div className="mb-6">
        <button
          onClick={() => toggleSection('gender')}
          className="flex items-center justify-between w-full text-left font-medium text-text-primary mb-3"
        >
          <span>Gender Preference</span>
          <svg
            className={`h-5 w-5 transform transition-transform ${expanded.gender ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        
        {expanded.gender && (
          <div className="space-y-2">
            {[
              { value: '', label: 'Any' },
              { value: 'male', label: 'Male Only' },
              { value: 'female', label: 'Female Only' },
              { value: 'mixed', label: 'Co-ed' }
            ].map(option => (
              <label key={option.value} className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="gender"
                  value={option.value}
                  checked={filters.gender === option.value}
                  onChange={(e) => setFilters(prev => ({ ...prev, gender: e.target.value }))}
                  className="form-radio h-4 w-4 text-primary-600"
                />
                <span className="text-sm text-text-secondary">{option.label}</span>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Room Type Filter */}
      <div className="mb-6">
        <button
          onClick={() => toggleSection('roomType')}
          className="flex items-center justify-between w-full text-left font-medium text-text-primary mb-3"
        >
          <span>Room Type</span>
          <svg
            className={`h-5 w-5 transform transition-transform ${expanded.roomType ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        
        {expanded.roomType && (
          <div className="space-y-2">
            {[
              { value: '', label: 'Any Type' },
              { value: 'single', label: 'Single Occupancy' },
              { value: 'double', label: 'Double Sharing' },
              { value: 'triple', label: 'Triple Sharing' },
              { value: 'dormitory', label: 'Dormitory' }
            ].map(type => (
              <label key={type.value} className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="roomType"
                  value={type.value}
                  checked={filters.roomType === type.value}
                  onChange={(e) => setFilters(prev => ({ ...prev, roomType: e.target.value }))}
                  className="form-radio h-4 w-4 text-primary-600"
                />
                <span className="text-sm text-text-secondary">{type.label}</span>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Amenities Filter */}
      <div className="mb-6">
        <button
          onClick={() => toggleSection('amenities')}
          className="flex items-center justify-between w-full text-left font-medium text-text-primary mb-3"
        >
          <span>Amenities</span>
          <svg
            className={`h-5 w-5 transform transition-transform ${expanded.amenities ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        
        {expanded.amenities && (
          <div className="space-y-2">
            {[
              'WiFi', 'AC', 'Food Included', 'Laundry', 'Parking', 'Security',
              'Power Backup', 'Water Supply', 'Gym', 'Common Area', 'TV', 'Fridge'
            ].map(amenity => (
              <label key={amenity} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={filters.amenities.includes(amenity)}
                  onChange={() => handleAmenityToggle(amenity)}
                  className="form-checkbox h-4 w-4 text-primary-600 rounded"
                />
                <span className="text-sm text-text-secondary">{amenity}</span>
              </label>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
