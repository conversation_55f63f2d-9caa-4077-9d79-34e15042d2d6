"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[494],{1008:(e,r,t)=>{t.d(r,{Eo:()=>d,Er:()=>l,M5:()=>i,R2:()=>s,hh:()=>c});let a="/php-backend/api",n={LOGIN:"".concat(a,"/auth/login.php"),SIGNUP:"".concat(a,"/auth/signup.php"),LOGOUT:"".concat(a,"/auth/logout.php"),CHECK_SESSION:"".concat(a,"/auth/check-session.php"),PROPERTIES:"".concat(a,"/properties/index.php"),PROPERTY_BY_ID:e=>"".concat(a,"/properties/get.php?id=").concat(e),BLOG_POSTS:"".concat(a,"/blog/index.php"),BLOG_POST_BY_SLUG:e=>"".concat(a,"/blog/get.php?slug=").concat(e),USER_PROPERTIES:"".concat(a,"/user/properties.php"),USER_INQUIRIES:"".concat(a,"/user/inquiries.php"),ADMIN_PROPERTIES:"".concat(a,"/admin/properties.php"),APPROVE_PROPERTY:e=>"".concat(a,"/admin/approve.php?id=").concat(e),REJECT_PROPERTY:e=>"".concat(a,"/admin/reject.php?id=").concat(e)},o=async function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t={headers:{"Content-Type":"application/json"},credentials:"include"},a={...t,...r,headers:{...t.headers,...r.headers}};try{let r;console.log("API Request:",{url:e,options:a});let t=await fetch(e,a),n=t.headers.get("content-type");if(n&&n.includes("application/json"))r=await t.json();else{let e=await t.text();throw console.error("Non-JSON response:",e),Error("Server returned non-JSON response: ".concat(e.substring(0,200)))}if(console.log("API Response:",{status:t.status,data:r}),!t.ok)throw Error(r.error||"HTTP error! status: ".concat(t.status));return r}catch(r){throw console.error("API request failed:",{url:e,error:r}),r}},s={login:async(e,r)=>o(n.LOGIN,{method:"POST",body:JSON.stringify({email:e,password:r})}),signup:async(e,r,t,a)=>o(n.SIGNUP,{method:"POST",body:JSON.stringify({name:e,email:r,password:t,phone:a})}),logout:async()=>o(n.LOGOUT,{method:"POST"}),checkSession:async()=>o(n.CHECK_SESSION)},i={getProperties:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=new URLSearchParams;return Object.entries(e).forEach(e=>{let[t,a]=e;null!=a&&""!==a&&r.append(t,a.toString())}),o("".concat(n.PROPERTIES,"?").concat(r.toString()))},createProperty:async e=>o(n.PROPERTIES,{method:"POST",body:JSON.stringify(e)}),getPropertyById:async e=>o(n.PROPERTY_BY_ID(e))},l={getProperties:async e=>o(e?"".concat(n.ADMIN_PROPERTIES,"?status=").concat(e):n.ADMIN_PROPERTIES),approveProperty:async e=>o(n.APPROVE_PROPERTY(e),{method:"PUT"}),rejectProperty:async(e,r)=>o(n.REJECT_PROPERTY(e),{method:"PUT",body:JSON.stringify({reason:r})})},c={getPosts:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=new URLSearchParams;return Object.entries(e).forEach(e=>{let[t,a]=e;null!=a&&""!==a&&r.append(t,a.toString())}),o("".concat(n.BLOG_POSTS,"?").concat(r.toString()))},getPostBySlug:async e=>o(n.BLOG_POST_BY_SLUG(e))},d={getProperties:async()=>o(n.USER_PROPERTIES),getInquiries:async()=>o(n.USER_INQUIRIES)}},5494:(e,r,t)=>{t.d(r,{Navbar:()=>l});var a=t(5155),n=t(2115),o=t(6874),s=t.n(o),i=t(1008);function l(){var e,r;let[t,o]=(0,n.useState)(!1),[l,c]=(0,n.useState)({user:null});return(0,n.useEffect)(()=>{(async()=>{try{let e=await i.R2.checkSession();c(e)}catch(e){console.error("Session fetch error:",e),c({user:null})}})()},[]),(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("nav",{className:"bg-white/95 backdrop-blur-md shadow-soft sticky top-0 z-50 border-b border-gray-100",children:(0,a.jsxs)("div",{className:"container-custom py-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)(s(),{href:"/",className:"flex items-center space-x-3 group",children:[(0,a.jsx)("div",{className:"relative w-12 h-12 p-2 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl shadow-soft group-hover:shadow-colored transition-all duration-300 transform group-hover:scale-105"}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-xl font-bold text-gradient",children:"RealEstate"}),(0,a.jsx)("span",{className:"text-xs text-text-tertiary font-medium",children:"India"})]})]}),(0,a.jsxs)("div",{className:"hidden lg:flex items-center space-x-1",children:[(0,a.jsx)(s(),{href:"/",className:"px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",children:"Home"}),(0,a.jsx)(s(),{href:"/buy",className:"px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",children:"Buy"}),(0,a.jsx)(s(),{href:"/rent",className:"px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",children:"Rent"}),(0,a.jsx)(s(),{href:"/pg",className:"px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",children:"PG"}),(0,a.jsx)(s(),{href:"/sell",className:"px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",children:"Sell"})]}),(0,a.jsx)("div",{className:"hidden lg:flex items-center space-x-3",children:l.user?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s(),{href:"/dashboard/",className:"px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",children:"Dashboard"}),"ADMIN"===l.user.role&&(0,a.jsx)(s(),{href:"/admin",className:"px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",children:"Admin Dashboard"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 px-4 py-2 bg-gray-50 rounded-xl",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-semibold",children:(null==(e=l.user.name)?void 0:e.charAt(0).toUpperCase())||"U"})}),(0,a.jsxs)("span",{className:"text-text-secondary font-medium",children:["Hi, ",l.user.name||"User"]})]}),(0,a.jsx)("button",{onClick:async()=>{try{await i.R2.logout(),c({user:null}),window.location.href="/"}catch(e){console.error("Logout error:",e)}},className:"px-4 py-2 text-text-secondary hover:text-error-600 hover:bg-error-50 font-medium rounded-xl transition-all duration-300",children:"Logout"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s(),{href:"/login/",className:"px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",children:"Login"}),(0,a.jsx)(s(),{href:"/signup/",className:"btn-primary",children:"Sign Up"})]})}),(0,a.jsx)("button",{className:"lg:hidden p-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",onClick:()=>o(!t),children:t?(0,a.jsx)("span",{children:"X"}):(0,a.jsx)("span",{children:"☰"})})]}),t&&(0,a.jsx)("div",{className:"lg:hidden mt-6 animate-slide-down",children:(0,a.jsx)("div",{className:"bg-white/95 backdrop-blur-md rounded-2xl shadow-large border border-gray-100 p-6",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,a.jsx)(s(),{href:"/",className:"px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",onClick:()=>o(!1),children:"Home"}),(0,a.jsx)(s(),{href:"/buy",className:"px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",onClick:()=>o(!1),children:"Buy"}),(0,a.jsx)(s(),{href:"/rent",className:"px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",onClick:()=>o(!1),children:"Rent"}),(0,a.jsx)(s(),{href:"/pg",className:"px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",onClick:()=>o(!1),children:"PG"}),(0,a.jsx)(s(),{href:"/sell",className:"px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",onClick:()=>o(!1),children:"Sell"}),(0,a.jsx)("div",{className:"pt-4 mt-4 border-t border-gray-200 flex flex-col space-y-2",children:l.user?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s(),{href:"/dashboard/",className:"px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",onClick:()=>o(!1),children:"Dashboard"}),"ADMIN"===l.user.role&&(0,a.jsx)(s(),{href:"/admin",className:"px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",onClick:()=>o(!1),children:"Admin Dashboard"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 px-4 py-3 bg-gray-50 rounded-xl",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-semibold",children:(null==(r=l.user.name)?void 0:r.charAt(0).toUpperCase())||"U"})}),(0,a.jsxs)("span",{className:"text-text-secondary font-medium",children:["Hi, ",l.user.name||"User"]})]}),(0,a.jsx)("button",{onClick:async()=>{try{await i.R2.logout(),c({user:null}),window.location.href="/"}catch(e){console.error("Logout error:",e)}},className:"px-4 py-2 text-text-secondary hover:text-error-600 hover:bg-error-50 font-medium rounded-xl transition-all duration-300",children:"Logout"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s(),{href:"/login/",className:"px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",onClick:()=>o(!1),children:"Login"}),(0,a.jsx)(s(),{href:"/signup/",className:"btn-primary",children:"Sign Up"})]})})]})})})]})})})}}}]);