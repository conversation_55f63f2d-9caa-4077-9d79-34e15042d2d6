<?php
require_once '../../config/database.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'PUT') {
    sendError('Method not allowed', 405);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if user is admin
    $user = getCurrentUser($db);
    if (!$user || $user['role'] !== 'ADMIN') {
        sendError('Admin access required', 403);
    }
    
    $property_id = $_GET['id'] ?? '';
    if (!$property_id) {
        sendError('Property ID is required', 400);
    }
    
    // Get rejection reason from request body
    $input = json_decode(file_get_contents('php://input'), true);
    $rejection_reason = isset($input['reason']) ? sanitizeInput($input['reason']) : 'No reason provided';
    
    // Check if property exists
    $check_query = "SELECT id FROM properties WHERE id = :id";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bindParam(':id', $property_id);
    $check_stmt->execute();
    
    if (!$check_stmt->fetch()) {
        sendError('Property not found', 404);
    }
    
    // Reject the property
    $update_query = "UPDATE properties SET 
                     approval_status = 'REJECTED', 
                     is_approved = 0, 
                     rejection_reason = :reason,
                     approved_by = :admin_id 
                     WHERE id = :id";
    
    $update_stmt = $db->prepare($update_query);
    $update_stmt->bindParam(':id', $property_id);
    $update_stmt->bindParam(':reason', $rejection_reason);
    $update_stmt->bindParam(':admin_id', $user['id']);
    
    if ($update_stmt->execute()) {
        sendResponse([
            'success' => true,
            'message' => 'Property rejected successfully'
        ]);
    } else {
        sendError('Failed to reject property', 500);
    }
    
} catch (Exception $e) {
    error_log("Reject property error: " . $e->getMessage());
    sendError('Failed to reject property', 500);
}

function getCurrentUser($db) {
    // Get session token
    $session_token = null;
    
    if (isset($_COOKIE['session_token'])) {
        $session_token = $_COOKIE['session_token'];
    } elseif (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $auth_header = $_SERVER['HTTP_AUTHORIZATION'];
        if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
            $session_token = $matches[1];
        }
    }
    
    if (!$session_token) {
        return null;
    }
    
    // Check session
    $query = "SELECT u.* FROM users u 
              JOIN user_sessions s ON u.id = s.user_id 
              WHERE s.session_token = :token AND s.expires_at > NOW()";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':token', $session_token);
    $stmt->execute();
    
    return $stmt->fetch();
}
?>
