'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { popularLocations } from '@/utils/searchUtils';

export function SearchBar() {
  const router = useRouter();
  const [searchType, setSearchType] = useState('buy');
  const [location, setLocation] = useState('');
  const [propertyType, setPropertyType] = useState('');
  const [priceRange, setPriceRange] = useState('');
  const [bedrooms, setBedrooms] = useState('');
  const [pgRoomType, setPgRoomType] = useState('');
  const [pgGender, setPgGender] = useState('');
  const [showLocationSuggestions, setShowLocationSuggestions] = useState(false);
  const [locationSuggestions, setLocationSuggestions] = useState<string[]>([]);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  const locationInputRef = useRef<HTMLInputElement>(null);



  // Handle location input changes and show suggestions
  const handleLocationChange = (value: string) => {
    setLocation(value);
    setSelectedSuggestionIndex(-1);

    if (value.length > 0) {
      const filtered = popularLocations.filter(loc =>
        loc.toLowerCase().includes(value.toLowerCase())
      ).slice(0, 8); // Show max 8 suggestions
      setLocationSuggestions(filtered);
      setShowLocationSuggestions(true);
    } else {
      setShowLocationSuggestions(false);
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showLocationSuggestions || locationSuggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(prev =>
          prev < locationSuggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex(prev =>
          prev > 0 ? prev - 1 : locationSuggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedSuggestionIndex >= 0) {
          handleLocationSelect(locationSuggestions[selectedSuggestionIndex]);
        }
        break;
      case 'Escape':
        setShowLocationSuggestions(false);
        setSelectedSuggestionIndex(-1);
        break;
    }
  };

  // Handle location suggestion selection
  const handleLocationSelect = (selectedLocation: string) => {
    setLocation(selectedLocation);
    setShowLocationSuggestions(false);
    setLocationSuggestions([]);
    // Don't blur immediately to prevent issues
    setTimeout(() => {
      locationInputRef.current?.blur();
    }, 100);
  };

  // Hide suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      const inputElement = locationInputRef.current;
      const suggestionsElement = inputElement?.parentElement?.querySelector('.suggestions-dropdown');

      // Don't hide if clicking on input or suggestions dropdown
      if (inputElement && (inputElement.contains(target) || suggestionsElement?.contains(target))) {
        return;
      }

      setShowLocationSuggestions(false);
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const scrollToResults = () => {
    // If we're already on the properties page, scroll to results
    if (window.location.pathname === '/properties') {
      const resultsSection = document.querySelector('section[data-results]');
      if (resultsSection) {
        const navbarHeight = 80; // Approximate navbar height
        const elementPosition = resultsSection.getBoundingClientRect().top + window.pageYOffset - navbarHeight;

        window.scrollTo({
          top: elementPosition,
          behavior: 'smooth'
        });
      }
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setShowLocationSuggestions(false);

    // Handle PG search separately
    if (searchType === 'pg') {
      const params = new URLSearchParams();
      if (location) params.set('city', location);
      if (priceRange) params.set('priceRange', priceRange);
      if (pgRoomType) params.set('roomType', pgRoomType);
      if (pgGender) params.set('gender', pgGender);

      const queryString = params.toString();
      const targetUrl = `/pg${queryString ? `?${queryString}` : ''}`;
      router.push(targetUrl);
      return;
    }

    // Build query parameters for regular property search
    const params = new URLSearchParams();

    if (location) params.set('city', location);
    if (propertyType) params.set('type', propertyType.toUpperCase());
    if (bedrooms) params.set('bedrooms', bedrooms);

    // Handle price range
    if (priceRange && priceRange !== '') {
      if (priceRange.includes('-')) {
        const [min, max] = priceRange.split('-');
        if (min && min !== '0') params.set('minPrice', min);
        if (max && !priceRange.endsWith('+')) params.set('maxPrice', max);
      } else if (priceRange.endsWith('+')) {
        const min = priceRange.replace('+', '');
        params.set('minPrice', min);
      }
    }

    // Navigate to properties page with filters
    const queryString = params.toString();
    const targetUrl = `/properties${queryString ? `?${queryString}` : ''}`;

    // If we're already on properties page, just update URL and scroll
    if (window.location.pathname === '/properties') {
      window.history.pushState({}, '', targetUrl);
      // Trigger a custom event to update filters
      window.dispatchEvent(new CustomEvent('searchUpdate', {
        detail: { url: targetUrl }
      }));
      setTimeout(scrollToResults, 100);
    } else {
      router.push(targetUrl);
    }
  };

  return (
    <div className="bg-white/95 backdrop-blur-md rounded-2xl shadow-large border border-white/20 p-8 max-w-6xl mx-auto animate-scale-in">
      <div className="flex space-x-2 mb-8 bg-gray-100 p-2 rounded-xl">
        <button
          className={`flex-1 px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
            searchType === 'buy'
              ? 'bg-primary-600 text-white shadow-soft transform scale-105'
              : 'text-text-secondary hover:text-text-primary hover:bg-white/50'
          }`}
          onClick={() => setSearchType('buy')}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          Buy
        </button>
        <button
          className={`flex-1 px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
            searchType === 'rent'
              ? 'bg-primary-600 text-white shadow-soft transform scale-105'
              : 'text-text-secondary hover:text-text-primary hover:bg-white/50'
          }`}
          onClick={() => setSearchType('rent')}
        >
          {/* <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z" />
          </svg> */}
          Rent
        </button>
        <button
          className={`flex-1 px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
            searchType === 'pg'
              ? 'bg-primary-600 text-white shadow-soft transform scale-105'
              : 'text-text-secondary hover:text-text-primary hover:bg-white/50'
          }`}
          onClick={() => setSearchType('pg')}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          PG
        </button>
        <button
          className={`flex-1 px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
            searchType === 'sell'
              ? 'bg-primary-600 text-white shadow-soft transform scale-105'
              : 'text-text-secondary hover:text-text-primary hover:bg-white/50'
          }`}
          onClick={() => setSearchType('sell')}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Sell
        </button>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 gap-6 mb-6">
          <div className="space-y-2">
            <label htmlFor="location" className="block text-sm font-semibold text-text-primary">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              Location
            </label>
            <div className="relative">
              <input
                ref={locationInputRef}
                type="text"
                id="location"
                placeholder="Enter city, area (e.g., Hyderabad, Jubilee Hills)"
                className="input-field pl-12"
                value={location}
                onChange={(e) => handleLocationChange(e.target.value)}
                onFocus={() => location.length > 0 && setShowLocationSuggestions(true)}
                onKeyDown={handleKeyDown}
                autoComplete="off"
              />
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 absolute left-4 top-1/2 transform -translate-y-1/2 text-text-tertiary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>

              {/* Location Suggestions Dropdown */}
              {showLocationSuggestions && locationSuggestions.length > 0 && (
                <div className="suggestions-dropdown absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-xl z-[9999] mt-1 max-h-60 overflow-y-auto">
                  {locationSuggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      type="button"
                      className={`w-full text-left px-4 py-3 border-b border-gray-100 last:border-b-0 transition-colors duration-200 cursor-pointer ${
                        selectedSuggestionIndex === index
                          ? 'bg-blue-100 text-blue-800'
                          : 'hover:bg-blue-50 hover:text-blue-700'
                      }`}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleLocationSelect(suggestion);
                      }}
                      onMouseDown={(e) => {
                        e.preventDefault();
                        handleLocationSelect(suggestion);
                      }}
                      onMouseEnter={() => setSelectedSuggestionIndex(index)}
                    >
                      <div className="flex items-center pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <span className="text-sm text-gray-700">{suggestion}</span>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {searchType === 'pg' ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">

            <div className="space-y-2">
              <label htmlFor="pgRoomType" className="block text-sm font-semibold text-text-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                Room Type
              </label>
              <select
                id="pgRoomType"
                className="input-field"
                value={pgRoomType}
                onChange={(e) => setPgRoomType(e.target.value)}
              >
                <option value="">Any Room Type</option>
                <option value="single">🛏️ Single Room</option>
                <option value="double">🛏️🛏️ Double Sharing</option>
                <option value="triple">🛏️🛏️🛏️ Triple Sharing</option>
                <option value="dormitory">🏠 Dormitory</option>
              </select>
            </div>

            <div className="space-y-2">
              <label htmlFor="pgPriceRange" className="block text-sm font-semibold text-text-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Monthly Rent
              </label>
              <select
                id="pgPriceRange"
                className="input-field"
                value={priceRange}
                onChange={(e) => setPriceRange(e.target.value)}
              >
                <option value="">Any Budget</option>
                <option value="0-5000">₹0 - ₹5,000</option>
                <option value="5000-10000">₹5,000 - ₹10,000</option>
                <option value="10000-15000">₹10,000 - ₹15,000</option>
                <option value="15000-20000">₹15,000 - ₹20,000</option>
                <option value="20000-25000">₹20,000 - ₹25,000</option>
                <option value="25000+">₹25,000+</option>
              </select>
            </div>

            <div className="space-y-2">
              <label htmlFor="pgGender" className="block text-sm font-semibold text-text-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Gender Preference
              </label>
              <select
                id="pgGender"
                className="input-field"
                value={pgGender}
                onChange={(e) => setPgGender(e.target.value)}
              >
                <option value="">Any Gender</option>
                <option value="male">👨 Boys Only</option>
                <option value="female">👩 Girls Only</option>
                <option value="co-ed">👫 Co-ed</option>
              </select>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <label htmlFor="propertyType" className="block text-sm font-semibold text-text-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                Property Type
              </label>
              <select
                id="propertyType"
                className="input-field"
                value={propertyType}
                onChange={(e) => setPropertyType(e.target.value)}
              >
                <option value="">Any Type</option>
                <option value="house">🏠 House</option>
                <option value="apartment">🏢 Apartment</option>
                <option value="villa">🏡 Villa</option>
                <option value="plot">📐 Plot</option>
                <option value="commercial">🏪 Commercial</option>
                <option value="office">🏢 Office</option>
              </select>
            </div>

            <div className="space-y-2">
              <label htmlFor="priceRange" className="block text-sm font-semibold text-text-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Price Range
              </label>
              <select
                id="priceRange"
                className="input-field"
                value={priceRange}
                onChange={(e) => setPriceRange(e.target.value)}
              >
                <option value="">Any Budget</option>
                {searchType === 'rent' ? (
                  <>
                    <option value="0-10000">₹0 - ₹10,000</option>
                    <option value="10000-25000">₹10,000 - ₹25,000</option>
                    <option value="25000-50000">₹25,000 - ₹50,000</option>
                    <option value="50000-100000">₹50,000 - ₹1,00,000</option>
                    <option value="100000+">₹1,00,000+</option>
                  </>
                ) : (
                  <>
                    <option value="0-1000000">₹0 - ₹10 Lakh</option>
                    <option value="1000000-2500000">₹10 Lakh - ₹25 Lakh</option>
                    <option value="2500000-5000000">₹25 Lakh - ₹50 Lakh</option>
                    <option value="5000000-10000000">₹50 Lakh - ₹1 Crore</option>
                    <option value="10000000-20000000">₹1 Crore - ₹2 Crore</option>
                    <option value="20000000+">₹2 Crore+</option>
                  </>
                )}
              </select>
            </div>

            <div className="space-y-2">
              <label htmlFor="bedrooms" className="block text-sm font-semibold text-text-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                Bedrooms
              </label>
              <select
                id="bedrooms"
                className="input-field"
                value={bedrooms}
                onChange={(e) => setBedrooms(e.target.value)}
              >
                <option value="">Any Size</option>
                <option value="1">1+ Bedroom</option>
                <option value="2">2+ Bedrooms</option>
                <option value="3">3+ Bedrooms</option>
                <option value="4">4+ Bedrooms</option>
                <option value="5">5+ Bedrooms</option>
              </select>
            </div>
          </div>
        )}

        <div className="mt-8 flex justify-center">
          <button type="submit" className="btn-primary px-12 py-4 text-lg shadow-colored-lg">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            Search Properties
          </button>
        </div>
      </form>
    </div>
  );
}
