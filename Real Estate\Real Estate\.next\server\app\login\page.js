(()=>{var e={};e.id=520,e.ids=[520],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2627:(e,r,s)=>{Promise.resolve().then(s.bind(s,4934))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4934:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Music\\\\Real Estate\\\\Real Estate\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\login\\page.tsx","default")},6189:(e,r,s)=>{"use strict";var t=s(5773);s.o(t,"useRouter")&&s.d(r,{useRouter:function(){return t.useRouter}}),s.o(t,"useSearchParams")&&s.d(r,{useSearchParams:function(){return t.useSearchParams}})},7008:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=s(5239),a=s(8088),n=s(8170),o=s.n(n),i=s(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(r,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4934)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\login\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9488:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>u});var t=s(687),a=s(3210),n=s(6189),o=s(5814),i=s.n(o),l=s(9190),d=s(1317),c=s(216);function u(){let[e,r]=(0,a.useState)(""),[s,o]=(0,a.useState)(""),[u,p]=(0,a.useState)(""),[m,x]=(0,a.useState)(!1),h=(0,n.useRouter)(),f=async r=>{r.preventDefault(),x(!0),p("");try{let r=await c.R2.login(e,s);r.success?(localStorage.setItem("user",JSON.stringify(r.user)),"ADMIN"===r.user.role?h.push("/admin/dashboard"):h.push("/dashboard")):p("Login failed. Please try again.")}catch(e){console.error("Login error:",e),p(e.message||"Login failed. Please check your credentials.")}finally{x(!1)}};return(0,t.jsxs)("main",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(l.Navbar,{}),(0,t.jsx)("div",{className:"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),(0,t.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,t.jsx)(i(),{href:"/signup",className:"font-medium text-primary-600 hover:text-primary-700",children:"create a new account"})]})]}),(0,t.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:f,children:[(0,t.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"sr-only",children:"Email address"}),(0,t.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm",placeholder:"Email address",value:e,onChange:e=>r(e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"sr-only",children:"Password"}),(0,t.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm",placeholder:"Password",value:s,onChange:e=>o(e.target.value)})]})]}),u&&(0,t.jsx)("div",{className:"text-red-600 text-sm text-center",children:u}),(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsx)("div",{className:"text-sm",children:(0,t.jsx)(i(),{href:"/forgot-password",className:"font-medium text-primary-600 hover:text-primary-700",children:"Forgot your password?"})})}),(0,t.jsx)("div",{children:(0,t.jsx)("button",{type:"submit",disabled:m,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",children:m?"Signing in...":"Sign in"})})]})]})}),(0,t.jsx)(d.w,{})]})}},9579:(e,r,s)=>{Promise.resolve().then(s.bind(s,9488))}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[771,814,604,317],()=>s(7008));module.exports=t})();