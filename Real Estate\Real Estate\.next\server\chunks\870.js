"use strict";exports.id=870,exports.ids=[870],exports.modules={3870:(e,r,s)=>{s.d(r,{f:()=>n});var a=s(687),l=s(3210);function n({onFilterChange:e}){let[r,s]=(0,l.useState)({type:"",minPrice:"",maxPrice:"",bedrooms:"",city:"",roomType:"",sharing:"",foodIncluded:"",gender:""}),n=(a,l)=>{console.log("Filter change:",a,"=",l);let n={...r,[a]:l};console.log("New filters:",n),s(n),e(n)},[i,o]=(0,l.useState)({propertyType:!0,price:!0,bedrooms:!0,bathrooms:!0,amenities:!0,roomType:!0,sharing:!0,foodIncluded:!0,gender:!0}),t=e=>{o(r=>({...r,[e]:!r[e]}))};return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-bold mb-6",children:"Filters"}),(0,a.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-6",children:[(0,a.jsx)("div",{className:"flex justify-between items-center cursor-pointer",onClick:()=>t("propertyType"),children:(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Property Type"})}),i.propertyType&&(0,a.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"propertyType",value:"",checked:""===r.type,onChange:s=>e({...r,type:s.target.value}),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"All Types"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"propertyType",value:"HOUSE",checked:"HOUSE"===r.type,onChange:e=>n("type",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"House"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"propertyType",value:"APARTMENT",checked:"APARTMENT"===r.type,onChange:e=>n("type",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Apartment"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"propertyType",value:"VILLA",checked:"VILLA"===r.type,onChange:e=>n("type",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Villa"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"propertyType",value:"PLOT",checked:"PLOT"===r.type,onChange:e=>n("type",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Plot"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"propertyType",value:"COMMERCIAL",checked:"COMMERCIAL"===r.type,onChange:e=>n("type",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Commercial"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"propertyType",value:"OFFICE",checked:"OFFICE"===r.type,onChange:e=>n("type",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Office"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"propertyType",value:"PG",checked:"PG"===r.type,onChange:e=>n("type",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"PG (Paying Guest)"})]})]})]}),(0,a.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-6",children:[(0,a.jsx)("div",{className:"flex justify-between items-center cursor-pointer",onClick:()=>t("price"),children:(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Price Range"})}),i.price&&(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)("div",{className:"flex justify-between mt-4 space-x-4",children:[(0,a.jsxs)("div",{className:"w-1/2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Min Price"}),(0,a.jsxs)("select",{className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",value:r.minPrice,onChange:e=>n("minPrice",e.target.value),children:[(0,a.jsx)("option",{value:"",children:"No Min"}),(0,a.jsx)("option",{value:"500000",children:"₹5 Lakh"}),(0,a.jsx)("option",{value:"1000000",children:"₹10 Lakh"}),(0,a.jsx)("option",{value:"2000000",children:"₹20 Lakh"}),(0,a.jsx)("option",{value:"3000000",children:"₹30 Lakh"}),(0,a.jsx)("option",{value:"5000000",children:"₹50 Lakh"}),(0,a.jsx)("option",{value:"7500000",children:"₹75 Lakh"}),(0,a.jsx)("option",{value:"10000000",children:"₹1 Crore"}),(0,a.jsx)("option",{value:"20000000",children:"₹2 Crore"})]})]}),(0,a.jsxs)("div",{className:"w-1/2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Max Price"}),(0,a.jsxs)("select",{className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",value:r.maxPrice,onChange:e=>n("maxPrice",e.target.value),children:[(0,a.jsx)("option",{value:"",children:"No Max"}),(0,a.jsx)("option",{value:"1000000",children:"₹10 Lakh"}),(0,a.jsx)("option",{value:"2000000",children:"₹20 Lakh"}),(0,a.jsx)("option",{value:"3000000",children:"₹30 Lakh"}),(0,a.jsx)("option",{value:"5000000",children:"₹50 Lakh"}),(0,a.jsx)("option",{value:"7500000",children:"₹75 Lakh"}),(0,a.jsx)("option",{value:"10000000",children:"₹1 Crore"}),(0,a.jsx)("option",{value:"20000000",children:"₹2 Crore"}),(0,a.jsx)("option",{value:"50000000",children:"₹5 Crore"})]})]})]})})]}),(0,a.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-6",children:[(0,a.jsx)("div",{className:"flex justify-between items-center cursor-pointer",onClick:()=>t("bedrooms"),children:(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Bedrooms"})}),i.bedrooms&&(0,a.jsx)("div",{className:"mt-4 flex flex-wrap gap-2",children:["","1","2","3","4","5"].map(e=>(0,a.jsx)("button",{onClick:()=>n("bedrooms",e),className:`px-4 py-2 border rounded-md transition-colors ${r.bedrooms===e?"bg-primary text-white border-primary":"border-gray-300 hover:bg-primary hover:text-white hover:border-primary"}`,children:""===e?"Any":`${e}+`},e))})]}),(0,a.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-6",children:[(0,a.jsx)("div",{className:"flex justify-between items-center cursor-pointer",onClick:()=>t("bathrooms"),children:(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Bathrooms"})}),i.bathrooms&&(0,a.jsxs)("div",{className:"mt-4 flex flex-wrap gap-2",children:[(0,a.jsx)("button",{className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors",children:"Any"}),(0,a.jsx)("button",{className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors",children:"1+"}),(0,a.jsx)("button",{className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors",children:"2+"}),(0,a.jsx)("button",{className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors",children:"3+"}),(0,a.jsx)("button",{className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors",children:"4+"})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("div",{className:"flex justify-between items-center cursor-pointer",onClick:()=>t("amenities"),children:(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Amenities"})}),i.amenities&&(0,a.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",className:"form-checkbox h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Air Conditioning"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",className:"form-checkbox h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Swimming Pool"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",className:"form-checkbox h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Gym"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",className:"form-checkbox h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Balcony"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",className:"form-checkbox h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Parking"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",className:"form-checkbox h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Furnished"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",className:"form-checkbox h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Pet Friendly"})]})]})]}),(0,a.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"City"}),(0,a.jsx)("input",{type:"text",placeholder:"Enter city name",value:r.city,onChange:e=>n("city",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"})]}),"PG"===r.type&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-6",children:[(0,a.jsx)("div",{className:"flex justify-between items-center cursor-pointer",onClick:()=>t("roomType"),children:(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Room Type"})}),i.roomType&&(0,a.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"roomType",value:"",checked:""===r.roomType,onChange:e=>n("roomType",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Any"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"roomType",value:"SINGLE",checked:"SINGLE"===r.roomType,onChange:e=>n("roomType",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Single Room"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"roomType",value:"DOUBLE",checked:"DOUBLE"===r.roomType,onChange:e=>n("roomType",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Double Room"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"roomType",value:"TRIPLE",checked:"TRIPLE"===r.roomType,onChange:e=>n("roomType",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Triple Sharing"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"roomType",value:"FOUR_SHARING",checked:"FOUR_SHARING"===r.roomType,onChange:e=>n("roomType",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Four Sharing"})]})]})]}),(0,a.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-6",children:[(0,a.jsx)("div",{className:"flex justify-between items-center cursor-pointer",onClick:()=>t("sharing"),children:(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Sharing"})}),i.sharing&&(0,a.jsx)("div",{className:"mt-4 flex flex-wrap gap-2",children:["","1","2","3","4+"].map(e=>(0,a.jsx)("button",{onClick:()=>n("sharing",e),className:`px-4 py-2 border rounded-md transition-colors ${r.sharing===e?"bg-primary text-white border-primary":"border-gray-300 hover:bg-primary hover:text-white hover:border-primary"}`,children:""===e?"Any":"1"===e?"Single":`${e} Sharing`},e))})]}),(0,a.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-6",children:[(0,a.jsx)("div",{className:"flex justify-between items-center cursor-pointer",onClick:()=>t("foodIncluded"),children:(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Food"})}),i.foodIncluded&&(0,a.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"foodIncluded",value:"",checked:""===r.foodIncluded,onChange:e=>n("foodIncluded",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Any"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"foodIncluded",value:"YES",checked:"YES"===r.foodIncluded,onChange:e=>n("foodIncluded",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Food Included"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"foodIncluded",value:"NO",checked:"NO"===r.foodIncluded,onChange:e=>n("foodIncluded",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"No Food"})]})]})]}),(0,a.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-6",children:[(0,a.jsx)("div",{className:"flex justify-between items-center cursor-pointer",onClick:()=>t("gender"),children:(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Gender Preference"})}),i.gender&&(0,a.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"gender",value:"",checked:""===r.gender,onChange:e=>n("gender",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Any"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"gender",value:"MALE",checked:"MALE"===r.gender,onChange:e=>n("gender",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Male Only"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"gender",value:"FEMALE",checked:"FEMALE"===r.gender,onChange:e=>n("gender",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Female Only"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"gender",value:"MIXED",checked:"MIXED"===r.gender,onChange:e=>n("gender",e.target.value),className:"form-radio h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"Mixed"})]})]})]})]}),(0,a.jsx)("button",{onClick:()=>{let r={type:"",minPrice:"",maxPrice:"",bedrooms:"",city:"",roomType:"",sharing:"",foodIncluded:"",gender:""};s(r),e(r)},className:"btn-secondary w-full",children:"Reset Filters"})]})}}};