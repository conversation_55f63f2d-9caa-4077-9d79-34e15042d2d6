'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import React from 'react';
import { authAPI } from '@/config/api';

export function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  interface User {
  id: string;
  role: string;
  name: string;
}
const [session, setSession] = useState<{ user: User | null }>({ user: null });
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await authAPI.checkSession()
        setSession(response)
      } catch (err) {
        console.error('Session fetch error:', err);
        setSession({ user: null });
      }
    }

    checkAuth()
  }, []);

  return (
    <>
      <nav className="bg-white/95 backdrop-blur-md shadow-soft sticky top-0 z-50 border-b border-gray-100">
        <div className="container-custom py-4">
          <div className="flex justify-between items-center">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-3 group">
              <div className="relative w-12 h-12 p-2 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl shadow-soft group-hover:shadow-colored transition-all duration-300 transform group-hover:scale-105">
                {/* <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-full h-full text-white">
                  <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  <path d="M9 22V12H15V22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg> */}
              </div>
              <div className="flex flex-col">
                <span className="text-xl font-bold text-gradient">RealEstate</span>
                <span className="text-xs text-text-tertiary font-medium">India</span>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-1">
              <Link href="/" className="px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300">
                Home
              </Link>
              <Link href="/buy" className="px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300">
                Buy
              </Link>
              <Link href="/rent" className="px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300">
                Rent
              </Link>
              <Link href="/pg" className="px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300">
                PG
              </Link>
              <Link href="/sell" className="px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300">
                Sell
              </Link>
            </div>

            {/* Auth Buttons */}
            <div className="hidden lg:flex items-center space-x-3">
              {session.user ? (
                <>
                  <Link
                    href="/dashboard/"
                    className="px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300"
                  >
                    Dashboard
                  </Link>
                  {session.user.role === 'ADMIN' && (
                    <Link
                      href="/admin"
                      className="px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300"
                    >
                      Admin Dashboard
                    </Link>
                  )}
                  <div className="flex items-center space-x-3 px-4 py-2 bg-gray-50 rounded-xl">
                    <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-semibold">
                        {session.user.name?.charAt(0).toUpperCase() || 'U'}
                      </span>
                    </div>
                    <span className="text-text-secondary font-medium">Hi, {session.user.name || 'User'}</span>
                  </div>
                  <button
                    onClick={async () => {
                      try {
                        await authAPI.logout()
                        setSession({ user: null })
                        window.location.href = '/'
                      } catch (error) {
                        console.error('Logout error:', error)
                      }
                    }}
                    className="px-4 py-2 text-text-secondary hover:text-error-600 hover:bg-error-50 font-medium rounded-xl transition-all duration-300"
                  >
                    Logout
                  </button>
                </>
              ) : (
                <>
                  <Link href="/login/" className="px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300">
                    Login
                  </Link>
                  <Link href="/signup/" className="btn-primary">
                    Sign Up
                  </Link>
                </>
              )}
            </div>

            {/* Mobile Menu Button */}
            <button
              className="lg:hidden p-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <span>X</span> : <span>☰</span>}
            </button>
          </div>

          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="lg:hidden mt-6 animate-slide-down">
              <div className="bg-white/95 backdrop-blur-md rounded-2xl shadow-large border border-gray-100 p-6">
                <div className="flex flex-col space-y-2">
                  <Link
                    href="/"
                    className="px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Home
                  </Link>
                  <Link
                    href="/buy"
                    className="px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Buy
                  </Link>
                  <Link
                    href="/rent"
                    className="px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Rent
                  </Link>
                  <Link
                    href="/pg"
                    className="px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    PG
                  </Link>
                  <Link
                    href="/sell"
                    className="px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Sell
                  </Link>
                  <div className="pt-4 mt-4 border-t border-gray-200 flex flex-col space-y-2">
                    {session.user ? (
                      <>
                        <Link
                          href="/dashboard/"
                          className="px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          Dashboard
                        </Link>
                        {session.user.role === 'ADMIN' && (
                          <Link
                            href="/admin"
                            className="px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300"
                            onClick={() => setIsMenuOpen(false)}
                          >
                            Admin Dashboard
                          </Link>
                        )}
                        <div className="flex items-center space-x-3 px-4 py-3 bg-gray-50 rounded-xl">
                          <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
                            <span className="text-white text-sm font-semibold">
                              {session.user.name?.charAt(0).toUpperCase() || 'U'}
                            </span>
                          </div>
                          <span className="text-text-secondary font-medium">Hi, {session.user.name || 'User'}</span>
                        </div>
                        <button
                          onClick={async () => {
                            try {
                              await authAPI.logout()
                              setSession({ user: null })
                              window.location.href = '/'
                            } catch (error) {
                              console.error('Logout error:', error)
                            }
                          }}
                          className="px-4 py-2 text-text-secondary hover:text-error-600 hover:bg-error-50 font-medium rounded-xl transition-all duration-300"
                        >
                          Logout
                        </button>
                      </>
                    ) : (
                      <>
                        <Link href="/login/" className="px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300" onClick={() => setIsMenuOpen(false)}>
                          Login
                        </Link>
                        <Link href="/signup/" className="btn-primary">
                          Sign Up
                        </Link>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </nav>
    </>
  );
}
