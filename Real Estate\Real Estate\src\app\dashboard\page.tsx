'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Navbar } from '@/components/Navbar'
import { Footer } from '@/components/Footer'
import Link from 'next/link'
import { userAPI, authAPI } from '@/config/api'

interface SavedProperty {
  id: string
  property: {
    id: string
    title: string
    price: number
    currency: string
    type: string
    city: string
    images: string
  }
}

interface UserInquiry {
  id: string
  message: string
  status: string
  createdAt: string
  property: {
    title: string
    price: number
    currency: string
  }
}

interface UserProperty {
  id: string
  title: string
  price: number
  currency: string
  type: string
  city: string
  state: string
  approvalStatus: string
  isApproved: boolean
  viewCount: number
  createdAt: string
  images: string
  _count: {
    inquiries: number
    savedBy: number
  }
}

interface Notification {
  id: string
  type: string
  title: string
  message: string
  isRead: boolean
  createdAt: string
  property?: {
    id: string
    title: string
  }
}

export default function DashboardPage() {
  const router = useRouter()
  const [session, setSession] = useState<{ user: any } | null>(null)
  const [listedProperties, setListedProperties] = useState<UserProperty[]>([])
  const [inquiries, setInquiries] = useState<UserInquiry[]>([])
  const [savedProperties, setSavedProperties] = useState<SavedProperty[]>([])
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const [filterStatus, setFilterStatus] = useState<'all' | 'approved' | 'pending' | 'rejected'>('all')

  // Check session on component mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await authAPI.checkSession()
        setSession(response)
        if (!response.user) {
          router.push('/login?error=Please%20login%20to%20access%20dashboard')
        }
      } catch (err) {
        console.error('Session fetch error:', err)
        router.push('/login?error=Please%20login%20to%20access%20dashboard')
      }
    }

    checkAuth()
  }, [])

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!session?.user) {
        console.log('No user session, skipping dashboard data fetch');
        return;
      }

      try {
        console.log('Fetching dashboard data for user:', session.user.id);

        const [propertiesData, inquiriesData] = await Promise.all([
          userAPI.getProperties(),
          userAPI.getInquiries(),
        ]);

        console.log('Properties response:', propertiesData);
        console.log('Inquiries response:', inquiriesData);

        // Handle properties response
        if (propertiesData && propertiesData.success && Array.isArray(propertiesData.properties)) {
          setListedProperties(propertiesData.properties);
        } else if (Array.isArray(propertiesData)) {
          setListedProperties(propertiesData);
        } else {
          console.error('API /api/user/properties did not return expected format:', propertiesData);
          setListedProperties([]);
        }

        // Handle inquiries response
        if (inquiriesData && inquiriesData.success && Array.isArray(inquiriesData.inquiries)) {
          setInquiries(inquiriesData.inquiries);
        } else if (Array.isArray(inquiriesData)) {
          setInquiries(inquiriesData);
        } else {
          console.error('API /api/user/inquiries did not return expected format:', inquiriesData);
          setInquiries([]);
        }

        // For now, set saved properties to empty array since we haven't implemented this API yet
        setSavedProperties([]);
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
        console.error('Error details:', error instanceof Error ? error.message : 'Unknown error');
        // Set empty arrays on error to prevent crashes
        setListedProperties([]);
        setInquiries([]);
        setSavedProperties([]);
      }
    }

    fetchDashboardData()
  }, [session, refreshTrigger])

  // Show loading while checking session
  if (session === null) {
    return (
      <main className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </main>
    )
  }

  // Don't render dashboard if user is not logged in (will redirect)
  if (!session.user) {
    return null
  }

  return (
    <main className="min-h-screen bg-gray-50">
      <Navbar />

      <div className="container-custom py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Welcome to Your Dashboard!</h1>
          <p className="text-gray-600 mt-2">Manage your properties, inquiries, and saved listings here.</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold mb-2">Total Properties</h3>
            <p className="text-3xl font-bold text-primary">{listedProperties.length}</p>
            <p className="text-gray-600">Your listed properties</p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold mb-2">Approved Properties</h3>
            <p className="text-3xl font-bold text-green-600">{listedProperties.filter(p => p.approvalStatus === 'APPROVED').length}</p>
            <p className="text-gray-600">Properties approved by admin</p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold mb-2">Pending Properties</h3>
            <p className="text-3xl font-bold text-yellow-600">{listedProperties.filter(p => p.approvalStatus === 'PENDING').length}</p>
            <p className="text-gray-600">Properties awaiting approval</p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold mb-2">Rejected Properties</h3>
            <p className="text-3xl font-bold text-red-600">{listedProperties.filter(p => p.approvalStatus === 'REJECTED').length}</p>
            <p className="text-gray-600">Properties rejected by admin</p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold mb-2">Inquiries Received</h3>
            <p className="text-3xl font-bold text-primary">{inquiries.length}</p>
            <p className="text-gray-600">Inquiries on your properties</p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold mb-2">Saved Properties</h3>
            <p className="text-3xl font-bold text-primary">{savedProperties.length}</p>
            <p className="text-gray-600">Properties you have saved</p>
          </div>
        </div>

        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Your Listed Properties</h2>
          <div className="flex space-x-4 mb-4">
            <button
              onClick={() => setFilterStatus('all')}
              className={`px-4 py-2 rounded-md text-sm font-medium ${filterStatus === 'all' ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
            >
              All ({listedProperties.length})
            </button>
            <button
              onClick={() => setFilterStatus('approved')}
              className={`px-4 py-2 rounded-md text-sm font-medium ${filterStatus === 'approved' ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
            >
              Approved ({listedProperties.filter(p => p.approvalStatus === 'APPROVED').length})
            </button>
            <button
              onClick={() => setFilterStatus('pending')}
              className={`px-4 py-2 rounded-md text-sm font-medium ${filterStatus === 'pending' ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
            >
              Pending ({listedProperties.filter(p => p.approvalStatus === 'PENDING').length})
            </button>
            <button
              onClick={() => setFilterStatus('rejected')}
              className={`px-4 py-2 rounded-md text-sm font-medium ${filterStatus === 'rejected' ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
            >
              Rejected ({listedProperties.filter(p => p.approvalStatus === 'REJECTED').length})
            </button>
          </div>

          {listedProperties.filter(property => {
            if (filterStatus === 'approved') return property.approvalStatus === 'APPROVED';
            if (filterStatus === 'pending') return property.approvalStatus === 'PENDING';
            if (filterStatus === 'rejected') return property.approvalStatus === 'REJECTED';
            return true;
          }).length === 0 ? (
            <p className="text-gray-600">No properties found for the selected status.</p>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {listedProperties.filter(property => {
                if (filterStatus === 'approved') return property.approvalStatus === 'APPROVED';
                if (filterStatus === 'pending') return property.approvalStatus === 'PENDING';
                if (filterStatus === 'rejected') return property.approvalStatus === 'REJECTED';
                return true;
              }).map((property) => {
                const propertyImages = typeof property.images === 'string' ? JSON.parse(property.images) : property.images;
                return (
                <div key={property.id} className="bg-white rounded-lg shadow p-4">
                  <img src={propertyImages[0]} alt={property.title} className="w-full h-48 object-cover rounded-md mb-4" />
                  <h3 className="text-lg font-semibold mb-1">{property.title}</h3>
                  <p className="text-primary font-bold">{property.currency} {property.price.toLocaleString()}</p>
                  <p className="text-gray-600 text-sm">{property.type} in {property.city}, {property.state}</p>
                  <p className="text-gray-500 text-xs mt-2">Status: {property.approvalStatus}</p>
                  <p className="text-gray-500 text-xs">Views: {property.viewCount}</p>
                  <p className="text-gray-500 text-xs">Inquiries: {property._count.inquiries}</p>
                  <p className="text-gray-500 text-xs">Saved By: {property._count.savedBy}</p>
                  <Link href={`/properties/${property.id}`} className="text-blue-500 hover:underline text-sm mt-2 block">View Details</Link>
                </div>
                );
              })}
            </div>
          )}
        </div>

        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Link href="/properties/create/" className="btn-primary text-center">
              List New Property
            </Link>
            <Link href="/properties/" className="btn-secondary text-center">
              Browse Properties
            </Link>
            <button onClick={() => setRefreshTrigger(prev => prev + 1)} className="btn-secondary text-center">
              Refresh Dashboard Data
            </button>
          </div>
        </div>
      </div>

      <Footer />
    </main>
  )
}
