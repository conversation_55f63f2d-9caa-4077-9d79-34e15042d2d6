'use client'

import { useState, useEffect, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { Navbar } from '@/components/Navbar'
import { Footer } from '@/components/Footer'
import { PropertyCard } from '@/components/PropertyCard'
import { PGFilters } from '@/components/PGFilters'


interface Property {
  id: string
  title: string
  description: string
  price: number
  currency: string
  type: string
  bedrooms: number | null
  bathrooms: number | null
  area: number
  address: string
  city: string
  state: string
  images: string
  amenities: string
  owner: {
    name: string
    email: string
    phone?: string
  }
  createdAt: string
}

// Client component that uses useSearchParams
function PGContent() {
  const searchParams = useSearchParams()
  const [properties, setProperties] = useState<Property[]>([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    hasNext: false,
    hasPrev: false
  })
  const [filters, setFilters] = useState({
    city: '',
    priceRange: '',
    gender: '',
    roomType: '',
    amenities: [] as string[]
  })

  const fetchPGProperties = async (filterParams = filters, page = 1) => {
    setLoading(true)
    try {
      // Filter out empty values from filters
      const cleanFilters = Object.entries(filterParams).reduce((acc, [key, value]) => {
        if (value && typeof value === 'string' && value.trim() !== '') {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, string>);

      const query = new URLSearchParams({
        page: page.toString(),
        limit: '12',
        type: 'PG', // Only fetch PG properties
        ...cleanFilters,
      }).toString()

      const response = await fetch(`/api/properties?${query}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const data = await response.json()

      setProperties(data.properties)
      setPagination(data.pagination)
    } catch (error) {
      console.error('Error loading PG properties:', error)
    } finally {
      setLoading(false)
    }
  }

  // Initialize filters from URL parameters and filter properties
  useEffect(() => {
    // Get URL parameters
    const urlCity = searchParams.get('city') || ''
    const urlPriceRange = searchParams.get('priceRange') || ''
    const urlGender = searchParams.get('gender') || ''
    const urlRoomType = searchParams.get('roomType') || ''

    // Set initial filters from URL
    const initialFilters = {
      city: urlCity,
      priceRange: urlPriceRange,
      gender: urlGender,
      roomType: urlRoomType,
      amenities: [] as string[]
    }
    setFilters(initialFilters)

    // Fetch PG properties with initial filters
    fetchPGProperties(initialFilters)
  }, [searchParams])

  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters)
    fetchPGProperties(newFilters, 1) // Reset to page 1 when filters change
  }
  
  return (
    <>
      {/* Main Content */}
      <section className="py-12">
        <div className="container-custom">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Filters Sidebar */}
            <div className="lg:w-1/4">
              <div className="sticky top-6">
                <PGFilters onFilterChange={handleFilterChange} />
              </div>
            </div>
            
            {/* Properties Grid */}
            <div className="lg:w-3/4">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-text-primary">
                  Available PG Accommodations
                </h2>
                <p className="text-text-secondary">
                  {properties.length} PG{properties.length !== 1 ? 's' : ''} found
                </p>
              </div>

              {loading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="bg-gray-200 h-48 rounded-t-xl"></div>
                      <div className="bg-white p-6 rounded-b-xl">
                        <div className="h-4 bg-gray-200 rounded mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                        <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : properties.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                  {properties.map((property) => (
                    <PropertyCard key={property.id} property={property} />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                    <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-text-primary mb-2">No PG accommodations found</h3>
                  <p className="text-text-secondary mb-6">Try adjusting your filters or check back later for new listings.</p>
                  <button 
                    onClick={() => setFilters({ city: '', priceRange: '', gender: '', roomType: '', amenities: [] })}
                    className="btn-primary"
                  >
                    Clear Filters
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>
    </>
  );
}

// Main page component that wraps the client component in Suspense
export default function PGPage() {

  return (
    <main className="min-h-screen bg-background">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-600 via-primary-700 to-accent-600 text-white py-20">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              Find Your Perfect PG
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-white/90">
              Discover comfortable and affordable paying guest accommodations in Hyderabad
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <div className="flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Verified PG Owners
              </div>
              <div className="flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Safe & Secure
              </div>
              <div className="flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                All Amenities
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Wrap the component that uses useSearchParams in Suspense */}
      <Suspense fallback={
        <div className="py-12 container-custom">
          <div className="flex justify-center">
            <div className="animate-pulse w-full max-w-4xl">
              <div className="h-8 bg-gray-200 rounded mb-4 w-1/3"></div>
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="bg-gray-200 h-48 rounded-t-xl"></div>
                    <div className="bg-white p-6 rounded-b-xl">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                      <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      }>
        <PGContent />
      </Suspense>

      <Footer />
    </main>
  )
}
