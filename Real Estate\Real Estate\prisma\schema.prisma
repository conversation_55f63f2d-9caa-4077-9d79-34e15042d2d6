// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          UserRole  @default(USER)
  phone         String?
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts      Account[]
  sessions      Session[]
  properties    Property[]
  approvedProperties Property[] @relation("PropertyApprover")
  blogPosts     BlogPost[]
  savedProperties SavedProperty[]
  inquiries     Inquiry[]
  notifications PropertyNotification[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Property {
  id          String        @id @default(cuid())
  title       String
  description String
  price       Int
  currency    String        @default("INR")
  type        PropertyType
  listingType ListingType   @default(SALE)
  accommodationType AccommodationType?
  pgRoomType  PGRoomType? // New field for PG room type
  pgGenderPreference PGGenderPreference? // New field for PG gender preference
  status      PropertyStatus @default(AVAILABLE)
  bedrooms    Int?
  bathrooms   Int?
  area        Int?
  address     String
  city        String
  state       String
  pincode     String
  latitude    Float?
  longitude   Float?
  images      String // JSON string of image URLs
  amenities   String // JSON string of amenities
  isFeatured  Boolean       @default(false)
  isApproved  Boolean       @default(false)
  approvalStatus ApprovalStatus @default(PENDING)
  rejectionReason String?
  adminNotes  String?
  viewCount   Int           @default(0)
  isActive    Boolean       @default(true)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  approvedAt  DateTime?
  approvedBy  String?

  owner       User          @relation(fields: [ownerId], references: [id])
  ownerId     String
  approver    User?         @relation("PropertyApprover", fields: [approvedBy], references: [id])
  savedBy     SavedProperty[]
  inquiries   Inquiry[]
  notifications PropertyNotification[]
}

model SavedProperty {
  id         String   @id @default(cuid())
  userId     String
  propertyId String
  createdAt  DateTime @default(now())

  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  property   Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)

  @@unique([userId, propertyId])
}

model Inquiry {
  id         String      @id @default(cuid())
  name       String
  email      String
  phone      String?
  message    String
  status     InquiryStatus @default(NEW)
  adminNotes String?
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt

  property   Property?   @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  propertyId String?
  user       User?       @relation(fields: [userId], references: [id])
  userId     String?
}

model BlogPost {
  id          String      @id @default(cuid())
  title       String
  slug        String      @unique
  content     String
  excerpt     String?
  featuredImage String?
  published   Boolean     @default(false)
  tags        String // JSON string of tags
  category    String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  author      User        @relation(fields: [authorId], references: [id])
  authorId    String
}

enum UserRole {
  USER
  ADMIN
}

enum ListingType {
  RENT
  SALE
}

enum PropertyType {
  APARTMENT
  HOUSE
  VILLA
  PLOT
  COMMERCIAL
  OFFICE
  PG // PG is now a top-level property type
}

enum AccommodationType {
  FULL_HOUSE
  FLAT
  ONE_BHK
  TWO_BHK
  THREE_BHK
  FOUR_BHK
}

enum PGRoomType {
  SINGLE
  DOUBLE
  TRIPLE
  FOUR_SHARING
  DORMITORY
}

enum PGGenderPreference {
  MALE
  FEMALE
  MIXED
}

enum PropertyStatus {
  AVAILABLE
  SOLD
  RENTED
  PENDING
}

enum InquiryStatus {
  NEW
  CONTACTED
  QUALIFIED
  CLOSED
}

enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
  NEEDS_CHANGES
}

enum NotificationType {
  PROPERTY_APPROVED
  PROPERTY_REJECTED
  PROPERTY_NEEDS_CHANGES
  PROPERTY_FEATURED
  INQUIRY_RECEIVED
}

model PropertyNotification {
  id          String           @id @default(cuid())
  type        NotificationType
  title       String
  message     String
  isRead      Boolean          @default(false)
  createdAt   DateTime         @default(now())

  user        User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      String
  property    Property?        @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  propertyId  String?
}
