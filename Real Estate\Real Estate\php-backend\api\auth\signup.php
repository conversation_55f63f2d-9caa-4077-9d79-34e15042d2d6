<?php
require_once '../../config/database.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendError('Method not allowed', 405);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $input = json_decode(file_get_contents('php://input'), true);
    $input = validateInput($input, ['name', 'email', 'password']);
    
    $name = sanitizeInput($input['name']);
    $email = sanitizeInput($input['email']);
    $password = $input['password'];
    $phone = isset($input['phone']) ? sanitizeInput($input['phone']) : null;
    
    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        sendError('Invalid email format', 422);
    }
    
    // Validate password strength
    if (strlen($password) < 6) {
        sendError('Password must be at least 6 characters long', 422);
    }
    
    // Check if user already exists
    $check_query = "SELECT id FROM users WHERE email = :email";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bindParam(':email', $email);
    $check_stmt->execute();
    
    if ($check_stmt->fetch()) {
        sendError('User already exists with this email', 409);
    }
    
    // Hash password
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    
    // Create user
    $user_id = uniqid('user_', true);
    $insert_query = "INSERT INTO users (id, name, email, password, phone, role) VALUES (:id, :name, :email, :password, :phone, 'USER')";
    $insert_stmt = $db->prepare($insert_query);
    $insert_stmt->bindParam(':id', $user_id);
    $insert_stmt->bindParam(':name', $name);
    $insert_stmt->bindParam(':email', $email);
    $insert_stmt->bindParam(':password', $hashed_password);
    $insert_stmt->bindParam(':phone', $phone);
    
    if ($insert_stmt->execute()) {
        sendResponse([
            'success' => true,
            'message' => 'User created successfully',
            'user' => [
                'id' => $user_id,
                'name' => $name,
                'email' => $email,
                'role' => 'USER'
            ]
        ], 201);
    } else {
        sendError('Failed to create user', 500);
    }
    
} catch (Exception $e) {
    error_log("Signup error: " . $e->getMessage());
    sendError('Registration failed', 500);
}
?>
