@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern CSS Custom Properties */
:root {
  --foreground-rgb: 15, 23, 42;
  --background-rgb: 255, 255, 255;
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --blur-backdrop: blur(16px);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 248, 250, 252;
    --background-rgb: 15, 23, 42;
  }
}

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
  font-feature-settings: 'rlig' 1, 'calt' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Selection styles */
::selection {
  background-color: rgba(59, 130, 246, 0.2);
  color: rgb(15, 23, 42);
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(241, 245, 249);
}

::-webkit-scrollbar-thumb {
  background: rgb(203, 213, 225);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(148, 163, 184);
}

@layer components {
  /* Container */
  .container-custom {
    @apply container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
  }

  /* Modern Button Styles */
  .btn-primary {
    @apply relative inline-flex items-center justify-center px-6 py-3 text-base font-semibold text-white bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl shadow-soft hover:shadow-colored transition-all duration-300 transform hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary:hover {
    @apply from-primary-700 to-primary-800 shadow-colored-lg;
  }

  .btn-secondary {
    @apply relative inline-flex items-center justify-center px-6 py-3 text-base font-semibold text-primary-700 bg-white border-2 border-primary-200 rounded-xl shadow-soft hover:shadow-medium hover:border-primary-300 hover:bg-primary-50 transition-all duration-300 transform hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-ghost {
    @apply relative inline-flex items-center justify-center px-6 py-3 text-base font-semibold text-primary-700 bg-transparent hover:bg-primary-50 rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-success {
    @apply relative inline-flex items-center justify-center px-6 py-3 text-base font-semibold text-white bg-gradient-to-r from-success-600 to-success-700 rounded-xl shadow-soft hover:shadow-large transition-all duration-300 transform hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-success-500 focus:ring-offset-2;
  }

  /* Modern Card Styles */
  .card {
    @apply bg-white rounded-2xl shadow-soft hover:shadow-medium transition-all duration-300 overflow-hidden border border-gray-100;
  }

  .card-elevated {
    @apply bg-white rounded-2xl shadow-medium hover:shadow-large transition-all duration-300 transform hover:-translate-y-1 overflow-hidden border border-gray-100;
  }

  .card-glass {
    @apply bg-white/80 backdrop-blur-md rounded-2xl shadow-soft border border-white/20 overflow-hidden;
  }

  /* Modern Input Styles */
  .input-field {
    @apply w-full px-4 py-3 text-base text-text-primary bg-white border-2 border-gray-200 rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 hover:border-gray-300 placeholder:text-text-tertiary;
  }

  .input-field:focus {
    @apply shadow-soft;
  }

  /* Modern Badge Styles */
  .badge {
    @apply inline-flex items-center px-3 py-1 text-sm font-medium rounded-full;
  }

  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }

  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }

  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }

  .badge-error {
    @apply badge bg-error-100 text-error-800;
  }

  /* Modern Gradient Backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .gradient-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .gradient-warm {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }

  /* Modern Text Styles */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent;
  }

  .heading-1 {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold text-text-primary leading-tight;
  }

  .heading-2 {
    @apply text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary leading-tight;
  }

  .heading-3 {
    @apply text-2xl md:text-3xl lg:text-4xl font-bold text-text-primary leading-tight;
  }

  .body-large {
    @apply text-lg md:text-xl text-text-secondary leading-relaxed;
  }

  .body-medium {
    @apply text-base md:text-lg text-text-secondary leading-relaxed;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out forwards;
  }
}