'use client'

import { useState, useEffect } from 'react'
import { Navbar } from '@/components/Navbar'
import { Footer } from '@/components/Footer'
import Link from 'next/link'
import Image from 'next/image'
import { blogAPI } from '@/config/api'
import blogData from '@/data/blog.json'

// Metadata moved to layout or will be set dynamically

interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt: string | null
  featuredImage: string | null
  createdAt: Date
  category: string | null
  tags: string
  author: {
    name: string | null
  }
}

export default function BlogPage() {
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadBlogPosts()
  }, [])

  const loadBlogPosts = async () => {
    try {
      // Try to load from API first, fallback to static data
      try {
        const response = await blogAPI.getPosts()
        if (response.posts && response.posts.length > 0) {
          setPosts(response.posts.map((post: any) => ({
            ...post,
            createdAt: new Date(post.created_at),
            featuredImage: post.featured_image,
            excerpt: post.excerpt
          })))
          setLoading(false)
          return
        }
      } catch (apiError) {
        console.log('API not available, using static data')
      }

      // Fallback to static data
      const staticPosts = blogData
        .filter(post => post.published)
        .map(post => ({
          ...post,
          createdAt: new Date(post.createdAt)
        }))
      setPosts(staticPosts)
    } catch (error) {
      console.error('Error loading blog posts:', error)
      setPosts([])
    } finally {
      setLoading(false)
    }
  }

  const parseJsonSafely = (jsonString: string): string[] => {
    try {
      return JSON.parse(jsonString)
    } catch {
      return []
    }
  }

  if (loading) {
    return (
      <main className="min-h-screen">
        <Navbar />
        <div className="container-custom py-16 text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading blog posts...</p>
        </div>
        <Footer />
      </main>
    )
  }

  return (
    <main className="min-h-screen">
      <Navbar />

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary to-secondary py-16">
        <div className="container-custom text-center text-white">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Real Estate Blog</h1>
          <p className="text-xl md:text-2xl max-w-3xl mx-auto">
            Stay informed with the latest market trends, buying tips, and property investment insights
          </p>
        </div>
      </section>

      {/* Blog Posts */}
      <section className="py-16">
        <div className="container-custom">
          {posts.length === 0 ? (
            <div className="text-center py-16">
              <h2 className="text-2xl font-semibold text-gray-600 mb-4">No blog posts yet</h2>
              <p className="text-gray-500">Check back soon for the latest real estate insights!</p>
            </div>
          ) : (
            <>
              {/* Featured Post */}
              {posts[0] && (
                <div className="mb-16">
                  <h2 className="text-2xl font-bold mb-8">Featured Post</h2>
                  <article className="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div className="md:flex">
                      <div className="md:w-1/2">
                        {posts[0].featuredImage ? (
                          <Image
                            src={posts[0].featuredImage}
                            alt={posts[0].title}
                            width={600}
                            height={400}
                            className="w-full h-64 md:h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-64 md:h-full bg-gray-200 flex items-center justify-center">
                            <span className="text-gray-400">No image</span>
                          </div>
                        )}
                      </div>
                      <div className="md:w-1/2 p-8">
                        <div className="flex items-center mb-4">
                          {posts[0].category && (
                            <span className="bg-primary text-white px-3 py-1 rounded-full text-sm mr-3">
                              {posts[0].category}
                            </span>
                          )}
                          <span className="text-gray-500 text-sm">
                            {new Date(posts[0].createdAt).toLocaleDateString()}
                          </span>
                        </div>
                        <h3 className="text-2xl font-bold mb-4">
                          <Link href={`/blog/${posts[0].slug}`} className="hover:text-primary">
                            {posts[0].title}
                          </Link>
                        </h3>
                        <p className="text-gray-600 mb-4">
                          {posts[0].excerpt || posts[0].title}
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-500">
                            By {posts[0].author.name || 'Admin'}
                          </span>
                          <Link 
                            href={`/blog/${posts[0].slug}`}
                            className="text-primary hover:text-secondary font-medium"
                          >
                            Read More →
                          </Link>
                        </div>
                      </div>
                    </div>
                  </article>
                </div>
              )}

              {/* Other Posts */}
              {posts.length > 1 && (
                <div>
                  <h2 className="text-2xl font-bold mb-8">Latest Posts</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {posts.slice(1).map((post) => (
                      <article key={post.id} className="bg-white rounded-lg shadow-lg overflow-hidden">
                        {post.featuredImage ? (
                          <Image
                            src={post.featuredImage}
                            alt={post.title}
                            width={400}
                            height={250}
                            className="w-full h-48 object-cover"
                          />
                        ) : (
                          <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
                            <span className="text-gray-400">No image</span>
                          </div>
                        )}
                        <div className="p-6">
                          <div className="flex items-center mb-3">
                            {post.category && (
                              <span className="bg-primary text-white px-2 py-1 rounded text-xs mr-2">
                                {post.category}
                              </span>
                            )}
                            <span className="text-gray-500 text-xs">
                              {new Date(post.createdAt).toLocaleDateString()}
                            </span>
                          </div>
                          <h3 className="text-lg font-semibold mb-3">
                            <Link href={`/blog/${post.slug}`} className="hover:text-primary">
                              {post.title}
                            </Link>
                          </h3>
                          <p className="text-gray-600 text-sm mb-4">
                            {post.excerpt || post.title}
                          </p>
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-500">
                              By {post.author.name || 'Admin'}
                            </span>
                            <Link 
                              href={`/blog/${post.slug}`}
                              className="text-primary hover:text-secondary text-sm font-medium"
                            >
                              Read More →
                            </Link>
                          </div>
                        </div>
                      </article>
                    ))}
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </section>
      
      <Footer />
    </main>
  )
}
