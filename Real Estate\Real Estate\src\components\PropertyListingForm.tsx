'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { propertiesAPI } from '@/config/api'

interface PropertyFormData {
  title: string
  description: string
  price: number
  listingType: string // 'RENT' or 'SALE'
  type: string
  accommodationType: string // For BHK/room types
  bedrooms: number
  bathrooms: number
  area: number
  address: string
  city: string
  state: string
  pincode: string
  images: string[]
  amenities: string[]
  pgRoomType?: string // Optional for PG properties
  pgGenderPreference?: string // Optional for PG properties
}

const LISTING_TYPES = [
  { value: 'RENT', label: 'For Rent' },
  { value: 'SALE', label: 'For Sale' }
]

const PROPERTY_TYPES = [
  { value: 'APARTMENT', label: 'Apartment' },
  { value: 'HOUSE', label: 'House' },
  { value: 'VILLA', label: 'Villa' },
  { value: 'PLOT', label: 'Plot' },
  { value: 'COMMERCIAL', label: 'Commercial' },
  { value: 'OFFICE', label: 'Office' },
  { value: 'PG', label: 'PG (Paying Guest)' }
]

const RENT_ACCOMMODATION_TYPES = [
  { value: 'FULL_HOUSE', label: 'Full House' },
  { value: 'FLAT', label: 'Flat' }
]

const SALE_ACCOMMODATION_TYPES = [
  { value: 'ONE_BHK', label: '1BHK' },
  { value: 'TWO_BHK', label: '2BHK' },
  { value: 'THREE_BHK', label: '3BHK' },
  { value: 'FOUR_BHK', label: '4BHK' }
]

const PG_ROOM_TYPES = [
  { value: 'SINGLE', label: 'Single Occupancy' },
  { value: 'DOUBLE', label: 'Double Sharing' },
  { value: 'TRIPLE', label: 'Triple Sharing' },
  { value: 'FOUR_SHARING', label: 'Four Sharing' },
  { value: 'DORMITORY', label: 'Dormitory' }
]

const PG_GENDER_PREFERENCES = [
  { value: 'MALE', label: 'Male Only' },
  { value: 'FEMALE', label: 'Female Only' },
  { value: 'MIXED', label: 'Co-ed' }
]

const AMENITIES_LIST = [
  'Parking', 'Swimming Pool', 'Gym', 'Garden', 'Security', 'Elevator',
  'Power Backup', 'Water Supply', 'Internet', 'Air Conditioning',
  'Balcony', 'Terrace', 'Furnished', 'Semi-Furnished', 'Unfurnished',
  'WiFi', 'AC', 'Food Included', 'Laundry', 'TV', 'Fridge', 'Common Area'
]

export function PropertyListingForm() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    price: '',
    currency: 'INR',
    listingType: '',
    type: '',
    accommodationType: '',
    bedrooms: '1',
    bathrooms: '1',
    area: '',
    address: '',
    city: '',
    state: '',
    pincode: '',
    images: [] as string[],
    amenities: [] as string[],
    pgRoomType: '', // New field for PG room type
    pgGenderPreference: '' // New field for PG gender preference
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleListingTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { value } = e.target
    setFormData(prev => ({
      ...prev,
      listingType: value,
      accommodationType: '', // Reset accommodation type when listing type changes
      pgRoomType: '', // Reset PG specific fields
      pgGenderPreference: '' // Reset PG specific fields
    }))
  }

  const handlePropertyTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { value } = e.target
    setFormData(prev => ({
      ...prev,
      type: value,
      accommodationType: '', // Reset accommodation type when property type changes
      pgRoomType: '', // Reset PG specific fields
      pgGenderPreference: '' // Reset PG specific fields
    }))
  }

  const getAccommodationTypes = () => {
    if (formData.listingType === 'RENT') {
      return RENT_ACCOMMODATION_TYPES
    } else if (formData.listingType === 'SALE') {
      return SALE_ACCOMMODATION_TYPES
    }
    return []
  }

  const handleAmenityToggle = (amenity: string) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...prev.amenities, amenity]
    }))
  }

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      const uploadedImageUrls = await Promise.all(
        Array.from(files).map(async (file) => {
          const formData = new FormData();
          formData.append('file', file);

          const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData,
          });

          if (!response.ok) {
            throw new Error('Image upload failed');
          }
          const data = await response.json();
          return data.url;
        })
      );
      setFormData(prev => ({
        ...prev,
        images: [...prev.images, ...uploadedImageUrls]
      }));
    }
  }

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    // Validation
    if (!formData.listingType) {
      alert('Please select a listing type (Rent or Sale)')
      setLoading(false)
      return
    }

    if (!formData.type) {
      alert('Please select a property type')
      setLoading(false)
      return
    }

    if (formData.type === 'PG') {
      if (!formData.pgRoomType) {
        alert('Please select a PG room type')
        setLoading(false)
        return
      }
      if (!formData.pgGenderPreference) {
        alert('Please select a PG gender preference')
        setLoading(false)
        return
      }
    } else if (!formData.accommodationType) {
      alert('Please select an accommodation type')
      setLoading(false)
      return
    }

    try {
      // Convert string values back to numbers for submission
      const submitData: any = {
        ...formData,
        price: parseInt(formData.price) || 0,
        bedrooms: parseInt(formData.bedrooms) || 1,
        bathrooms: parseInt(formData.bathrooms) || 1,
        area: parseInt(formData.area) || 0,
      }

      // Conditionally include PG specific fields or accommodationType
      if (formData.type === 'PG') {
        delete submitData.accommodationType; // Remove accommodationType for PG
      } else {
        delete submitData.pgRoomType; // Remove PG specific fields for non-PG
        delete submitData.pgGenderPreference;
      }

      console.log('Submitting property data:', submitData);

      const response = await propertiesAPI.createProperty(submitData);

      console.log('Response data:', response);

      if (response.success) {
        alert('Property listing created successfully!');
        if (formData.type === 'PG') {
          router.push('/pg?success=property-listed')
        } else {
          router.push('/dashboard?success=property-listed')
        }
      } else {
        console.error('Error response:', response);
        alert(response.error || response.details || 'Failed to create property listing')
      }
    } catch (error: any) {
      console.error('Error creating property:', error)

      // Check if it's an authentication error
      if (error.message && error.message.includes('Authentication required')) {
        alert('Please login to create a property listing')
        router.push('/login')
      } else {
        alert(error.message || 'Failed to create property listing')
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="md:col-span-2">
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
            Property Title *
          </label>
          <input
            type="text"
            id="title"
            name="title"
            required
            value={formData.title}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder="e.g., Beautiful 3BHK Apartment in Downtown"
          />
        </div>

        <div>
          <label htmlFor="listingType" className="block text-sm font-medium text-gray-700 mb-2">
            Listing Type *
          </label>
          <select
            id="listingType"
            name="listingType"
            required
            value={formData.listingType}
            onChange={handleListingTypeChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="">Select Listing Type</option>
            {LISTING_TYPES.map(type => (
              <option key={type.value} value={type.value}>{type.label}</option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-2">
            Property Type *
          </label>
          <select
            id="type"
            name="type"
            required
            value={formData.type}
            onChange={handlePropertyTypeChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="">Select Property Type</option>
            {PROPERTY_TYPES.map(type => (
              <option key={type.value} value={type.value}>{type.label}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Accommodation Type - Conditional */}
      {formData.type === 'PG' ? (
        <>
          <div>
            <label htmlFor="pgRoomType" className="block text-sm font-medium text-gray-700 mb-2">
              Room Type *
            </label>
            <select
              id="pgRoomType"
              name="pgRoomType"
              required
              value={formData.pgRoomType}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="">Select Room Type</option>
              {PG_ROOM_TYPES.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="pgGenderPreference" className="block text-sm font-medium text-gray-700 mb-2">
              Gender Preference *
            </label>
            <select
              id="pgGenderPreference"
              name="pgGenderPreference"
              required
              value={formData.pgGenderPreference}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="">Select Gender Preference</option>
              {PG_GENDER_PREFERENCES.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
          </div>
        </>
      ) : (
        formData.listingType && (
          <div>
            <label htmlFor="accommodationType" className="block text-sm font-medium text-gray-700 mb-2">
              {formData.listingType === 'RENT' ? 'Accommodation Type *' : 'BHK Type *'}
            </label>
            <select
              id="accommodationType"
              name="accommodationType"
              required
              value={formData.accommodationType}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="">
                {formData.listingType === 'RENT' ? 'Select Accommodation Type' : 'Select BHK Type'}
              </option>
              {getAccommodationTypes().map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
          </div>
        )
      )}

      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
          Description *
        </label>
        <textarea
          id="description"
          name="description"
          required
          rows={4}
          value={formData.description}
          onChange={handleInputChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          placeholder="Describe your property in detail..."
        />
      </div>

      {/* Price and Details */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div>
          <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-2">
            Price (₹) *
          </label>
          <input
            type="number"
            id="price"
            name="price"
            required
            min="0"
            value={formData.price}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder="Enter price in ₹"
          />
        </div>

        <div>
          <label htmlFor="bedrooms" className="block text-sm font-medium text-gray-700 mb-2">
            Bedrooms
          </label>
          <input
            type="number"
            id="bedrooms"
            name="bedrooms"
            min="0"
            value={formData.bedrooms}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>

        <div>
          <label htmlFor="bathrooms" className="block text-sm font-medium text-gray-700 mb-2">
            Bathrooms
          </label>
          <input
            type="number"
            id="bathrooms"
            name="bathrooms"
            min="0"
            value={formData.bathrooms}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>

        <div>
          <label htmlFor="area" className="block text-sm font-medium text-gray-700 mb-2">
            Area (sq ft) *
          </label>
          <input
            type="number"
            id="area"
            name="area"
            required
            min="0"
            value={formData.area}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder="0"
          />
        </div>
      </div>

      {/* Location */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Location Details</h3>
        
        <div>
          <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
            Full Address *
          </label>
          <textarea
            id="address"
            name="address"
            required
            rows={2}
            value={formData.address}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder="Enter complete address"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-2">
              City *
            </label>
            <input
              type="text"
              id="city"
              name="city"
              required
              value={formData.city}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="City"
            />
          </div>

          <div>
            <label htmlFor="state" className="block text-sm font-medium text-gray-700 mb-2">
              State *
            </label>
            <input
              type="text"
              id="state"
              name="state"
              required
              value={formData.state}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="State"
            />
          </div>

          <div>
            <label htmlFor="pincode" className="block text-sm font-medium text-gray-700 mb-2">
              Pincode *
            </label>
            <input
              type="text"
              id="pincode"
              name="pincode"
              required
              pattern="[0-9]{6}"
              value={formData.pincode}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="000000"
            />
          </div>
        </div>
      </div>

      {/* Images */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Property Images</h3>

        <div>
          <label htmlFor="images" className="block text-sm font-medium text-gray-700 mb-2">
            Upload Images
          </label>
          <input
            type="file"
            id="images"
            multiple
            accept="image/*"
            onChange={handleImageUpload}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
          <p className="text-sm text-gray-500 mt-1">Upload multiple images of your property</p>
        </div>

        {formData.images.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {formData.images.map((image, index) => (
              <div key={index} className="relative">
                <img
                  src={image}
                  alt={`Property ${index + 1}`}
                  className="w-full h-24 object-cover rounded-md"
                />
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Amenities */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Amenities</h3>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
          {AMENITIES_LIST.map(amenity => (
            <label key={amenity} className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={formData.amenities.includes(amenity)}
                onChange={() => handleAmenityToggle(amenity)}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="text-sm text-gray-700">{amenity}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Terms and Submit */}
      <div className="border-t pt-6">
        <div className="flex items-center space-x-2 mb-4">
          <input
            type="checkbox"
            id="terms"
            required
            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
          />
          <label htmlFor="terms" className="text-sm text-gray-700">
            I agree to the <a href="/terms" className="text-primary-600 hover:underline">Terms and Conditions</a> and confirm that all information provided is accurate.
          </label>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                <strong>Note:</strong> Your property listing will be reviewed by our team before it goes live.
                You will receive a notification once it's approved or if any changes are needed.
              </p>
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Creating Listing...' : 'Create Property Listing'}
          </button>
        </div>
      </div>
    </form>
  )
}
