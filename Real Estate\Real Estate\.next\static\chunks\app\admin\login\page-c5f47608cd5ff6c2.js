(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[116],{1008:(e,t,r)=>{"use strict";r.d(t,{Eo:()=>l,M5:()=>c,R2:()=>o,hh:()=>i});let s="/php-backend/api",a={LOGIN:"".concat(s,"/auth/login.php"),SIGNUP:"".concat(s,"/auth/signup.php"),LOGOUT:"".concat(s,"/auth/logout.php"),CHECK_SESSION:"".concat(s,"/auth/check-session.php"),PROPERTIES:"".concat(s,"/properties/index.php"),PROPERTY_BY_ID:e=>"".concat(s,"/properties/get.php?id=").concat(e),BLOG_POSTS:"".concat(s,"/blog/index.php"),BL<PERSON><PERSON>_POST_BY_SLUG:e=>"".concat(s,"/blog/get.php?slug=").concat(e),USER_PROPERTIES:"".concat(s,"/user/properties.php"),USER_INQUIRIES:"".concat(s,"/user/inquiries.php")},n=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r={headers:{"Content-Type":"application/json"},credentials:"include"},s={...r,...t,headers:{...r.headers,...t.headers}};try{let t=await fetch(e,s),r=await t.json();if(!t.ok)throw Error(r.error||"HTTP error! status: ".concat(t.status));return r}catch(e){throw console.error("API request failed:",e),e}},o={login:async(e,t)=>n(a.LOGIN,{method:"POST",body:JSON.stringify({email:e,password:t})}),signup:async(e,t,r,s)=>n(a.SIGNUP,{method:"POST",body:JSON.stringify({name:e,email:t,password:r,phone:s})}),logout:async()=>n(a.LOGOUT,{method:"POST"}),checkSession:async()=>n(a.CHECK_SESSION)},c={getProperties:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,s]=e;null!=s&&""!==s&&t.append(r,s.toString())}),n("".concat(a.PROPERTIES,"?").concat(t.toString()))},createProperty:async e=>n(a.PROPERTIES,{method:"POST",body:JSON.stringify(e)}),getPropertyById:async e=>n(a.PROPERTY_BY_ID(e))},i={getPosts:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,s]=e;null!=s&&""!==s&&t.append(r,s.toString())}),n("".concat(a.BLOG_POSTS,"?").concat(t.toString()))},getPostBySlug:async e=>n(a.BLOG_POST_BY_SLUG(e))},l={getProperties:async()=>n(a.USER_PROPERTIES),getInquiries:async()=>n(a.USER_INQUIRIES)}},5430:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(5155),a=r(2115),n=r(5695),o=r(6874),c=r.n(o),i=r(1008);function l(){let[e,t]=(0,a.useState)(""),[r,o]=(0,a.useState)(""),[l,d]=(0,a.useState)(""),[u,h]=(0,a.useState)(!1),m=(0,n.useRouter)(),p=async t=>{t.preventDefault(),h(!0),d("");try{let t=await i.R2.login(e,r);if(t.success){if("ADMIN"!==t.user.role){d("Access denied. Admin privileges required."),h(!1);return}localStorage.setItem("user",JSON.stringify(t.user)),m.push("/admin/dashboard")}else d("Login failed. Please try again.")}catch(e){console.error("Admin login error:",e),d(e.message||"Login failed. Please check your credentials.")}finally{h(!1)}};return(0,s.jsx)("main",{className:"min-h-screen bg-gray-900 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8 p-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto h-12 w-12 bg-red-600 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})}),(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Admin Access"}),(0,s.jsx)("p",{className:"mt-2 text-center text-sm text-gray-400",children:"Restricted area - Admin credentials required"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-8",children:[(0,s.jsxs)("form",{className:"space-y-6",onSubmit:p,children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Admin Email"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 focus:z-10 sm:text-sm",placeholder:"Enter admin email",value:e,onChange:e=>t(e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Admin Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 focus:z-10 sm:text-sm",placeholder:"Enter admin password",value:r,onChange:e=>o(e.target.value)})]}),l&&(0,s.jsx)("div",{className:"text-red-600 text-sm text-center bg-red-50 p-3 rounded-md",children:l}),(0,s.jsx)("div",{children:(0,s.jsxs)("button",{type:"submit",disabled:u,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,s.jsx)("span",{className:"absolute left-0 inset-y-0 flex items-center pl-3",children:(0,s.jsx)("svg",{className:"h-5 w-5 text-red-500 group-hover:text-red-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z",clipRule:"evenodd"})})}),u?"Authenticating...":"Access Admin Panel"]})})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsx)(c(),{href:"/login",className:"text-sm text-gray-600 hover:text-gray-900",children:"← Back to regular login"})})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"This area is restricted to authorized administrators only."})})]})})}},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},7364:(e,t,r)=>{Promise.resolve().then(r.bind(r,5430))}},e=>{var t=t=>e(e.s=t);e.O(0,[874,441,684,358],()=>t(7364)),_N_E=e.O()}]);