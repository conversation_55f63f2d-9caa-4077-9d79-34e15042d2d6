# Complete Testing Guide for housing.okayy.in

## 🚀 Step-by-Step Testing Process

### 1. **Database & Admin Setup**

First, run the admin reset script:
```bash
# On your server, run:
php reset-admin-production.php
```

Expected output:
```
✅ Admin password updated successfully!
Email: <EMAIL>
Password: Admin@2024!
Role: ADMIN
✅ Password verification successful!
```

### 2. **API Connectivity Test**

Visit: `https://housing.okayy.in/debug-api.php`

Check for:
- ✅ Database connection successful
- ✅ Users table exists
- ✅ Admin users: 1 (or more)
- ✅ All API endpoint files exist

### 3. **Admin Login Test**

1. Go to: `https://housing.okayy.in/admin/login`
2. Use credentials:
   - Email: `<EMAIL>`
   - Password: `Admin@2024!`
3. Should redirect to admin dashboard

**If login fails:**
- Check browser console for errors
- Verify database credentials in `php-backend/config/database.php`
- Run debug script again

### 4. **Property Posting Test**

1. <PERSON><PERSON> as regular user or admin
2. Go to "List Property" or "Add Property"
3. Fill out the form completely
4. Submit the form
5. Watch browser console for any errors

**Expected behavior:**
- Success message appears
- Redirects to dashboard or PG page
- No client-side exceptions

**If posting fails:**
- Check browser console (F12 → Console)
- Look for network errors in Network tab
- Check if API endpoints return JSON

### 5. **Common Issues & Solutions**

#### Issue: "Application error: a client-side exception has occurred"

**Possible causes:**
1. **API returning HTML instead of JSON**
   - Check if PHP files have syntax errors
   - Verify database connection works

2. **CORS issues**
   - Ensure CORS headers are set in PHP files
   - Check if requests are being blocked

3. **JavaScript errors**
   - Check browser console for specific errors
   - Look for undefined variables or functions

4. **Database connection issues**
   - Verify credentials in `database.php`
   - Check if database exists and tables are created

#### Issue: Admin login not working

**Solutions:**
1. Run `reset-admin-production.php` again
2. Check database table structure:
   ```sql
   DESCRIBE users;
   ```
3. Verify password hashing method (should be bcrypt, not MD5)

#### Issue: API endpoints returning 404

**Solutions:**
1. Check file paths and permissions
2. Verify .htaccess configuration
3. Ensure all PHP files were uploaded correctly

### 6. **Security Cleanup**

After successful testing:
1. **Delete debug files:**
   ```bash
   rm debug-api.php
   rm reset-admin-production.php
   rm test-complete-flow.md
   ```

2. **Update database credentials** if using default ones

3. **Change admin password** to something more secure

### 7. **Monitoring & Maintenance**

1. **Check error logs regularly**
   - Look in hosting control panel
   - Monitor PHP error logs

2. **Test key functionality weekly**
   - Admin login
   - Property posting
   - User registration

3. **Keep backups**
   - Database backups
   - File backups

### 8. **Emergency Recovery**

If everything breaks:
1. Check hosting error logs first
2. Restore from backup
3. Re-run setup scripts
4. Contact hosting support if needed

## 📞 Support Checklist

Before contacting support, gather:
- Error messages from browser console
- PHP error log entries
- Database connection test results
- Specific steps that cause the issue

## 🎯 Success Criteria

✅ Admin can login successfully
✅ Properties can be posted without errors
✅ No client-side exceptions occur
✅ All API endpoints return proper JSON
✅ Database operations work correctly
