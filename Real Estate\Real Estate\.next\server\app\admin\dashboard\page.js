(()=>{var e={};e.id=957,e.ids=[957],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1031:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Music\\\\Real Estate\\\\Real Estate\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\dashboard\\page.tsx","default")},1125:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=t(5239),a=t(8088),n=t(8170),i=t.n(n),o=t(893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["admin",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1031)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\dashboard\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\dashboard\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/dashboard/page",pathname:"/admin/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},1135:()=>{},1388:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3271:(e,r,t)=>{Promise.resolve().then(t.bind(t,1031))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o,metadata:()=>i});var s=t(7413),a=t(5091),n=t.n(a);t(1135);let i={title:{default:"Real Estate India - Buy, Sell, and Rent Properties",template:"%s | Real Estate India"},description:"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities, or list your property with us. Expert guidance for all your property needs.",keywords:["real estate India","property for sale","property for rent","buy property","sell property","apartments","houses","villas","commercial property"],authors:[{name:"Real Estate India"}],creator:"Real Estate India",publisher:"Real Estate India",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("https://realestate-india.com"),alternates:{canonical:"/"},openGraph:{type:"website",locale:"en_IN",url:"https://realestate-india.com",title:"Real Estate India - Buy, Sell, and Rent Properties",description:"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities.",siteName:"Real Estate India"},twitter:{card:"summary_large_image",title:"Real Estate India - Buy, Sell, and Rent Properties",description:"Find your dream home in India with our comprehensive real estate platform.",creator:"@realestateindia"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function o({children:e}){return(0,s.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,s.jsx)("body",{className:`${n().variable} font-sans bg-background text-text-primary antialiased`,children:e})})}},4940:()=>{},4943:(e,r,t)=>{Promise.resolve().then(t.bind(t,9573))},5171:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},6189:(e,r,t)=>{"use strict";var s=t(5773);t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9243:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9573:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(687),a=t(3210),n=t(6189);function i(){let[e,r]=(0,a.useState)(!0),[t,i]=(0,a.useState)(null),o=(0,n.useRouter)();return e?(0,s.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading admin dashboard..."})]})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,s.jsx)("header",{className:"bg-white shadow",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Admin Dashboard"}),(0,s.jsxs)("p",{className:"text-gray-600",children:["Welcome back, ",t?.name]})]}),(0,s.jsx)("button",{onClick:()=>o.push("/admin/login"),className:"bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700",children:"Logout"})]})})}),(0,s.jsx)("main",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"px-4 py-6 sm:px-0",children:(0,s.jsx)("div",{className:"border-4 border-dashed border-gray-200 rounded-lg h-96 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Admin Dashboard"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Welcome to the admin panel"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("a",{href:"/admin/properties",className:"block bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-center",children:"Manage Properties"}),(0,s.jsx)("a",{href:"/admin/users",className:"block bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-center",children:"Manage Users"})]})]})})})})]})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[771],()=>t(1125));module.exports=s})();