'use client';

import Image from 'next/image';

type TestimonialProps = {
  name: string;
  role: string;
  image: string;
  quote: string;
  rating: number;
};

export function Testimonial({ name, role, image, quote, rating }: TestimonialProps) {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }).map((_, index) => (
      <svg
        key={index}
        className={`w-5 h-5 ${index < rating ? 'text-yellow-400' : 'text-gray-300'}`}
        fill="currentColor"
        viewBox="0 0 20 20"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
      </svg>
    ));
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-8 md:p-12 transition-all duration-500 transform hover:shadow-xl">
      <div className="flex flex-col md:flex-row items-center md:items-start gap-8">
        <div className="relative w-24 h-24 md:w-32 md:h-32 flex-shrink-0">
          <Image
            src={image}
            alt={name}
            fill
            className="object-cover rounded-full border-4 border-primary/20"
          />
        </div>
        <div>
          <div className="flex mb-4">
            {renderStars(rating)}
          </div>
          <blockquote className="text-lg md:text-xl italic text-gray-700 mb-6">
            "{quote}"
          </blockquote>
          <div>
            <p className="font-bold text-lg text-primary">{name}</p>
            <p className="text-gray-600">{role}</p>
          </div>
        </div>
      </div>
    </div>
  );
}