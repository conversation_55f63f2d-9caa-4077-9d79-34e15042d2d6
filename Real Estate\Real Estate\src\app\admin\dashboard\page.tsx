'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { adminAPI, authAPI } from '@/config/api'

interface Property {
  id: string
  title: string
  price: number
  type: string
  approval_status: string
  owner: {
    name: string
    email: string
  }
  created_at: string
}

export default function AdminDashboard() {
  const [user, setUser] = useState<any>(null)
  const [properties, setProperties] = useState<Property[]>([])
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0
  })
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')
  const router = useRouter()

  useEffect(() => {
    checkAdminAuth()
  }, [])

  const checkAdminAuth = async () => {
    try {
      const response = await authAPI.checkSession()
      if (!response.user || response.user.role !== 'ADMIN') {
        router.push('/login')
        return
      }
      setUser(response.user)
      await loadDashboardData()
    } catch (error) {
      console.error('Auth check failed:', error)
      router.push('/login')
    }
  }

  const loadDashboardData = async () => {
    try {
      const response = await adminAPI.getProperties('all')
      setProperties(response.properties || [])
      
      // Calculate stats
      const total = response.properties?.length || 0
      const pending = response.properties?.filter((p: Property) => p.approval_status === 'PENDING').length || 0
      const approved = response.properties?.filter((p: Property) => p.approval_status === 'APPROVED').length || 0
      const rejected = response.properties?.filter((p: Property) => p.approval_status === 'REJECTED').length || 0
      
      setStats({ total, pending, approved, rejected })
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleApprove = async (propertyId: string) => {
    try {
      await adminAPI.approveProperty(propertyId)
      await loadDashboardData() // Refresh data
      alert('Property approved successfully!')
    } catch (error: any) {
      alert(error.message || 'Failed to approve property')
    }
  }

  const handleReject = async (propertyId: string) => {
    const reason = prompt('Enter rejection reason:')
    if (!reason) return

    try {
      await adminAPI.rejectProperty(propertyId, reason)
      await loadDashboardData() // Refresh data
      alert('Property rejected successfully!')
    } catch (error: any) {
      alert(error.message || 'Failed to reject property')
    }
  }

  const logout = async () => {
    try {
      await authAPI.logout()
      router.push('/login')
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading admin dashboard...</p>
        </div>
      </div>
    )
  }

  const pendingProperties = properties.filter(p => p.approval_status === 'PENDING')

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-gray-600">Welcome back, {user?.name}</p>
            </div>
            <button
              onClick={logout}
              className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
            >
              Logout
            </button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'overview', name: 'Overview' },
              { id: 'pending', name: 'Pending Approvals' },
              { id: 'all', name: 'All Properties' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-red-500 text-red-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                      <span className="text-white font-bold">T</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Total Properties</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.total}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

              {/* Total Users */}
              <div className="bg-gradient-to-r from-green-500 to-green-600 overflow-hidden shadow-lg rounded-xl">
                <div className="p-6 text-white">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                        <span className="text-2xl">👥</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-green-100 truncate">Total Users</dt>
                        <dd className="text-3xl font-bold">{stats.totalUsers}</dd>
                        <dd className="text-sm text-green-100">Registered users</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              {/* Total Views */}
              <div className="bg-gradient-to-r from-purple-500 to-purple-600 overflow-hidden shadow-lg rounded-xl">
                <div className="p-6 text-white">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                        <span className="text-2xl">👁️</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-purple-100 truncate">Total Views</dt>
                        <dd className="text-3xl font-bold">{stats.totalViews.toLocaleString()}</dd>
                        <dd className="text-sm text-purple-100">Property views</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions & Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Quick Actions */}
              <div className="bg-white shadow-lg rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div className="grid grid-cols-2 gap-4">
                  <button
                    onClick={() => setActiveTab('pending')}
                    className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg hover:bg-yellow-100 transition-colors"
                  >
                    <div className="text-center">
                      <div className="text-2xl mb-2">⏳</div>
                      <div className="text-sm font-medium text-gray-900">Review Pending</div>
                      <div className="text-xs text-gray-500">{stats.pending} properties</div>
                    </div>
                  </button>

                  <button
                    onClick={() => setActiveTab('users')}
                    className="p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
                  >
                    <div className="text-center">
                      <div className="text-2xl mb-2">👥</div>
                      <div className="text-sm font-medium text-gray-900">Manage Users</div>
                      <div className="text-xs text-gray-500">{stats.totalUsers} users</div>
                    </div>
                  </button>

                  <button
                    onClick={() => setActiveTab('analytics')}
                    className="p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors"
                  >
                    <div className="text-center">
                      <div className="text-2xl mb-2">📈</div>
                      <div className="text-sm font-medium text-gray-900">View Analytics</div>
                      <div className="text-xs text-gray-500">Performance data</div>
                    </div>
                  </button>

                  <button
                    onClick={() => window.open('/properties', '_blank')}
                    className="p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors"
                  >
                    <div className="text-center">
                      <div className="text-2xl mb-2">🌐</div>
                      <div className="text-sm font-medium text-gray-900">View Site</div>
                      <div className="text-xs text-gray-500">Public view</div>
                    </div>
                  </button>
                </div>
              </div>

              {/* Recent Activity */}
              <div className="bg-white shadow-lg rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
                <div className="space-y-4">
                  {stats.recentActivity.slice(0, 5).map((property: Property) => (
                    <div key={property.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <div className={`w-3 h-3 rounded-full ${
                        property.approval_status === 'APPROVED' ? 'bg-green-500' :
                        property.approval_status === 'PENDING' ? 'bg-yellow-500' : 'bg-red-500'
                      }`}></div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {property.title}
                        </p>
                        <p className="text-xs text-gray-500">
                          {property.owner_name} • {new Date(property.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        property.approval_status === 'APPROVED' ? 'bg-green-100 text-green-800' :
                        property.approval_status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {property.approval_status}
                      </span>
                    </div>
                  ))}
                  {stats.recentActivity.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <div className="text-4xl mb-2">📭</div>
                      <p>No recent activity</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Pending Approvals Tab */}
        {activeTab === 'pending' && (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Pending Property Approvals ({pendingProperties.length})
              </h3>
            </div>
            <ul className="divide-y divide-gray-200">
              {pendingProperties.map((property) => (
                <li key={property.id} className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">{property.title}</h4>
                      <p className="text-sm text-gray-500">
                        ₹{property.price.toLocaleString()} • {property.type} • Owner: {property.owner.name}
                      </p>
                      <p className="text-xs text-gray-400">
                        Submitted: {new Date(property.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleApprove(property.id)}
                        className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700"
                      >
                        Approve
                      </button>
                      <button
                        onClick={() => handleReject(property.id)}
                        className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700"
                      >
                        Reject
                      </button>
                    </div>
                  </div>
                </li>
              ))}
              {pendingProperties.length === 0 && (
                <li className="px-4 py-8 text-center text-gray-500">
                  No pending properties to review
                </li>
              )}
            </ul>
          </div>
        )}

        {/* All Properties Tab */}
        {activeTab === 'all' && (
          <div className="space-y-6">
            {/* Search and Filter Bar */}
            <div className="bg-white shadow-sm rounded-lg p-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
                    Search Properties
                  </label>
                  <input
                    type="text"
                    id="search"
                    placeholder="Search by title, owner, or city..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="sm:w-48">
                  <label htmlFor="filter" className="block text-sm font-medium text-gray-700 mb-2">
                    Filter by Status
                  </label>
                  <select
                    id="filter"
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="approved">Approved</option>
                    <option value="rejected">Rejected</option>
                  </select>
                </div>
              </div>
              <div className="mt-4 flex items-center justify-between">
                <p className="text-sm text-gray-600">
                  Showing {filteredProperties.length} of {properties.length} properties
                </p>
                {(searchTerm || filterStatus !== 'all') && (
                  <button
                    onClick={() => {
                      setSearchTerm('')
                      setFilterStatus('all')
                    }}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    Clear filters
                  </button>
                )}
              </div>
            </div>

            {/* Properties List */}
            <div className="bg-white shadow overflow-hidden sm:rounded-lg">
              <div className="px-4 py-5 sm:px-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  All Properties ({filteredProperties.length})
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  Complete list of property listings
                </p>
              </div>
              <ul className="divide-y divide-gray-200">
                {filteredProperties.map((property) => (
                <li key={property.id} className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">{property.title}</h4>
                      <p className="text-sm text-gray-500">
                        ₹{property.price.toLocaleString()} • {property.type} • Owner: {property.owner_name}
                      </p>
                      <p className="text-xs text-gray-400">
                        Created: {new Date(property.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        property.approval_status === 'APPROVED' ? 'bg-green-100 text-green-800' :
                        property.approval_status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {property.approval_status}
                      </span>
                      {property.approval_status === 'PENDING' && (
                        <div className="flex space-x-1">
                          <button
                            onClick={() => handleApprove(property.id)}
                            className="bg-green-600 text-white px-2 py-1 rounded text-xs hover:bg-green-700"
                          >
                            Approve
                          </button>
                          <button
                            onClick={() => handleReject(property.id)}
                            className="bg-red-600 text-white px-2 py-1 rounded text-xs hover:bg-red-700"
                          >
                            Reject
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </li>
              ))}
              {filteredProperties.length === 0 && (
                <li className="px-4 py-8 text-center text-gray-500">
                  {searchTerm || filterStatus !== 'all' ? 'No properties match your filters' : 'No properties found'}
                </li>
              )}
            </ul>
          </div>
        )}

        {/* Users Tab */}
        {activeTab === 'users' && (
          <div className="bg-white shadow-lg rounded-xl overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">User Management</h3>
              <p className="text-sm text-gray-600">Manage registered users and their properties</p>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Properties</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {users.map((user: any) => {
                    const userProperties = properties.filter(p => p.owner_name === user.name)
                    return (
                      <tr key={user.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                              <span className="text-white font-medium text-sm">
                                {user.name?.charAt(0)?.toUpperCase() || 'U'}
                              </span>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">{user.name}</div>
                              <div className="text-sm text-gray-500">{user.email}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            user.role === 'ADMIN' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'
                          }`}>
                            {user.role}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {userProperties.length} properties
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(user.created_at).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {user.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                      </tr>
                    )
                  })}
                  {users.length === 0 && (
                    <tr>
                      <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                        No users found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && (
          <div className="space-y-6">
            {/* Analytics Overview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white shadow-lg rounded-xl p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">Property Performance</h4>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Approval Rate</span>
                    <span className="text-sm font-medium">
                      {stats.total > 0 ? Math.round((stats.approved / stats.total) * 100) : 0}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Avg. Views per Property</span>
                    <span className="text-sm font-medium">
                      {stats.approved > 0 ? Math.round(stats.totalViews / stats.approved) : 0}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Total Inquiries</span>
                    <span className="text-sm font-medium">{stats.totalInquiries}</span>
                  </div>
                </div>
              </div>

              <div className="bg-white shadow-lg rounded-xl p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">Property Types</h4>
                <div className="space-y-3">
                  {['APARTMENT', 'HOUSE', 'PG', 'COMMERCIAL'].map(type => {
                    const count = properties.filter(p => p.type === type).length
                    const percentage = stats.total > 0 ? Math.round((count / stats.total) * 100) : 0
                    return (
                      <div key={type} className="flex justify-between">
                        <span className="text-sm text-gray-600">{type}</span>
                        <span className="text-sm font-medium">{count} ({percentage}%)</span>
                      </div>
                    )
                  })}
                </div>
              </div>

              <div className="bg-white shadow-lg rounded-xl p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">Status Distribution</h4>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Approved</span>
                    <span className="text-sm font-medium text-green-600">{stats.approved}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Pending</span>
                    <span className="text-sm font-medium text-yellow-600">{stats.pending}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Rejected</span>
                    <span className="text-sm font-medium text-red-600">{stats.rejected}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Top Performing Properties */}
            <div className="bg-white shadow-lg rounded-xl p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Top Performing Properties</h4>
              <div className="overflow-x-auto">
                <table className="min-w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-2 text-sm font-medium text-gray-600">Property</th>
                      <th className="text-left py-2 text-sm font-medium text-gray-600">Views</th>
                      <th className="text-left py-2 text-sm font-medium text-gray-600">Inquiries</th>
                      <th className="text-left py-2 text-sm font-medium text-gray-600">Owner</th>
                    </tr>
                  </thead>
                  <tbody>
                    {properties
                      .filter(p => p.approval_status === 'APPROVED')
                      .sort((a, b) => (b.view_count || 0) - (a.view_count || 0))
                      .slice(0, 5)
                      .map(property => (
                        <tr key={property.id} className="border-b border-gray-100">
                          <td className="py-3">
                            <div className="text-sm font-medium text-gray-900">{property.title}</div>
                            <div className="text-xs text-gray-500">₹{property.price.toLocaleString()}</div>
                          </td>
                          <td className="py-3 text-sm text-gray-900">{property.view_count || 0}</td>
                          <td className="py-3 text-sm text-gray-900">{property.inquiries_count || 0}</td>
                          <td className="py-3 text-sm text-gray-500">{property.owner_name}</td>
                        </tr>
                      ))}
                  </tbody>
                </table>
                {properties.filter(p => p.approval_status === 'APPROVED').length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No approved properties yet
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
