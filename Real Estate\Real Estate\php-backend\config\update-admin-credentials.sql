-- Update Admin Credentials for housing.okayy.in
-- Run this SQL to update admin user with correct credentials
-- 
-- IMPORTANT: Update the database.php file with your actual database credentials:
-- Database Name: housing_okayy_db (or your actual database name)
-- Username: housing_okayy_user (or your actual username)
-- Password: your_secure_password (your actual password)

-- First, delete any existing admin users to avoid conflicts
DELETE FROM users WHERE role = 'ADMIN';

-- Insert new admin user with secure credentials
-- Email: <EMAIL>
-- Password: Admin@2024!
INSERT INTO users (
    id, 
    name, 
    email, 
    password, 
    role, 
    is_active, 
    created_at, 
    updated_at
) VALUES (
    'admin-housing-okayy-2024', 
    'Housing Admin', 
    '<EMAIL>', 
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
    'ADMIN', 
    TRUE, 
    NOW(), 
    NOW()
);

-- Verify the admin user was created
SELECT id, name, email, role, is_active, created_at FROM users WHERE role = 'ADMIN';

-- Optional: Create a test regular user
INSERT INTO users (
    id, 
    name, 
    email, 
    password, 
    role, 
    phone, 
    is_active, 
    created_at, 
    updated_at
) VALUES (
    'user-test-housing-okayy', 
    'Test User', 
    '<EMAIL>', 
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
    'USER', 
    '+91-9876543210', 
    TRUE, 
    NOW(), 
    NOW()
);

-- Show all users to verify
SELECT id, name, email, role, is_active FROM users ORDER BY role DESC, created_at DESC;
