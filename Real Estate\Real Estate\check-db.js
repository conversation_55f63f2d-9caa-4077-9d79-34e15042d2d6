const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkDatabase() {
  try {
    console.log('Checking database contents...\n');
    
    // Check users
    const users = await prisma.user.findMany();
    console.log(`Users in database: ${users.length}`);
    users.forEach(user => {
      console.log(`- ${user.name} (${user.email}) - Role: ${user.role}`);
    });
    
    console.log('\n');
    
    // Check properties
    const properties = await prisma.property.findMany({
      include: {
        owner: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });
    
    console.log(`Properties in database: ${properties.length}`);
    properties.forEach(property => {
      console.log(`- ${property.title} - ${property.type} - ${property.approvalStatus} - Owner: ${property.owner.name}`);
    });
    
    console.log('\n');
    
    // Check approved properties specifically
    const approvedProperties = await prisma.property.findMany({
      where: {
        approvalStatus: 'APPROVED',
        isApproved: true
      }
    });
    
    console.log(`Approved properties: ${approvedProperties.length}`);
    approvedProperties.forEach(property => {
      console.log(`- ${property.title} - ${property.type} - Price: ${property.price}`);
    });
    
  } catch (error) {
    console.error('Error checking database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabase();
