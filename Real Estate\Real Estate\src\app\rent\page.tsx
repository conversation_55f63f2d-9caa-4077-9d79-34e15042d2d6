'use client'

import { useState, useCallback, useEffect } from 'react';
import { Navbar } from '@/components/Navbar';
import { Footer } from '@/components/Footer';
import { SearchBar } from '@/components/SearchBar';
import { PropertyCard } from '@/components/PropertyCard';
import { PropertyFilters } from '@/components/PropertyFilters';
import Link from 'next/link';
import Image from 'next/image';

export default function RentPage() {
  const [filters, setFilters] = useState({
    type: '',
    minPrice: '',
    maxPrice: '',
    bedrooms: '',
    city: ''
  });
  const [properties, setProperties] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    hasNext: false,
    hasPrev: false
  });

  const fetchProperties = async (filterParams = filters, page = 1) => {
    setLoading(true);
    try {
      // Filter out empty values from filters
      const cleanFilters = Object.entries(filterParams).reduce((acc, [key, value]) => {
        if (value && value.trim() !== '') {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, string>);

      const query = new URLSearchParams({
        page: page.toString(),
        limit: '12',
        listingType: 'RENT', // Only show rental properties
        ...cleanFilters,
      }).toString();

      const response = await fetch(`/php-backend/api/properties/search.php?${query}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();

      setProperties(data.properties);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error loading properties:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load properties on component mount
  useEffect(() => {
    fetchProperties();
  }, []);

  const handleFilterChange = useCallback((newFilters: any) => {
    setFilters(newFilters);
    fetchProperties(newFilters, 1); // Reset to page 1 when filters change
  }, []);

  return (
    <main className="min-h-screen">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative h-[500px] flex items-center justify-center">
        <div className="absolute inset-0 z-0">
          <Image 
            src="https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?q=80&w=1470&auto=format&fit=crop" 
            alt="Rent Properties" 
            fill 
            className="object-cover brightness-50"
          />
        </div>
        <div className="container-custom relative z-10 text-center text-white">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Find Your Perfect Rental</h1>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Discover a wide range of rental properties that match your lifestyle and budget
          </p>
          <SearchBar />
        </div>
      </section>
      
      {/* Renting Guide */}
      <section className="py-16 bg-gray-100">
        <div className="container-custom">
          <h2 className="text-3xl font-bold mb-12 text-center">Your Renting Journey</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="w-16 h-16 bg-accent text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">1</div>
              <h3 className="text-xl font-bold mb-3">Determine Your Budget</h3>
              <p className="text-text-secondary">
                Calculate how much rent you can afford, including utilities and other monthly expenses.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="w-16 h-16 bg-accent text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">2</div>
              <h3 className="text-xl font-bold mb-3">Find Your Rental</h3>
              <p className="text-text-secondary">
                Browse our listings, schedule viewings, and find the perfect rental property that meets your needs.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="w-16 h-16 bg-accent text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">3</div>
              <h3 className="text-xl font-bold mb-3">Apply for the Property</h3>
              <p className="text-text-secondary">
                Submit your rental application, including references, credit check, and proof of income.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="w-16 h-16 bg-accent text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">4</div>
              <h3 className="text-xl font-bold mb-3">Sign the Lease</h3>
              <p className="text-text-secondary">
                Review the lease agreement, pay the security deposit, and move into your new home.
              </p>
            </div>
          </div>
          <div className="text-center mt-10">
            <Link href="/renting-guide" className="btn-accent">
              View Complete Renting Guide
            </Link>
          </div>
        </div>
      </section>
      
      {/* Properties Section */}
      <section className="py-16">
        <div className="container-custom">
          <h2 className="text-3xl font-bold mb-12 text-center">Properties For Rent</h2>
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Filters Sidebar */}
            <div className="lg:w-1/4">
              <PropertyFilters onFilterChange={handleFilterChange} />
            </div>
            
            {/* Properties Grid */}
            <div className="lg:w-3/4">
              <div className="flex justify-between items-center mb-6">
                <p className="text-text-secondary">Showing <span className="font-semibold">24</span> properties</p>
                <div className="flex items-center space-x-2">
                  <label htmlFor="sort" className="text-text-secondary">Sort by:</label>
                  <select 
                    id="sort" 
                    className="border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent"
                  >
                    <option value="newest">Newest</option>
                    <option value="price-asc">Price (Low to High)</option>
                    <option value="price-desc">Price (High to Low)</option>
                    <option value="popular">Most Popular</option>
                  </select>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* TODO: Implement property listing functionality */}
                <div className="col-span-full text-center py-8">
                  <p className="text-gray-500">Rental properties will be displayed here</p>
                  <Link href="/properties" className="text-primary hover:underline mt-2 inline-block">
                    Browse All Properties
                  </Link>
                </div>
              </div>
              
              {/* Pagination */}
              <div className="mt-12 flex justify-center">
                <nav className="flex items-center space-x-2">
                  <button className="w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center text-gray-500 hover:bg-gray-100">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                  <button className="w-10 h-10 rounded-md bg-accent text-white flex items-center justify-center">1</button>
                  <button className="w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center hover:bg-gray-100">2</button>
                  <button className="w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center hover:bg-gray-100">3</button>
                  <span className="text-gray-500">...</span>
                  <button className="w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center hover:bg-gray-100">10</button>
                  <button className="w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center text-gray-500 hover:bg-gray-100">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Renting Tips */}
      <section className="py-16 bg-gray-100">
        <div className="container-custom">
          <h2 className="text-3xl font-bold mb-12 text-center">Renting Tips</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="relative h-48">
                <Image 
                  src="https://images.unsplash.com/photo-1560520031-3a4dc4e9de0c?q=80&w=1473&auto=format&fit=crop" 
                  alt="Budgeting for Rent" 
                  fill 
                  className="object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-3">Budgeting for Rent</h3>
                <p className="text-text-secondary mb-4">
                  Learn how to budget for your rental, including security deposits, monthly rent, utilities, and other expenses.
                </p>
                <Link href="/blog/budgeting-for-rent" className="text-accent font-semibold hover:underline">
                  Read More
                </Link>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="relative h-48">
                <Image 
                  src="https://images.unsplash.com/photo-1628744448840-55bdb2497bd4?q=80&w=1470&auto=format&fit=crop" 
                  alt="Rental Inspection" 
                  fill 
                  className="object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-3">Rental Inspection Checklist</h3>
                <p className="text-text-secondary mb-4">
                  What to look for during a rental property inspection and red flags that could save you from a bad rental experience.
                </p>
                <Link href="/blog/rental-inspection" className="text-accent font-semibold hover:underline">
                  Read More
                </Link>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="relative h-48">
                <Image 
                  src="https://images.unsplash.com/photo-1450101499163-c8848c66ca85?q=80&w=1470&auto=format&fit=crop" 
                  alt="Lease Agreement Tips" 
                  fill 
                  className="object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-3">Understanding Lease Agreements</h3>
                <p className="text-text-secondary mb-4">
                  Expert tips on understanding your lease agreement, including terms, conditions, and your rights as a tenant.
                </p>
                <Link href="/blog/lease-agreements" className="text-accent font-semibold hover:underline">
                  Read More
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Featured Neighborhoods */}
      <section className="py-16">
        <div className="container-custom">
          <h2 className="text-3xl font-bold mb-12 text-center">Popular Rental Neighborhoods</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="relative rounded-lg overflow-hidden group h-80">
              <Image 
                src="https://images.unsplash.com/photo-1519501025264-65ba15a82390?q=80&w=1464&auto=format&fit=crop" 
                alt="Downtown" 
                fill 
                className="object-cover transition-transform duration-500 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-end p-6 text-white">
                <h3 className="text-2xl font-bold mb-2">Downtown</h3>
                <p className="mb-3">Urban living with easy access to restaurants, shops, and entertainment</p>
                <div className="flex items-center">
                  <span className="font-semibold mr-4">Avg. Rent: $2,200/mo</span>
                  <Link href="/neighborhoods/downtown" className="text-white underline hover:text-accent">
                    View Properties
                  </Link>
                </div>
              </div>
            </div>
            <div className="relative rounded-lg overflow-hidden group h-80">
              <Image 
                src="https://images.unsplash.com/photo-1600047509807-ba8f99d2cdde?q=80&w=1384&auto=format&fit=crop" 
                alt="Riverside" 
                fill 
                className="object-cover transition-transform duration-500 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-end p-6 text-white">
                <h3 className="text-2xl font-bold mb-2">Riverside</h3>
                <p className="mb-3">Scenic views with parks, trails, and a relaxed atmosphere</p>
                <div className="flex items-center">
                  <span className="font-semibold mr-4">Avg. Rent: $1,800/mo</span>
                  <Link href="/neighborhoods/riverside" className="text-white underline hover:text-accent">
                    View Properties
                  </Link>
                </div>
              </div>
            </div>
            <div className="relative rounded-lg overflow-hidden group h-80">
              <Image 
                src="https://images.unsplash.com/photo-1604014237800-1c9102c219da?q=80&w=1470&auto=format&fit=crop" 
                alt="University District" 
                fill 
                className="object-cover transition-transform duration-500 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-end p-6 text-white">
                <h3 className="text-2xl font-bold mb-2">University District</h3>
                <p className="mb-3">Vibrant area with student life, cafes, and affordable housing</p>
                <div className="flex items-center">
                  <span className="font-semibold mr-4">Avg. Rent: $1,500/mo</span>
                  <Link href="/neighborhoods/university-district" className="text-white underline hover:text-accent">
                    View Properties
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-16 bg-accent text-white">
        <div className="container-custom text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Find Your Perfect Rental?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Our expert agents are ready to help you find the ideal rental property that fits your lifestyle and budget.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/contact" className="btn-white">
              Contact an Agent
            </Link>
            <Link href="/properties" className="btn-outline-white">
              Browse All Rentals
            </Link>
          </div>
        </div>
      </section>
      
      <Footer />
    </main>
  );
}