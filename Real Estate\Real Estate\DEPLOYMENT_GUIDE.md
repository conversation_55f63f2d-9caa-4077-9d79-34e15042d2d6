# 🚀 Hybrid Deployment Guide for Shared Hosting

This guide explains how to deploy your Real Estate application to shared hosting with full functionality using a hybrid approach.

## 📋 **Architecture Overview**

- **Frontend**: Static Next.js files (in `out` folder)
- **Backend**: PHP APIs (in `php-backend` folder)
- **Database**: MySQL (provided by shared hosting)
- **File Storage**: Server uploads folder

## 🛠️ **Pre-Deployment Setup**

### 1. **Build Static Frontend**
```bash
cd "Real Estate/Real Estate"
npm run build
```
This creates the `out` folder with static files.

### 2. **Database Setup**
1. Create a MySQL database in your hosting control panel
2. Import the SQL schema: `php-backend/config/database.sql`
3. Update database credentials in `php-backend/config/database.php`

### 3. **Configure Database Connection**
Edit `php-backend/config/database.php`:
```php
private $host = 'localhost';           // Usually localhost
private $db_name = 'your_db_name';     // Your database name
private $username = 'your_db_user';    // Your database username
private $password = 'your_db_pass';    // Your database password
```

## 📁 **File Structure on Shared Hosting**

Upload files to your hosting account with this structure:
```
public_html/
├── index.html                 (from out folder)
├── _next/                     (from out folder)
├── blog/                      (from out folder)
├── properties/                (from out folder)
├── [other static files]       (from out folder)
├── php-backend/
│   ├── config/
│   │   ├── database.php
│   │   └── database.sql
│   ├── api/
│   │   ├── auth/
│   │   ├── properties/
│   │   ├── upload/
│   │   └── contact/
│   └── uploads/               (create this folder, set 755 permissions)
```

## 🔧 **Deployment Steps**

### Step 1: Upload Static Files
1. Upload all contents of the `out` folder to your `public_html` directory
2. These are your static frontend files

### Step 2: Upload PHP Backend
1. Upload the entire `php-backend` folder to `public_html/php-backend/`
2. Create an `uploads` folder: `public_html/php-backend/uploads/`
3. Set folder permissions to 755 for the uploads folder

### Step 3: Database Setup
1. Access your hosting control panel
2. Create a new MySQL database
3. Import the SQL file: `php-backend/config/database.sql`
4. Update database credentials in `php-backend/config/database.php`

### Step 4: Configure Permissions
Set the following folder permissions:
- `php-backend/uploads/` → 755 (for file uploads)
- All PHP files → 644
- All folders → 755

## 🔐 **Security Configuration**

### 1. **Protect PHP Config Files**
Create `.htaccess` in `php-backend/config/`:
```apache
Order Deny,Allow
Deny from all
```

### 2. **Enable CORS** (if needed)
Add to your main `.htaccess`:
```apache
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
```

## 🧪 **Testing Your Deployment**

### 1. **Test Static Frontend**
Visit: `https://yourdomain.com`
- Should load the homepage
- Navigation should work
- Static pages should load

### 2. **Test PHP Backend**
Visit: `https://yourdomain.com/php-backend/api/auth/check-session.php`
- Should return JSON: `{"user":null}`

### 3. **Test Database Connection**
Try creating an account or logging in
- Should work without errors

## 🔄 **API Endpoints**

Your application will use these endpoints:

### Authentication
- `POST /php-backend/api/auth/login.php`
- `POST /php-backend/api/auth/signup.php`
- `GET /php-backend/api/auth/check-session.php`
- `POST /php-backend/api/auth/logout.php`

### Properties
- `GET /php-backend/api/properties/index.php` (with filters)
- `POST /php-backend/api/properties/index.php` (create property)

### File Upload
- `POST /php-backend/api/upload/index.php`

### Contact
- `POST /php-backend/api/contact/index.php`

## 🚨 **Troubleshooting**

### Common Issues:

1. **Database Connection Failed**
   - Check database credentials in `database.php`
   - Ensure database exists and user has permissions

2. **File Upload Not Working**
   - Check `uploads` folder permissions (755)
   - Verify PHP upload limits in hosting settings

3. **CORS Errors**
   - Add CORS headers to `.htaccess`
   - Check if hosting allows custom headers

4. **404 Errors on API Calls**
   - Verify PHP files are uploaded correctly
   - Check file permissions (644 for PHP files)

### Debug Mode:
Enable error reporting by adding to PHP files:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## 📊 **Features Available**

✅ **Working Features:**
- User registration/login
- Property listings with filters
- Property creation
- File uploads
- Contact forms
- Search functionality
- PG-specific filters
- Admin approval system

✅ **Static Features:**
- Fast loading static pages
- SEO-friendly URLs
- Responsive design
- Blog pages

## 🔄 **Updates and Maintenance**

### To Update Frontend:
1. Make changes to Next.js code
2. Run `npm run build`
3. Upload new `out` folder contents

### To Update Backend:
1. Modify PHP files
2. Upload changed files to `php-backend/`

### Database Updates:
1. Run SQL migrations manually
2. Update through hosting control panel

## 📞 **Support**

If you encounter issues:
1. Check server error logs
2. Verify file permissions
3. Test API endpoints individually
4. Check database connection

Your Real Estate application is now ready for shared hosting with full functionality! 🎉
