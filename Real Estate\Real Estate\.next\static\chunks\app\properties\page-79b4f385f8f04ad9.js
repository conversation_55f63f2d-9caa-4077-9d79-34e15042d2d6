(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[754],{796:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(5155),a=s(2115),i=s(5494),l=s(6821),o=s(4527),n=s(4659),c=s(9604),d=s(1008);function m(){let[e,t]=(0,a.useState)([]),[s,m]=(0,a.useState)(null),[x,p]=(0,a.useState)(!0),[g,h]=(0,a.useState)({page:1,limit:12,total:0,pages:0}),[u,f]=(0,a.useState)({type:"",minPrice:"",maxPrice:"",bedrooms:"",city:"",roomType:"",sharing:"",foodIncluded:"",gender:""}),y=(0,a.useRef)(null);(0,a.useEffect)(()=>{{let e=new URLSearchParams(window.location.search),t={type:e.get("type")||"",minPrice:e.get("minPrice")||"",maxPrice:e.get("maxPrice")||"",bedrooms:e.get("bedrooms")||"",city:e.get("city")||"",roomType:e.get("roomType")||"",sharing:e.get("sharing")||"",foodIncluded:e.get("foodIncluded")||"",gender:e.get("gender")||""};f(t),b(t,1),Array.from(e.values()).some(e=>""!==e)&&setTimeout(()=>{j()},500)}},[]),(0,a.useEffect)(()=>{g.page&&g.limit&&(console.log("Filters changed, fetching properties:",u),b())},[u]),(0,a.useEffect)(()=>{g.page&&g.limit&&g.page>1&&b()},[g.page]),(0,a.useEffect)(()=>{let e=e=>{let t=new URLSearchParams(new URL(e.detail.url,window.location.origin).search);f({type:t.get("type")||"",minPrice:t.get("minPrice")||"",maxPrice:t.get("maxPrice")||"",bedrooms:t.get("bedrooms")||"",city:t.get("city")||"",roomType:t.get("roomType")||"",sharing:t.get("sharing")||"",foodIncluded:t.get("foodIncluded")||"",gender:t.get("gender")||""}),h(e=>({...e,page:1}))};return window.addEventListener("searchUpdate",e),()=>{window.removeEventListener("searchUpdate",e)}},[]);let b=async(e,s)=>{let r=e||u,a=s||g.page,i=g.limit;if(a&&i){console.log("Fetching properties with filters:",r,"page:",a),p(!0);try{let e=Object.entries(r).reduce((e,t)=>{let[s,r]=t;return r&&"string"==typeof r&&""!==r.trim()&&(e[s]=r),e},{});console.log("Clean filters being sent to API:",e);let s={page:a,limit:i,...e};console.log("API filter params:",s);let l=await d.M5.getProperties(s);console.log("API response:",l),t(l.properties),h(l.pagination)}catch(e){console.error("Error loading properties:",e),m(e.message||"Failed to load properties.")}finally{p(!1)}}},j=()=>{if(y.current){let e=y.current.offsetTop-80;window.scrollTo({top:e,behavior:"smooth"})}},v=(0,a.useCallback)(e=>{f(e),h(e=>({...e,page:1})),b(e,1),setTimeout(()=>{j()},100)},[]),N=e=>{h(t=>({...t,page:e})),setTimeout(()=>{j()},100)};return(0,r.jsxs)("main",{className:"min-h-screen",children:[(0,r.jsx)(i.Navbar,{}),(0,r.jsx)("section",{className:"bg-gray-100 py-12",children:(0,r.jsxs)("div",{className:"container-custom",children:[(0,r.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Properties"}),(0,r.jsx)("p",{className:"text-lg text-text-secondary mb-8",children:"Browse our extensive collection of properties for sale and rent. Use the filters to find your perfect match."}),(0,r.jsx)(o.SearchBar,{})]})}),(0,r.jsx)("section",{ref:y,"data-results":!0,className:"py-12",children:(0,r.jsx)("div",{className:"container-custom",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,r.jsx)("div",{className:"lg:w-1/4",children:(0,r.jsx)(c.f,{onFilterChange:v})}),(0,r.jsxs)("div",{className:"lg:w-3/4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsxs)("p",{className:"text-text-secondary",children:["Showing ",(0,r.jsx)("span",{className:"font-semibold",children:e.length})," of ",(0,r.jsx)("span",{className:"font-semibold",children:g.total})," properties"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("label",{htmlFor:"sort",className:"text-text-secondary",children:"Sort by:"}),(0,r.jsxs)("select",{id:"sort",className:"border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",children:[(0,r.jsx)("option",{value:"newest",children:"Newest"}),(0,r.jsx)("option",{value:"price-asc",children:"Price (Low to High)"}),(0,r.jsx)("option",{value:"price-desc",children:"Price (High to Low)"})]})]})]}),x?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"bg-gray-300 h-48 rounded-lg mb-4"}),(0,r.jsx)("div",{className:"h-4 bg-gray-300 rounded mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-300 rounded w-3/4"})]},t))}):0===e.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-500 text-lg mb-4",children:"No properties found"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Try adjusting your search filters"})]}):(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,r.jsx)(n.y,{property:e},e.id))}),g.pages>1&&(0,r.jsx)("div",{className:"mt-12 flex justify-center",children:(0,r.jsxs)("nav",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>N(g.page-1),disabled:1===g.page,className:"w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),[...Array(Math.min(5,g.pages))].map((e,t)=>{let s=t+1;return(0,r.jsx)("button",{onClick:()=>N(s),className:"w-10 h-10 rounded-md flex items-center justify-center ".concat(g.page===s?"bg-primary text-white":"border border-gray-300 hover:bg-gray-100"),children:s},s)}),g.pages>5&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"text-gray-500",children:"..."}),(0,r.jsx)("button",{onClick:()=>N(g.pages),className:"w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center hover:bg-gray-100",children:g.pages})]}),(0,r.jsx)("button",{onClick:()=>N(g.page+1),disabled:g.page===g.pages,className:"w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})})]})]})})}),(0,r.jsx)(l.w,{})]})}},4659:(e,t,s)=>{"use strict";s.d(t,{y:()=>n});var r=s(5155),a=s(6874),i=s.n(a),l=s(6766);function o(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],s=t?"₹":"";if(e>=1e7){let t=e/1e7;return"".concat(s).concat(t.toFixed(+(t%1!=0))," Cr")}if(e>=1e5){let t=e/1e5;return"".concat(s).concat(t.toFixed(+(t%1!=0))," L")}if(!(e>=1e3))return"".concat(s).concat(e.toLocaleString("en-IN"));{let t=e/1e3;return"".concat(s).concat(t.toFixed(+(t%1!=0)),"K")}}function n(e){var t,s,a;let{property:n}=e,c=(e=>{try{return JSON.parse(e||"[]")}catch(e){return[]}})(n.images),d=c.length>0?c[0]:"/placeholder-property.jpg",m=(e=>{let t=new Date(e);return 7>=Math.ceil(Math.abs(new Date().getTime()-t.getTime())/864e5)})(n.createdAt);return(0,r.jsxs)("div",{className:"card-elevated group overflow-hidden animate-fade-in",children:[(0,r.jsxs)("div",{className:"relative h-72 w-full overflow-hidden",children:[(0,r.jsx)(l.default,{src:d,alt:n.title,fill:!0,className:"object-cover transition-all duration-700 group-hover:scale-110 group-hover:brightness-110"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,r.jsx)("div",{className:"absolute top-4 left-4",children:(0,r.jsx)("span",{className:"px-4 py-2 rounded-xl text-sm font-semibold backdrop-blur-sm border border-white/20 ".concat("PG"===n.type||n.title.toLowerCase().includes("pg")?"bg-purple-500/90 text-white":n.title.toLowerCase().includes("rent")||n.price<1e5?"bg-accent-500/90 text-white":"bg-primary-500/90 text-white"," shadow-soft"),children:"PG"===n.type||n.title.toLowerCase().includes("pg")?"PG":n.title.toLowerCase().includes("rent")||n.price<1e5?"For Rent":"For Sale"})}),m&&(0,r.jsx)("div",{className:"absolute top-4 right-16",children:(0,r.jsx)("span",{className:"px-4 py-2 rounded-xl text-sm font-semibold bg-success-500/90 text-white backdrop-blur-sm border border-white/20 shadow-soft animate-bounce-subtle",children:"✨ New"})}),(0,r.jsx)("div",{className:"absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300",children:(0,r.jsx)("button",{className:"w-full py-3 bg-white/95 backdrop-blur-sm text-text-primary font-semibold rounded-xl shadow-soft hover:bg-white transition-all duration-300",children:"Quick View"})})]}),(0,r.jsxs)("div",{className:"p-6 space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-text-primary group-hover:text-primary-600 transition-colors duration-300 line-clamp-2",children:n.title}),(0,r.jsx)("p",{className:"text-text-tertiary text-sm flex items-center",children:(0,r.jsxs)("span",{className:"line-clamp-1",children:[n.address,", ",n.city,", ",n.state]})})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("span",{className:"text-2xl font-bold text-gradient",children:(t=n.price,s=n.title,a=n.type,s.toLowerCase().includes("rent")||"PG"===a||s.toLowerCase().includes("pg")||s.toLowerCase().includes("paying guest")||t<1e5?"".concat(o(t),"/month"):o(t))}),(n.title.toLowerCase().includes("rent")||"PG"===n.type||n.title.toLowerCase().includes("pg")||n.price<1e5)&&(0,r.jsx)("p",{className:"text-xs text-text-tertiary",children:"per month"})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-sm text-text-tertiary",children:"Price per sqft"}),(0,r.jsxs)("div",{className:"text-lg font-semibold text-text-secondary",children:["₹",Math.round(n.price/n.area).toLocaleString("en-IN")]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 py-4 border-t border-gray-100",children:[n.bedrooms&&(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-1",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary-50 rounded-lg flex items-center justify-center"}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-sm font-semibold text-text-primary",children:n.bedrooms}),(0,r.jsx)("div",{className:"text-xs text-text-tertiary",children:"Beds"})]})]}),n.bathrooms&&(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-1",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary-50 rounded-lg flex items-center justify-center"}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-sm font-semibold text-text-primary",children:n.bathrooms}),(0,r.jsx)("div",{className:"text-xs text-text-tertiary",children:"Baths"})]})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-1",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary-50 rounded-lg flex items-center justify-center"}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-sm font-semibold text-text-primary",children:n.area}),(0,r.jsx)("div",{className:"text-xs text-text-tertiary",children:"sqft"})]})]})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(i(),{href:"PG"===n.type?"/pg/".concat(n.id):"/properties/".concat(n.id),className:"btn-primary flex-1 text-center",children:"View Details"}),(0,r.jsx)("button",{className:"px-4 py-3 bg-primary-50 text-primary-600 font-semibold rounded-xl hover:bg-primary-100 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:"Call"})]})]})]},n.id)}},7543:(e,t,s)=>{Promise.resolve().then(s.bind(s,796))}},e=>{var t=t=>e(e.s=t);e.O(0,[874,63,494,821,982,604,441,684,358],()=>t(7543)),_N_E=e.O()}]);