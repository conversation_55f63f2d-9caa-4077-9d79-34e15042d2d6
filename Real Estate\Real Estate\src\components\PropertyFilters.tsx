'use client';

import { useState, useEffect } from 'react';

interface PropertyFiltersProps {
  onFilterChange: (filters: any) => void;
}

export function PropertyFilters({ onFilterChange }: PropertyFiltersProps) {
  const [filters, setFilters] = useState({
    type: '',
    minPrice: '',
    maxPrice: '',
    bedrooms: '',
    city: '',
    // PG-specific filters
    roomType: '',
    sharing: '',
    foodIncluded: '',
    gender: ''
  });

  // Read URL parameters on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const urlFilters = {
        type: urlParams.get('type') || '',
        minPrice: urlParams.get('minPrice') || '',
        maxPrice: urlParams.get('maxPrice') || '',
        bedrooms: urlParams.get('bedrooms') || '',
        city: urlParams.get('city') || '',
        // PG-specific filters
        roomType: urlParams.get('roomType') || '',
        sharing: urlParams.get('sharing') || '',
        foodIncluded: urlParams.get('foodIncluded') || '',
        gender: urlParams.get('gender') || ''
      };
      setFilters(urlFilters);
      // Immediately notify parent of initial filters
      onFilterChange(urlFilters);
    }
  }, [onFilterChange]);

  // Helper function to update filters
  const updateFilter = (key: string, value: string) => {
    console.log('Filter change:', key, '=', value);
    const newFilters = { ...filters, [key]: value };
    console.log('New filters:', newFilters);
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const [expanded, setExpanded] = useState({
    propertyType: true,
    price: true,
    bedrooms: true,
    bathrooms: true,
    amenities: true,
    // PG-specific sections
    roomType: true,
    sharing: true,
    foodIncluded: true,
    gender: true,
  });

  const toggleSection = (section: keyof typeof expanded) => {
    setExpanded(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };



  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-bold mb-6">Filters</h2>
      
      {/* Property Type */}
      <div className="mb-6 border-b border-gray-200 pb-6">
        <div 
          className="flex justify-between items-center cursor-pointer" 
          onClick={() => toggleSection('propertyType')}
        >
          <h3 className="text-lg font-semibold">Property Type</h3>
          {/* <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className={`h-5 w-5 transition-transform ${expanded.propertyType ? 'transform rotate-180' : ''}`} 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg> */}
        </div>
        
        {expanded.propertyType && (
          <div className="mt-4 space-y-2">
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name="propertyType"
                value=""
                checked={filters.type === ''}
                onChange={(e) => onFilterChange({ ...filters, type: e.target.value })}
                className="form-radio h-5 w-5 text-primary"
              />
              <span>All Types</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name="propertyType"
                value="HOUSE"
                checked={filters.type === 'HOUSE'}
                onChange={(e) => updateFilter('type', e.target.value)}
                className="form-radio h-5 w-5 text-primary"
              />
              <span>House</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name="propertyType"
                value="APARTMENT"
                checked={filters.type === 'APARTMENT'}
                onChange={(e) => updateFilter('type', e.target.value)}
                className="form-radio h-5 w-5 text-primary"
              />
              <span>Apartment</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name="propertyType"
                value="VILLA"
                checked={filters.type === 'VILLA'}
                onChange={(e) => updateFilter('type', e.target.value)}
                className="form-radio h-5 w-5 text-primary"
              />
              <span>Villa</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name="propertyType"
                value="PLOT"
                checked={filters.type === 'PLOT'}
                onChange={(e) => updateFilter('type', e.target.value)}
                className="form-radio h-5 w-5 text-primary"
              />
              <span>Plot</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name="propertyType"
                value="COMMERCIAL"
                checked={filters.type === 'COMMERCIAL'}
                onChange={(e) => updateFilter('type', e.target.value)}
                className="form-radio h-5 w-5 text-primary"
              />
              <span>Commercial</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name="propertyType"
                value="OFFICE"
                checked={filters.type === 'OFFICE'}
                onChange={(e) => updateFilter('type', e.target.value)}
                className="form-radio h-5 w-5 text-primary"
              />
              <span>Office</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name="propertyType"
                value="PG"
                checked={filters.type === 'PG'}
                onChange={(e) => updateFilter('type', e.target.value)}
                className="form-radio h-5 w-5 text-primary"
              />
              <span>PG (Paying Guest)</span>
            </label>
          </div>
        )}
      </div>
      
      {/* Price Range */}
      <div className="mb-6 border-b border-gray-200 pb-6">
        <div 
          className="flex justify-between items-center cursor-pointer" 
          onClick={() => toggleSection('price')}
        >
          <h3 className="text-lg font-semibold">Price Range</h3>
          {/* <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className={`h-5 w-5 transition-transform ${expanded.price ? 'transform rotate-180' : ''}`} 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg> */}
        </div>
        
        {expanded.price && (
          <div className="mt-4">
            <div className="flex justify-between mt-4 space-x-4">
              <div className="w-1/2">
                <label className="block text-sm font-medium text-gray-700 mb-1">Min Price</label>
                <select
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  value={filters.minPrice}
                  onChange={(e) => updateFilter('minPrice', e.target.value)}
                >
                  <option value="">No Min</option>
                  <option value="500000">₹5 Lakh</option>
                  <option value="1000000">₹10 Lakh</option>
                  <option value="2000000">₹20 Lakh</option>
                  <option value="3000000">₹30 Lakh</option>
                  <option value="5000000">₹50 Lakh</option>
                  <option value="7500000">₹75 Lakh</option>
                  <option value="10000000">₹1 Crore</option>
                  <option value="20000000">₹2 Crore</option>
                </select>
              </div>
              <div className="w-1/2">
                <label className="block text-sm font-medium text-gray-700 mb-1">Max Price</label>
                <select
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  value={filters.maxPrice}
                  onChange={(e) => updateFilter('maxPrice', e.target.value)}
                >
                  <option value="">No Max</option>
                  <option value="1000000">₹10 Lakh</option>
                  <option value="2000000">₹20 Lakh</option>
                  <option value="3000000">₹30 Lakh</option>
                  <option value="5000000">₹50 Lakh</option>
                  <option value="7500000">₹75 Lakh</option>
                  <option value="10000000">₹1 Crore</option>
                  <option value="20000000">₹2 Crore</option>
                  <option value="50000000">₹5 Crore</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Bedrooms */}
      <div className="mb-6 border-b border-gray-200 pb-6">
        <div 
          className="flex justify-between items-center cursor-pointer" 
          onClick={() => toggleSection('bedrooms')}
        >
          <h3 className="text-lg font-semibold">Bedrooms</h3>
          {/* <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className={`h-5 w-5 transition-transform ${expanded.bedrooms ? 'transform rotate-180' : ''}`} 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg> */}
        </div>
        
        {expanded.bedrooms && (
          <div className="mt-4 flex flex-wrap gap-2">
            {['', '1', '2', '3', '4', '5'].map((bedroom) => (
              <button
                key={bedroom}
                onClick={() => updateFilter('bedrooms', bedroom)}
                className={`px-4 py-2 border rounded-md transition-colors ${
                  filters.bedrooms === bedroom
                    ? 'bg-primary text-white border-primary'
                    : 'border-gray-300 hover:bg-primary hover:text-white hover:border-primary'
                }`}
              >
                {bedroom === '' ? 'Any' : `${bedroom}+`}
              </button>
            ))}
          </div>
        )}
      </div>
      
      {/* Bathrooms */}
      <div className="mb-6 border-b border-gray-200 pb-6">
        <div 
          className="flex justify-between items-center cursor-pointer" 
          onClick={() => toggleSection('bathrooms')}
        >
          <h3 className="text-lg font-semibold">Bathrooms</h3>
          {/* <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className={`h-5 w-5 transition-transform ${expanded.bathrooms ? 'transform rotate-180' : ''}`} 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg> */}
        </div>
        
        {expanded.bathrooms && (
          <div className="mt-4 flex flex-wrap gap-2">
            <button className="px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors">Any</button>
            <button className="px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors">1+</button>
            <button className="px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors">2+</button>
            <button className="px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors">3+</button>
            <button className="px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors">4+</button>
          </div>
        )}
      </div>
      
      {/* Amenities */}
      <div className="mb-6">
        <div 
          className="flex justify-between items-center cursor-pointer" 
          onClick={() => toggleSection('amenities')}
        >
          <h3 className="text-lg font-semibold">Amenities</h3>
          {/* <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className={`h-5 w-5 transition-transform ${expanded.amenities ? 'transform rotate-180' : ''}`} 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg> */}
        </div>
        
        {expanded.amenities && (
          <div className="mt-4 space-y-2">
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="form-checkbox h-5 w-5 text-primary" />
              <span>Air Conditioning</span>
            </label>
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="form-checkbox h-5 w-5 text-primary" />
              <span>Swimming Pool</span>
            </label>
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="form-checkbox h-5 w-5 text-primary" />
              <span>Gym</span>
            </label>
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="form-checkbox h-5 w-5 text-primary" />
              <span>Balcony</span>
            </label>
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="form-checkbox h-5 w-5 text-primary" />
              <span>Parking</span>
            </label>
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="form-checkbox h-5 w-5 text-primary" />
              <span>Furnished</span>
            </label>
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="form-checkbox h-5 w-5 text-primary" />
              <span>Pet Friendly</span>
            </label>
          </div>
        )}
      </div>
      
      {/* City Filter */}
      <div className="mb-6 border-b border-gray-200 pb-6">
        <h3 className="text-lg font-semibold mb-4">City</h3>
        <input
          type="text"
          placeholder="Enter city name"
          value={filters.city}
          onChange={(e) => updateFilter('city', e.target.value)}
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
        />
      </div>

      {/* PG-Specific Filters - Only show when PG is selected */}
      {filters.type === 'PG' && (
        <>
          {/* Room Type */}
          <div className="mb-6 border-b border-gray-200 pb-6">
            <div
              className="flex justify-between items-center cursor-pointer"
              onClick={() => toggleSection('roomType')}
            >
              <h3 className="text-lg font-semibold">Room Type</h3>
            </div>

            {expanded.roomType && (
              <div className="mt-4 space-y-2">
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="roomType"
                    value=""
                    checked={filters.roomType === ''}
                    onChange={(e) => updateFilter('roomType', e.target.value)}
                    className="form-radio h-5 w-5 text-primary"
                  />
                  <span>Any</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="roomType"
                    value="SINGLE"
                    checked={filters.roomType === 'SINGLE'}
                    onChange={(e) => updateFilter('roomType', e.target.value)}
                    className="form-radio h-5 w-5 text-primary"
                  />
                  <span>Single Room</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="roomType"
                    value="DOUBLE"
                    checked={filters.roomType === 'DOUBLE'}
                    onChange={(e) => updateFilter('roomType', e.target.value)}
                    className="form-radio h-5 w-5 text-primary"
                  />
                  <span>Double Room</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="roomType"
                    value="TRIPLE"
                    checked={filters.roomType === 'TRIPLE'}
                    onChange={(e) => updateFilter('roomType', e.target.value)}
                    className="form-radio h-5 w-5 text-primary"
                  />
                  <span>Triple Sharing</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="roomType"
                    value="FOUR_SHARING"
                    checked={filters.roomType === 'FOUR_SHARING'}
                    onChange={(e) => updateFilter('roomType', e.target.value)}
                    className="form-radio h-5 w-5 text-primary"
                  />
                  <span>Four Sharing</span>
                </label>
              </div>
            )}
          </div>

          {/* Sharing */}
          <div className="mb-6 border-b border-gray-200 pb-6">
            <div
              className="flex justify-between items-center cursor-pointer"
              onClick={() => toggleSection('sharing')}
            >
              <h3 className="text-lg font-semibold">Sharing</h3>
            </div>

            {expanded.sharing && (
              <div className="mt-4 flex flex-wrap gap-2">
                {['', '1', '2', '3', '4+'].map((sharing) => (
                  <button
                    key={sharing}
                    onClick={() => updateFilter('sharing', sharing)}
                    className={`px-4 py-2 border rounded-md transition-colors ${
                      filters.sharing === sharing
                        ? 'bg-primary text-white border-primary'
                        : 'border-gray-300 hover:bg-primary hover:text-white hover:border-primary'
                    }`}
                  >
                    {sharing === '' ? 'Any' : sharing === '1' ? 'Single' : `${sharing} Sharing`}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Food Included */}
          <div className="mb-6 border-b border-gray-200 pb-6">
            <div
              className="flex justify-between items-center cursor-pointer"
              onClick={() => toggleSection('foodIncluded')}
            >
              <h3 className="text-lg font-semibold">Food</h3>
            </div>

            {expanded.foodIncluded && (
              <div className="mt-4 space-y-2">
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="foodIncluded"
                    value=""
                    checked={filters.foodIncluded === ''}
                    onChange={(e) => updateFilter('foodIncluded', e.target.value)}
                    className="form-radio h-5 w-5 text-primary"
                  />
                  <span>Any</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="foodIncluded"
                    value="YES"
                    checked={filters.foodIncluded === 'YES'}
                    onChange={(e) => updateFilter('foodIncluded', e.target.value)}
                    className="form-radio h-5 w-5 text-primary"
                  />
                  <span>Food Included</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="foodIncluded"
                    value="NO"
                    checked={filters.foodIncluded === 'NO'}
                    onChange={(e) => updateFilter('foodIncluded', e.target.value)}
                    className="form-radio h-5 w-5 text-primary"
                  />
                  <span>No Food</span>
                </label>
              </div>
            )}
          </div>

          {/* Gender Preference */}
          <div className="mb-6 border-b border-gray-200 pb-6">
            <div
              className="flex justify-between items-center cursor-pointer"
              onClick={() => toggleSection('gender')}
            >
              <h3 className="text-lg font-semibold">Gender Preference</h3>
            </div>

            {expanded.gender && (
              <div className="mt-4 space-y-2">
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="gender"
                    value=""
                    checked={filters.gender === ''}
                    onChange={(e) => updateFilter('gender', e.target.value)}
                    className="form-radio h-5 w-5 text-primary"
                  />
                  <span>Any</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="gender"
                    value="MALE"
                    checked={filters.gender === 'MALE'}
                    onChange={(e) => updateFilter('gender', e.target.value)}
                    className="form-radio h-5 w-5 text-primary"
                  />
                  <span>Male Only</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="gender"
                    value="FEMALE"
                    checked={filters.gender === 'FEMALE'}
                    onChange={(e) => updateFilter('gender', e.target.value)}
                    className="form-radio h-5 w-5 text-primary"
                  />
                  <span>Female Only</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="gender"
                    value="MIXED"
                    checked={filters.gender === 'MIXED'}
                    onChange={(e) => updateFilter('gender', e.target.value)}
                    className="form-radio h-5 w-5 text-primary"
                  />
                  <span>Mixed</span>
                </label>
              </div>
            )}
          </div>
        </>
      )}

      <button
        onClick={() => {
          const resetFilters = {
            type: '',
            minPrice: '',
            maxPrice: '',
            bedrooms: '',
            city: '',
            roomType: '',
            sharing: '',
            foodIncluded: '',
            gender: ''
          };
          setFilters(resetFilters);
          onFilterChange(resetFilters);
        }}
        className="btn-secondary w-full"
      >
        Reset Filters
      </button>
    </div>
  );
}

export default PropertyFilters;
