/**
 * Utility functions for improving property search accuracy and relevance
 */

export interface PropertySearchResult {
  id: string;
  title: string;
  city: string;
  state: string;
  address: string;
  pincode: string;
  isFeatured: boolean;
  viewCount: number;
  createdAt: Date;
  relevanceScore?: number;
}

/**
 * Calculate relevance score for a property based on search term
 */
export function calculateRelevanceScore(
  property: PropertySearchResult,
  searchTerm: string
): number {
  const term = searchTerm.toLowerCase().trim();
  let score = 0;

  // Exact city match gets highest score
  if (property.city.toLowerCase() === term) {
    score += 100;
  }
  // City contains search term
  else if (property.city.toLowerCase().includes(term)) {
    score += 80;
  }

  // State match
  if (property.state.toLowerCase() === term) {
    score += 60;
  }
  else if (property.state.toLowerCase().includes(term)) {
    score += 40;
  }

  // Address contains search term (for area/neighborhood searches)
  if (property.address.toLowerCase().includes(term)) {
    score += 70;
  }

  // Pincode exact match
  if (property.pincode === term) {
    score += 90;
  }
  // Pincode partial match
  else if (property.pincode.includes(term)) {
    score += 50;
  }

  // Boost for featured properties
  if (property.isFeatured) {
    score += 20;
  }

  // Boost based on view count (popularity)
  score += Math.min(property.viewCount * 0.5, 10);

  // Slight boost for newer properties
  const daysSinceCreated = Math.floor(
    (Date.now() - new Date(property.createdAt).getTime()) / (1000 * 60 * 60 * 24)
  );
  if (daysSinceCreated < 30) {
    score += 5;
  }

  return score;
}

/**
 * Sort properties by relevance score
 */
export function sortByRelevance(
  properties: PropertySearchResult[],
  searchTerm: string
): PropertySearchResult[] {
  return properties
    .map(property => ({
      ...property,
      relevanceScore: calculateRelevanceScore(property, searchTerm)
    }))
    .sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0));
}

/**
 * Location aliases and variations for better search matching
 */
export const locationAliases: { [key: string]: string[] } = {
  // Major cities
  'hyderabad': ['hyd', 'secunderabad', 'cyberabad', 'hitec city'],
  'bangalore': ['bengaluru', 'blr', 'silicon valley of india'],
  'mumbai': ['bombay', 'bom', 'financial capital'],
  'delhi': ['new delhi', 'ncr', 'national capital'],
  'chennai': ['madras', 'detroit of india'],
  'kolkata': ['calcutta', 'city of joy'],
  'pune': ['poona', 'oxford of the east'],
  'gurgaon': ['gurugram', 'millennium city'],
  'noida': ['new okhla', 'planned city'],
  
  // Hyderabad specific areas
  'jubilee hills': ['jubilee', 'jh', 'film nagar'],
  'banjara hills': ['banjara', 'bh', 'road no 12'],
  'hitec city': ['hitech city', 'hitec', 'madhapur', 'cyber towers'],
  'gachibowli': ['gachi', 'financial district', 'it hub'],
  'kondapur': ['kphb', 'kukatpally housing board'],
  'kukatpally': ['kphb', 'jntu'],
  'secunderabad': ['sec bad', 'secbad', 'twin city'],
  'ameerpet': ['sr nagar', 'punjagutta', 'commercial hub'],
  'begumpet': ['somajiguda', 'raj bhavan road'],
  'madhapur': ['hitec city', 'cyber towers', 'it corridor'],
  'miyapur': ['bachupally', 'nizampet'],
  'uppal': ['boduppal', 'peerzadiguda'],
  'lb nagar': ['langer house', 'dilsukhnagar'],
  'mehdipatnam': ['rethibowli', 'tolichowki'],
  'charminar': ['old city', 'laad bazaar'],
  'abids': ['nampally', 'koti'],
  'tank bund': ['hussain sagar', 'necklace road'],
  
  // Mumbai areas
  'bandra': ['bandra west', 'bandra east', 'linking road'],
  'andheri': ['andheri west', 'andheri east', 'versova'],
  'powai': ['hiranandani', 'iit bombay'],
  'worli': ['worli sea face', 'atria mall'],
  'lower parel': ['phoenix mills', 'high street phoenix'],
  
  // Bangalore areas
  'koramangala': ['forum mall', '5th block'],
  'whitefield': ['itpl', 'brookefield'],
  'electronic city': ['infosys', 'wipro'],
  'indiranagar': ['100 feet road', 'cmh road'],
  'jayanagar': ['4th block', 'shopping complex'],
  
  // Delhi/NCR areas
  'connaught place': ['cp', 'rajiv chowk'],
  'karol bagh': ['ghaffar market', 'ajmal khan road'],
  'lajpat nagar': ['central market', 'ring road'],
  'dwarka': ['sector 21', 'metro city'],
  
  // Pune areas
  'wakad': ['hinjewadi phase 1', 'it park'],
  'kharadi': ['eon it park', 'world trade center'],
  'aundh': ['university road', 'nal stop'],
  'koregaon park': ['osho ashram', 'north main road']
};

/**
 * Get all possible search terms including aliases
 */
export function getSearchTermsWithAliases(searchTerm: string): string[] {
  const term = searchTerm.toLowerCase().trim();
  const searchTerms = [term];
  
  // Add aliases for the search term
  Object.entries(locationAliases).forEach(([key, aliases]) => {
    if (key.includes(term) || aliases.some(alias => alias.includes(term))) {
      searchTerms.push(key);
      searchTerms.push(...aliases);
    }
  });

  // Remove duplicates and empty terms
  return Array.from(new Set(searchTerms)).filter(t => t.length > 0);
}

/**
 * Popular locations for autocomplete suggestions
 */
export const popularLocations = [
  // Hyderabad and areas
  'Hyderabad', 'Jubilee Hills, Hyderabad', 'Banjara Hills, Hyderabad', 
  'HITEC City, Hyderabad', 'Gachibowli, Hyderabad', 'Madhapur, Hyderabad',
  'Kondapur, Hyderabad', 'Kukatpally, Hyderabad', 'Ameerpet, Hyderabad',
  'Secunderabad', 'Begumpet, Hyderabad', 'Somajiguda, Hyderabad',
  'Financial District, Hyderabad', 'Miyapur, Hyderabad', 'Uppal, Hyderabad',
  'LB Nagar, Hyderabad', 'Mehdipatnam, Hyderabad', 'Charminar, Hyderabad',
  'Abids, Hyderabad', 'Tank Bund, Hyderabad',
  
  // Other major cities
  'Mumbai', 'Bandra West, Mumbai', 'Andheri, Mumbai', 'Powai, Mumbai',
  'Worli, Mumbai', 'Lower Parel, Mumbai',
  
  'Bangalore', 'Koramangala, Bangalore', 'Whitefield, Bangalore', 
  'Electronic City, Bangalore', 'Indiranagar, Bangalore', 'Jayanagar, Bangalore',
  
  'Delhi', 'New Delhi', 'Connaught Place, Delhi', 'Karol Bagh, Delhi',
  'Lajpat Nagar, Delhi', 'Dwarka, Delhi',
  
  'Gurgaon', 'Sector 14, Gurgaon', 'DLF Phase 1, Gurgaon', 'Golf Course Road, Gurgaon',
  
  'Noida', 'Sector 62, Noida', 'Sector 18, Noida', 'Greater Noida',
  
  'Pune', 'Wakad, Pune', 'Hinjewadi, Pune', 'Kharadi, Pune',
  'Aundh, Pune', 'Koregaon Park, Pune',
  
  'Chennai', 'Anna Nagar, Chennai', 'T. Nagar, Chennai', 'Velachery, Chennai',
  'OMR, Chennai', 'Adyar, Chennai'
];

/**
 * Validate and normalize location data
 */
export interface LocationData {
  city: string;
  state: string;
  address: string;
  pincode: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  normalizedData?: LocationData;
}

/**
 * Indian states and their common variations
 */
const indianStates: { [key: string]: string } = {
  'andhra pradesh': 'Andhra Pradesh',
  'ap': 'Andhra Pradesh',
  'telangana': 'Telangana',
  'ts': 'Telangana',
  'karnataka': 'Karnataka',
  'ka': 'Karnataka',
  'maharashtra': 'Maharashtra',
  'mh': 'Maharashtra',
  'tamil nadu': 'Tamil Nadu',
  'tn': 'Tamil Nadu',
  'delhi': 'Delhi',
  'dl': 'Delhi',
  'haryana': 'Haryana',
  'hr': 'Haryana',
  'uttar pradesh': 'Uttar Pradesh',
  'up': 'Uttar Pradesh',
  'west bengal': 'West Bengal',
  'wb': 'West Bengal',
  'gujarat': 'Gujarat',
  'gj': 'Gujarat',
  'rajasthan': 'Rajasthan',
  'rj': 'Rajasthan',
  'punjab': 'Punjab',
  'pb': 'Punjab'
};

/**
 * Major Indian cities and their states
 */
const cityStateMapping: { [key: string]: string } = {
  'hyderabad': 'Telangana',
  'secunderabad': 'Telangana',
  'bangalore': 'Karnataka',
  'bengaluru': 'Karnataka',
  'mumbai': 'Maharashtra',
  'pune': 'Maharashtra',
  'delhi': 'Delhi',
  'new delhi': 'Delhi',
  'chennai': 'Tamil Nadu',
  'kolkata': 'West Bengal',
  'gurgaon': 'Haryana',
  'gurugram': 'Haryana',
  'noida': 'Uttar Pradesh',
  'ahmedabad': 'Gujarat',
  'jaipur': 'Rajasthan',
  'lucknow': 'Uttar Pradesh',
  'kanpur': 'Uttar Pradesh',
  'nagpur': 'Maharashtra',
  'indore': 'Madhya Pradesh',
  'thane': 'Maharashtra',
  'bhopal': 'Madhya Pradesh',
  'visakhapatnam': 'Andhra Pradesh',
  'pimpri chinchwad': 'Maharashtra',
  'patna': 'Bihar',
  'vadodara': 'Gujarat',
  'ludhiana': 'Punjab',
  'agra': 'Uttar Pradesh',
  'nashik': 'Maharashtra',
  'faridabad': 'Haryana',
  'meerut': 'Uttar Pradesh',
  'rajkot': 'Gujarat',
  'kalyan dombivali': 'Maharashtra',
  'vasai virar': 'Maharashtra',
  'varanasi': 'Uttar Pradesh',
  'srinagar': 'Jammu and Kashmir',
  'aurangabad': 'Maharashtra',
  'dhanbad': 'Jharkhand',
  'amritsar': 'Punjab',
  'navi mumbai': 'Maharashtra',
  'allahabad': 'Uttar Pradesh',
  'ranchi': 'Jharkhand',
  'howrah': 'West Bengal',
  'coimbatore': 'Tamil Nadu',
  'jabalpur': 'Madhya Pradesh',
  'gwalior': 'Madhya Pradesh',
  'vijayawada': 'Andhra Pradesh',
  'jodhpur': 'Rajasthan',
  'madurai': 'Tamil Nadu',
  'raipur': 'Chhattisgarh',
  'kota': 'Rajasthan',
  'guwahati': 'Assam',
  'chandigarh': 'Chandigarh',
  'solapur': 'Maharashtra',
  'hubli dharwad': 'Karnataka',
  'tiruchirappalli': 'Tamil Nadu',
  'bareilly': 'Uttar Pradesh',
  'mysore': 'Karnataka',
  'tiruppur': 'Tamil Nadu',
  'ghaziabad': 'Uttar Pradesh',
  'jalandhar': 'Punjab',
  'bhubaneswar': 'Odisha',
  'salem': 'Tamil Nadu',
  'warangal': 'Telangana',
  'mira bhayandar': 'Maharashtra',
  'thiruvananthapuram': 'Kerala',
  'bhiwandi': 'Maharashtra',
  'saharanpur': 'Uttar Pradesh',
  'guntur': 'Andhra Pradesh',
  'amravati': 'Maharashtra',
  'bikaner': 'Rajasthan',
  'jamshedpur': 'Jharkhand',
  'bhilai nagar': 'Chhattisgarh',
  'cuttack': 'Odisha',
  'firozabad': 'Uttar Pradesh',
  'kochi': 'Kerala',
  'bhavnagar': 'Gujarat',
  'dehradun': 'Uttarakhand',
  'durgapur': 'West Bengal',
  'asansol': 'West Bengal',
  'nanded waghala': 'Maharashtra',
  'kolhapur': 'Maharashtra',
  'ajmer': 'Rajasthan',
  'akola': 'Maharashtra',
  'gulbarga': 'Karnataka',
  'jamnagar': 'Gujarat',
  'ujjain': 'Madhya Pradesh',
  'loni': 'Uttar Pradesh',
  'siliguri': 'West Bengal',
  'jhansi': 'Uttar Pradesh',
  'ulhasnagar': 'Maharashtra',
  'nellore': 'Andhra Pradesh',
  'jammu': 'Jammu and Kashmir',
  'sangli miraj kupwad': 'Maharashtra',
  'belgaum': 'Karnataka',
  'mangalore': 'Karnataka',
  'ambattur': 'Tamil Nadu',
  'tirunelveli': 'Tamil Nadu',
  'malegaon': 'Maharashtra',
  'gaya': 'Bihar',
  'jalgaon': 'Maharashtra',
  'udaipur': 'Rajasthan',
  'maheshtala': 'West Bengal'
};

/**
 * Validate location data for property creation
 */
export function validateLocationData(data: Partial<LocationData>): ValidationResult {
  const errors: string[] = [];
  const normalizedData: LocationData = {
    city: '',
    state: '',
    address: '',
    pincode: ''
  };

  // Validate and normalize city
  if (!data.city || data.city.trim().length === 0) {
    errors.push('City is required');
  } else {
    const cityLower = data.city.toLowerCase().trim();
    normalizedData.city = data.city.trim();

    // Check if city exists in our mapping and auto-fill state if not provided
    if (cityStateMapping[cityLower] && (!data.state || data.state.trim().length === 0)) {
      normalizedData.state = cityStateMapping[cityLower];
    }
  }

  // Validate and normalize state
  if (!data.state || data.state.trim().length === 0) {
    if (!normalizedData.state) {
      errors.push('State is required');
    }
  } else {
    const stateLower = data.state.toLowerCase().trim();
    normalizedData.state = indianStates[stateLower] || data.state.trim();
  }

  // Validate address
  if (!data.address || data.address.trim().length === 0) {
    errors.push('Address is required');
  } else if (data.address.trim().length < 10) {
    errors.push('Address should be at least 10 characters long');
  } else {
    normalizedData.address = data.address.trim();
  }

  // Validate pincode
  if (!data.pincode || data.pincode.trim().length === 0) {
    errors.push('Pincode is required');
  } else {
    const pincode = data.pincode.trim();
    if (!/^\d{6}$/.test(pincode)) {
      errors.push('Pincode should be exactly 6 digits');
    } else {
      normalizedData.pincode = pincode;
    }
  }

  // Cross-validate city and state
  if (normalizedData.city && normalizedData.state) {
    const cityLower = normalizedData.city.toLowerCase();
    const expectedState = cityStateMapping[cityLower];
    if (expectedState && expectedState !== normalizedData.state) {
      errors.push(`${normalizedData.city} is typically in ${expectedState}, not ${normalizedData.state}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    normalizedData: errors.length === 0 ? normalizedData : undefined
  };
}
