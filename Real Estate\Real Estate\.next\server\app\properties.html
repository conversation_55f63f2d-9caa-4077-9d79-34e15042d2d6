<!DOCTYPE html><html lang="en" class="scroll-smooth"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/6650b15f75106e01.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-22534597e9bd48b5.js"/><script src="/_next/static/chunks/4bd1b696-aa487eca046639b4.js" async=""></script><script src="/_next/static/chunks/684-034bfc1bb224a087.js" async=""></script><script src="/_next/static/chunks/main-app-f73c5f7d4ff60b44.js" async=""></script><script src="/_next/static/chunks/874-8e9a565f7eb17c9e.js" async=""></script><script src="/_next/static/chunks/494-d450966e233bb5a9.js" async=""></script><script src="/_next/static/chunks/821-633469a9cf2fe713.js" async=""></script><script src="/_next/static/chunks/527-cc44b4833e8edc34.js" async=""></script><script src="/_next/static/chunks/604-6ed1223f79a7356e.js" async=""></script><script src="/_next/static/chunks/app/properties/page-1390501bae461884.js" async=""></script><title>Real Estate India - Buy, Sell, and Rent Properties</title><meta name="description" content="Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities, or list your property with us. Expert guidance for all your property needs."/><meta name="author" content="Real Estate India"/><meta name="keywords" content="real estate India,property for sale,property for rent,buy property,sell property,apartments,houses,villas,commercial property"/><meta name="creator" content="Real Estate India"/><meta name="publisher" content="Real Estate India"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><link rel="canonical" href="https://realestate-india.com/"/><meta name="format-detection" content="telephone=no, address=no, email=no"/><meta property="og:title" content="Real Estate India - Buy, Sell, and Rent Properties"/><meta property="og:description" content="Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities."/><meta property="og:url" content="https://realestate-india.com/"/><meta property="og:site_name" content="Real Estate India"/><meta property="og:locale" content="en_IN"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:creator" content="@realestateindia"/><meta name="twitter:title" content="Real Estate India - Buy, Sell, and Rent Properties"/><meta name="twitter:description" content="Find your dream home in India with our comprehensive real estate platform."/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_e8ce0c font-sans bg-background text-text-primary antialiased"><div hidden=""><!--$--><!--/$--></div><main class="min-h-screen"><nav class="bg-white/95 backdrop-blur-md shadow-soft sticky top-0 z-50 border-b border-gray-100"><div class="container-custom py-4"><div class="flex justify-between items-center"><a class="flex items-center space-x-3 group" href="/"><div class="relative w-12 h-12 p-2 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl shadow-soft group-hover:shadow-colored transition-all duration-300 transform group-hover:scale-105"></div><div class="flex flex-col"><span class="text-xl font-bold text-gradient">RealEstate</span><span class="text-xs text-text-tertiary font-medium">India</span></div></a><div class="hidden lg:flex items-center space-x-1"><a class="px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300" href="/">Home</a><a class="px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300" href="/buy/">Buy</a><a class="px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300" href="/rent/">Rent</a><a class="px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300" href="/pg/">PG</a><a class="px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300" href="/sell/">Sell</a></div><div class="hidden lg:flex items-center space-x-3"><a class="px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300" href="/login/">Login</a><a class="btn-primary" href="/signup/">Sign Up</a></div><button class="lg:hidden p-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"><span>☰</span></button></div></div></nav><section class="bg-gray-100 py-12"><div class="container-custom"><h1 class="text-3xl md:text-4xl font-bold mb-6">Properties</h1><p class="text-lg text-text-secondary mb-8">Browse our extensive collection of properties for sale and rent. Use the filters to find your perfect match.</p><div class="bg-white/95 backdrop-blur-md rounded-2xl shadow-large border border-white/20 p-8 max-w-6xl mx-auto animate-scale-in"><div class="flex space-x-2 mb-8 bg-gray-100 p-2 rounded-xl"><button class="flex-1 px-6 py-3 rounded-lg font-semibold transition-all duration-300 bg-primary-600 text-white shadow-soft transform scale-105"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path></svg>Buy</button><button class="flex-1 px-6 py-3 rounded-lg font-semibold transition-all duration-300 text-text-secondary hover:text-text-primary hover:bg-white/50">Rent</button><button class="flex-1 px-6 py-3 rounded-lg font-semibold transition-all duration-300 text-text-secondary hover:text-text-primary hover:bg-white/50"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg>PG</button><button class="flex-1 px-6 py-3 rounded-lg font-semibold transition-all duration-300 text-text-secondary hover:text-text-primary hover:bg-white/50"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>Sell</button></div><form><div class="grid grid-cols-1 gap-6 mb-6"><div class="space-y-2"><label for="location" class="block text-sm font-semibold text-text-primary"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>Location</label><div class="relative"><input type="text" id="location" placeholder="Enter city, area (e.g., Hyderabad, Jubilee Hills)" class="input-field pl-12" autoComplete="off" value=""/><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 absolute left-4 top-1/2 transform -translate-y-1/2 text-text-tertiary" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path></svg></div></div></div><div class="grid grid-cols-1 md:grid-cols-3 gap-6"><div class="space-y-2"><label for="propertyType" class="block text-sm font-semibold text-text-primary"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg>Property Type</label><select id="propertyType" class="input-field"><option value="" selected="">Any Type</option><option value="house">🏠 House</option><option value="apartment">🏢 Apartment</option><option value="villa">🏡 Villa</option><option value="plot">📐 Plot</option><option value="commercial">🏪 Commercial</option><option value="office">🏢 Office</option></select></div><div class="space-y-2"><label for="priceRange" class="block text-sm font-semibold text-text-primary"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>Price Range</label><select id="priceRange" class="input-field"><option value="" selected="">Any Budget</option><option value="0-1000000">₹0 - ₹10 Lakh</option><option value="1000000-2500000">₹10 Lakh - ₹25 Lakh</option><option value="2500000-5000000">₹25 Lakh - ₹50 Lakh</option><option value="5000000-10000000">₹50 Lakh - ₹1 Crore</option><option value="10000000-20000000">₹1 Crore - ₹2 Crore</option><option value="20000000+">₹2 Crore+</option></select></div><div class="space-y-2"><label for="bedrooms" class="block text-sm font-semibold text-text-primary"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path></svg>Bedrooms</label><select id="bedrooms" class="input-field"><option value="" selected="">Any Size</option><option value="1">1+ Bedroom</option><option value="2">2+ Bedrooms</option><option value="3">3+ Bedrooms</option><option value="4">4+ Bedrooms</option><option value="5">5+ Bedrooms</option></select></div></div><div class="mt-8 flex justify-center"><button type="submit" class="btn-primary px-12 py-4 text-lg shadow-colored-lg"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg>Search Properties</button></div></form></div></div></section><section data-results="true" class="py-12"><div class="container-custom"><div class="flex flex-col lg:flex-row gap-8"><div class="lg:w-1/4"><div class="bg-white rounded-lg shadow-md p-6"><h2 class="text-xl font-bold mb-6">Filters</h2><div class="mb-6 border-b border-gray-200 pb-6"><div class="flex justify-between items-center cursor-pointer"><h3 class="text-lg font-semibold">Property Type</h3></div><div class="mt-4 space-y-2"><label class="flex items-center space-x-2"><input type="radio" class="form-radio h-5 w-5 text-primary" name="propertyType" checked="" value=""/><span>All Types</span></label><label class="flex items-center space-x-2"><input type="radio" class="form-radio h-5 w-5 text-primary" name="propertyType" value="HOUSE"/><span>House</span></label><label class="flex items-center space-x-2"><input type="radio" class="form-radio h-5 w-5 text-primary" name="propertyType" value="APARTMENT"/><span>Apartment</span></label><label class="flex items-center space-x-2"><input type="radio" class="form-radio h-5 w-5 text-primary" name="propertyType" value="VILLA"/><span>Villa</span></label><label class="flex items-center space-x-2"><input type="radio" class="form-radio h-5 w-5 text-primary" name="propertyType" value="PLOT"/><span>Plot</span></label><label class="flex items-center space-x-2"><input type="radio" class="form-radio h-5 w-5 text-primary" name="propertyType" value="COMMERCIAL"/><span>Commercial</span></label><label class="flex items-center space-x-2"><input type="radio" class="form-radio h-5 w-5 text-primary" name="propertyType" value="OFFICE"/><span>Office</span></label><label class="flex items-center space-x-2"><input type="radio" class="form-radio h-5 w-5 text-primary" name="propertyType" value="PG"/><span>PG (Paying Guest)</span></label></div></div><div class="mb-6 border-b border-gray-200 pb-6"><div class="flex justify-between items-center cursor-pointer"><h3 class="text-lg font-semibold">Price Range</h3></div><div class="mt-4"><div class="flex justify-between mt-4 space-x-4"><div class="w-1/2"><label class="block text-sm font-medium text-gray-700 mb-1">Min Price</label><select class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"><option value="" selected="">No Min</option><option value="500000">₹5 Lakh</option><option value="1000000">₹10 Lakh</option><option value="2000000">₹20 Lakh</option><option value="3000000">₹30 Lakh</option><option value="5000000">₹50 Lakh</option><option value="7500000">₹75 Lakh</option><option value="10000000">₹1 Crore</option><option value="20000000">₹2 Crore</option></select></div><div class="w-1/2"><label class="block text-sm font-medium text-gray-700 mb-1">Max Price</label><select class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"><option value="" selected="">No Max</option><option value="1000000">₹10 Lakh</option><option value="2000000">₹20 Lakh</option><option value="3000000">₹30 Lakh</option><option value="5000000">₹50 Lakh</option><option value="7500000">₹75 Lakh</option><option value="10000000">₹1 Crore</option><option value="20000000">₹2 Crore</option><option value="50000000">₹5 Crore</option></select></div></div></div></div><div class="mb-6 border-b border-gray-200 pb-6"><div class="flex justify-between items-center cursor-pointer"><h3 class="text-lg font-semibold">Bedrooms</h3></div><div class="mt-4 flex flex-wrap gap-2"><button class="px-4 py-2 border rounded-md transition-colors bg-primary text-white border-primary">Any</button><button class="px-4 py-2 border rounded-md transition-colors border-gray-300 hover:bg-primary hover:text-white hover:border-primary">1+</button><button class="px-4 py-2 border rounded-md transition-colors border-gray-300 hover:bg-primary hover:text-white hover:border-primary">2+</button><button class="px-4 py-2 border rounded-md transition-colors border-gray-300 hover:bg-primary hover:text-white hover:border-primary">3+</button><button class="px-4 py-2 border rounded-md transition-colors border-gray-300 hover:bg-primary hover:text-white hover:border-primary">4+</button><button class="px-4 py-2 border rounded-md transition-colors border-gray-300 hover:bg-primary hover:text-white hover:border-primary">5+</button></div></div><div class="mb-6 border-b border-gray-200 pb-6"><div class="flex justify-between items-center cursor-pointer"><h3 class="text-lg font-semibold">Bathrooms</h3></div><div class="mt-4 flex flex-wrap gap-2"><button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors">Any</button><button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors">1+</button><button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors">2+</button><button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors">3+</button><button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-primary hover:text-white hover:border-primary transition-colors">4+</button></div></div><div class="mb-6"><div class="flex justify-between items-center cursor-pointer"><h3 class="text-lg font-semibold">Amenities</h3></div><div class="mt-4 space-y-2"><label class="flex items-center space-x-2"><input type="checkbox" class="form-checkbox h-5 w-5 text-primary"/><span>Air Conditioning</span></label><label class="flex items-center space-x-2"><input type="checkbox" class="form-checkbox h-5 w-5 text-primary"/><span>Swimming Pool</span></label><label class="flex items-center space-x-2"><input type="checkbox" class="form-checkbox h-5 w-5 text-primary"/><span>Gym</span></label><label class="flex items-center space-x-2"><input type="checkbox" class="form-checkbox h-5 w-5 text-primary"/><span>Balcony</span></label><label class="flex items-center space-x-2"><input type="checkbox" class="form-checkbox h-5 w-5 text-primary"/><span>Parking</span></label><label class="flex items-center space-x-2"><input type="checkbox" class="form-checkbox h-5 w-5 text-primary"/><span>Furnished</span></label><label class="flex items-center space-x-2"><input type="checkbox" class="form-checkbox h-5 w-5 text-primary"/><span>Pet Friendly</span></label></div></div><div class="mb-6 border-b border-gray-200 pb-6"><h3 class="text-lg font-semibold mb-4">City</h3><input type="text" placeholder="Enter city name" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" value=""/></div><button class="btn-secondary w-full">Reset Filters</button></div></div><div class="lg:w-3/4"><div class="flex justify-between items-center mb-6"><p class="text-text-secondary">Showing <span class="font-semibold">0</span> of <span class="font-semibold">0</span> properties</p><div class="flex items-center space-x-2"><label for="sort" class="text-text-secondary">Sort by:</label><select id="sort" class="border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"><option value="newest">Newest</option><option value="price-asc">Price (Low to High)</option><option value="price-desc">Price (High to Low)</option></select></div></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"><div class="animate-pulse"><div class="bg-gray-300 h-48 rounded-lg mb-4"></div><div class="h-4 bg-gray-300 rounded mb-2"></div><div class="h-4 bg-gray-300 rounded w-3/4"></div></div><div class="animate-pulse"><div class="bg-gray-300 h-48 rounded-lg mb-4"></div><div class="h-4 bg-gray-300 rounded mb-2"></div><div class="h-4 bg-gray-300 rounded w-3/4"></div></div><div class="animate-pulse"><div class="bg-gray-300 h-48 rounded-lg mb-4"></div><div class="h-4 bg-gray-300 rounded mb-2"></div><div class="h-4 bg-gray-300 rounded w-3/4"></div></div><div class="animate-pulse"><div class="bg-gray-300 h-48 rounded-lg mb-4"></div><div class="h-4 bg-gray-300 rounded mb-2"></div><div class="h-4 bg-gray-300 rounded w-3/4"></div></div><div class="animate-pulse"><div class="bg-gray-300 h-48 rounded-lg mb-4"></div><div class="h-4 bg-gray-300 rounded mb-2"></div><div class="h-4 bg-gray-300 rounded w-3/4"></div></div><div class="animate-pulse"><div class="bg-gray-300 h-48 rounded-lg mb-4"></div><div class="h-4 bg-gray-300 rounded mb-2"></div><div class="h-4 bg-gray-300 rounded w-3/4"></div></div></div></div></div></div></section><footer class="bg-gradient-to-br from-secondary-900 via-secondary-800 to-secondary-900 text-white pt-20 pb-8 relative overflow-hidden"><div class="absolute inset-0 opacity-5"><div class="absolute top-0 left-0 w-full h-full" style="background-image:url(&quot;data:image/svg+xml,%3Csvg width=&#x27;60&#x27; height=&#x27;60&#x27; viewBox=&#x27;0 0 60 60&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27;%3E%3Cg fill=&#x27;none&#x27; fill-rule=&#x27;evenodd&#x27;%3E%3Cg fill=&#x27;%23ffffff&#x27; fill-opacity=&#x27;0.1&#x27;%3E%3Ccircle cx=&#x27;30&#x27; cy=&#x27;30&#x27; r=&#x27;2&#x27;/%3E%3C/g%3E%3C/g%3E%3C/svg%3E&quot;)"></div></div><div class="container-custom relative z-10"><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-16"><div class="lg:col-span-2"><div class="flex items-center space-x-3 mb-6"><div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center"><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-white"><path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><path d="M9 22V12H15V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><div><h3 class="text-2xl font-bold text-gradient bg-gradient-to-r from-white to-primary-200 bg-clip-text text-transparent">RealEstate</h3><p class="text-sm text-gray-300">India</p></div></div><p class="text-gray-300 mb-6 leading-relaxed max-w-md">Your trusted partner in finding the perfect property across India. Whether you&#x27;re buying, selling, or renting, we provide expert guidance and comprehensive solutions for all your real estate needs.</p><div class="flex space-x-4"><a href="#" class="w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-300 transform hover:scale-110"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd"></path></svg></a><a href="#" class="w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-300 transform hover:scale-110"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path></svg></a><a href="#" class="w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-300 transform hover:scale-110"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd"></path></svg></a><a href="#" class="w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-300 transform hover:scale-110"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path fill-rule="evenodd" d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" clip-rule="evenodd"></path></svg></a></div></div><div><h3 class="text-lg font-semibold mb-4">Quick Links</h3><ul class="space-y-3"><li><a class="text-gray-400 hover:text-white transition-colors duration-300" href="/properties/">Properties</a></li><li><a class="text-gray-400 hover:text-white transition-colors duration-300" href="/buy/">Buy</a></li><li><a class="text-gray-400 hover:text-white transition-colors duration-300" href="/rent/">Rent</a></li><li><a class="text-gray-400 hover:text-white transition-colors duration-300" href="/sell/">Sell</a></li><li><a class="text-gray-400 hover:text-white transition-colors duration-300" href="/about/">About Us</a></li><li><a class="text-gray-400 hover:text-white transition-colors duration-300" href="/contact/">Contact Us</a></li></ul></div><div><h3 class="text-lg font-semibold mb-4">Services</h3><ul class="space-y-3"><li><a class="text-gray-400 hover:text-white transition-colors duration-300" href="/services/property-management/">Property Management</a></li><li><a class="text-gray-400 hover:text-white transition-colors duration-300" href="/services/property-valuation/">Property Valuation</a></li><li><a class="text-gray-400 hover:text-white transition-colors duration-300" href="/services/mortgage-services/">Mortgage Services</a></li><li><a class="text-gray-400 hover:text-white transition-colors duration-300" href="/services/legal-services/">Legal Services</a></li><li><a class="text-gray-400 hover:text-white transition-colors duration-300" href="/services/investment-advisory/">Investment Advisory</a></li></ul></div><div><h3 class="text-lg font-semibold mb-4">Contact Us</h3><ul class="space-y-3"><li class="flex items-start space-x-3"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg><span class="text-gray-400"><EMAIL></span></li><li class="flex items-start space-x-3"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg><span class="text-gray-400">Mon-Fri: 9AM - 6PM<br/>Sat: 10AM - 4PM<br/>Sun: Closed</span></li></ul></div></div><div class="pt-8 border-t border-gray-800 text-center md:flex md:justify-between md:text-left"><p class="text-gray-400 mb-4 md:mb-0">© <!-- -->2025<!-- --> RealEstate. All rights reserved.</p><div class="space-x-6"><a class="text-gray-400 hover:text-white transition-colors duration-300" href="/privacy-policy/">Privacy Policy</a><a class="text-gray-400 hover:text-white transition-colors duration-300" href="/terms-of-service/">Terms of Service</a><a class="text-gray-400 hover:text-white transition-colors duration-300" href="/sitemap/">Sitemap</a></div></div></div></footer></main><!--$--><!--/$--><script src="/_next/static/chunks/webpack-22534597e9bd48b5.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n4:I[894,[],\"ClientPageRoot\"]\n5:I[796,[\"874\",\"static/chunks/874-8e9a565f7eb17c9e.js\",\"494\",\"static/chunks/494-d450966e233bb5a9.js\",\"821\",\"static/chunks/821-633469a9cf2fe713.js\",\"527\",\"static/chunks/527-cc44b4833e8edc34.js\",\"604\",\"static/chunks/604-6ed1223f79a7356e.js\",\"754\",\"static/chunks/app/properties/page-1390501bae461884.js\"],\"default\"]\n8:I[9665,[],\"OutletBoundary\"]\nb:I[4911,[],\"AsyncMetadataOutlet\"]\nd:I[9665,[],\"ViewportBoundary\"]\nf:I[9665,[],\"MetadataBoundary\"]\n11:I[6614,[],\"\"]\n:HL[\"/_next/static/css/6650b15f75106e01.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"2dXEMPZY34AwPIDvCx8jz\",\"p\":\"\",\"c\":[\"\",\"properties\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"properties\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/6650b15f75106e01.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"scroll-smooth\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_e8ce0c font-sans bg-background text-text-primary antialiased\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"properties\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@6\",\"$@7\"]}],null,[\"$\",\"$L8\",null,{\"children\":[\"$L9\",\"$La\",[\"$\",\"$Lb\",null,{\"promise\":\"$@c\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"oR25p8vrerOWKd4P_0A6Dv\",{\"children\":[[\"$\",\"$Ld\",null,{\"children\":\"$Le\"}],null]}],[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"12:\"$Sreact.suspense\"\n13:I[4911,[],\"AsyncMetadata\"]\n6:{}\n7:{}\n10:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$12\",null,{\"fallback\":null,\"children\":[\"$\",\"$L13\",null,{\"promise\":\"$@14\"}]}]}]\n"])</script><script>self.__next_f.push([1,"a:null\n"])</script><script>self.__next_f.push([1,"e:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,"c:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Real Estate India - Buy, Sell, and Rent Properties\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities, or list your property with us. Expert guidance for all your property needs.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"Real Estate India\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"real estate India,property for sale,property for rent,buy property,sell property,apartments,houses,villas,commercial property\"}],[\"$\",\"meta\",\"4\",{\"name\":\"creator\",\"content\":\"Real Estate India\"}],[\"$\",\"meta\",\"5\",{\"name\":\"publisher\",\"content\":\"Real Estate India\"}],[\"$\",\"meta\",\"6\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"7\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"link\",\"8\",{\"rel\":\"canonical\",\"href\":\"https://realestate-india.com/\"}],[\"$\",\"meta\",\"9\",{\"name\":\"format-detection\",\"content\":\"telephone=no, address=no, email=no\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:title\",\"content\":\"Real Estate India - Buy, Sell, and Rent Properties\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:description\",\"content\":\"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities.\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:url\",\"content\":\"https://realestate-india.com/\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:site_name\",\"content\":\"Real Estate India\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:locale\",\"content\":\"en_IN\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:creator\",\"content\":\"@realestateindia\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:title\",\"content\":\"Real Estate India - Buy, Sell, and Rent Properties\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:description\",\"content\":\"Find your dream home in India with our comprehensive real estate platform.\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"14:{\"metadata\":\"$c:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>