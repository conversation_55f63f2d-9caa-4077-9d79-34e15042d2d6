<?php
// Basic authentication - replace with proper auth later
session_start();
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit;
}

// Database connection
try {
    $db = new PDO('sqlite:../Real Estate/prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die('Connection failed: ' . $e->getMessage());
}

// Handle POST actions for properties
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $propertyId = $_POST['property_id'] ?? '';
    switch ($_POST['action']) {
        case 'approve':
            $stmt = $db->prepare("UPDATE Property SET approvalStatus = 'APPROVED', isApproved = 1, approvedAt = CURRENT_TIMESTAMP, approvedBy = :adminId WHERE id = :id");
            $stmt->execute(['id' => $propertyId, 'adminId' => $_SESSION['admin_id']]);
            break;
        case 'reject':
            $reason = $_POST['rejection_reason'] ?? '';
            $stmt = $db->prepare("UPDATE Property SET approvalStatus = 'REJECTED', rejectionReason = :reason WHERE id = :id");
            $stmt->execute(['id' => $propertyId, 'reason' => $reason]);
            break;
        case 'delete':
            $stmt = $db->prepare("DELETE FROM Property WHERE id = :id");
            $stmt->execute(['id' => $propertyId]);
            break;
        case 'delete_user':
            $userId = $_POST['user_id'] ?? '';
            if ($userId !== $_SESSION['admin_id']) {
                $stmt = $db->prepare("DELETE FROM User WHERE id = :id");
                $stmt->execute(['id' => $userId]);
            }
            header('Location: index.php?tab=users');
            exit;
    }
    header('Location: index.php?tab=properties');
    exit;
}

// Fetch counts for overview
$propertyCount = $db->query('SELECT COUNT(*) FROM Property')->fetchColumn();
$userCount = $db->query('SELECT COUNT(*) FROM User')->fetchColumn();
$postCount = $db->query('SELECT COUNT(*) FROM BlogPost')->fetchColumn();

// Fetch data for tabs
$tab = $_GET['tab'] ?? 'overview';
if ($tab === 'properties') {
    $pendingProperties = $db->query("SELECT p.*, u.name as owner_name, u.email as owner_email FROM Property p JOIN User u ON p.ownerId = u.id WHERE approvalStatus = 'PENDING'")->fetchAll(PDO::FETCH_ASSOC);
    $allProperties = $db->query("SELECT p.*, u.name as owner_name, u.email as owner_email, (SELECT COUNT(*) FROM Inquiry WHERE propertyId = p.id) as inquiries_count, (SELECT COUNT(*) FROM SavedProperty WHERE propertyId = p.id) as saved_count FROM Property p JOIN User u ON p.ownerId = u.id")->fetchAll(PDO::FETCH_ASSOC);
} elseif ($tab === 'users') {
    $users = $db->query("SELECT u.*, (SELECT COUNT(*) FROM Property WHERE ownerId = u.id) as properties_count FROM User u")->fetchAll(PDO::FETCH_ASSOC);
} elseif ($tab === 'blog') {
    $posts = $db->query("SELECT b.*, u.name as author_name FROM BlogPost b JOIN User u ON b.authorId = u.id ORDER BY createdAt DESC")->fetchAll(PDO::FETCH_ASSOC);
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <aside class="w-64 bg-gray-800 text-white p-4">
            <h2 class="text-xl font-semibold mb-4">Admin Navigation</h2>
            <nav class="space-y-1">
                <a href="?tab=overview" class="block px-3 py-2 rounded-md hover:bg-gray-700">Dashboard Overview</a>

                <a href="?tab=properties" class="block px-3 py-2 rounded-md hover:bg-gray-700">Properties Management</a>
                <a href="?tab=users" class="block px-3 py-2 rounded-md hover:bg-gray-700">User Management</a>
                <a href="?tab=blog" class="block px-3 py-2 rounded-md hover:bg-gray-700">Blog Post Management</a>
                
                <div class="border-t border-gray-600 mt-4 pt-4">
                    <a href="logout.php" class="block px-3 py-2 rounded-md hover:bg-red-600 text-red-300 hover:text-white">Logout</a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-6">Admin Dashboard</h1>

            <?php if (!isset($_GET['tab']) || $_GET['tab'] === 'overview'): ?>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-700 mb-4">Total Properties</h2>
                        <p class="text-4xl font-bold text-indigo-600"><?php echo $propertyCount; ?></p>
                    </div>
                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-700 mb-4">Total Users</h2>
                        <p class="text-4xl font-bold text-indigo-600"><?php echo $userCount; ?></p>
                    </div>
                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-700 mb-4">Total Blog Posts</h2>
                        <p class="text-4xl font-bold text-indigo-600"><?php echo $postCount; ?></p>
                    </div>
                </div>

            <?php elseif ($tab === 'properties'): ?>
                <div class="bg-white shadow rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-4">Properties Management</h2>
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">Pending Approvals</h3>
                    <table class="min-w-full bg-white">
                        <thead>
                            <tr>
                                <th class="py-2 px-4 border-b text-left">Title</th>
                                <th class="py-2 px-4 border-b text-left">Owner</th>
                                <th class="py-2 px-4 border-b text-left">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($pendingProperties as $prop): ?>
                                <tr>
                                    <td class="py-2 px-4 border-b"><?php echo htmlspecialchars($prop['title']); ?></td>
                                    <td class="py-2 px-4 border-b"><?php echo htmlspecialchars($prop['owner_name']) . ' (' . htmlspecialchars($prop['owner_email']) . ')'; ?></td>
                                    <td class="py-2 px-4 border-b">
                                        <form method="POST" class="inline">
                                            <input type="hidden" name="action" value="approve">
                                            <input type="hidden" name="property_id" value="<?php echo $prop['id']; ?>">
                                            <button type="submit" class="text-green-600 hover:underline">Approve</button>
                                        </form>
                                        <form method="POST" class="inline ml-2">
                                            <input type="hidden" name="action" value="reject">
                                            <input type="hidden" name="property_id" value="<?php echo $prop['id']; ?>">
                                            <input type="text" name="rejection_reason" placeholder="Reason" class="border p-1">
                                            <button type="submit" class="text-red-600 hover:underline">Reject</button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                    <h3 class="text-lg font-semibold text-gray-700 mt-8 mb-4">All Properties</h3>
                    <table class="min-w-full bg-white">
                        <thead>
                            <tr>
                                <th class="py-2 px-4 border-b text-left">Title</th>
                                <th class="py-2 px-4 border-b text-left">Price</th>
                                <th class="py-2 px-4 border-b text-left">Status</th>
                                <th class="py-2 px-4 border-b text-left">Views</th>
                                <th class="py-2 px-4 border-b text-left">Owner</th>
                                <th class="py-2 px-4 border-b text-left">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($allProperties as $prop): ?>
                                <tr>
                                    <td class="py-2 px-4 border-b"><?php echo htmlspecialchars($prop['title']); ?></td>
                                    <td class="py-2 px-4 border-b"><?php echo htmlspecialchars($prop['currency']) . ' ' . number_format($prop['price']); ?></td>
                                    <td class="py-2 px-4 border-b"><?php echo htmlspecialchars($prop['approvalStatus']); ?></td>
                                    <td class="py-2 px-4 border-b"><?php echo $prop['viewCount']; ?></td>
                                    <td class="py-2 px-4 border-b"><?php echo htmlspecialchars($prop['owner_name']) . ' (' . htmlspecialchars($prop['owner_email']) . ')'; ?></td>
                                    <td class="py-2 px-4 border-b">
                                        <!-- Edit link would need a separate page -->
                                        <a href="#" class="text-indigo-600 hover:underline mr-2">Edit</a>
                                        <form method="POST" class="inline">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="property_id" value="<?php echo $prop['id']; ?>">
                                            <button type="submit" class="text-red-600 hover:underline" onclick="return confirm('Are you sure?')">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php elseif ($tab === 'users'): ?>
                <div class="bg-white shadow rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-4">User Management</h2>
                    <table class="min-w-full bg-white">
                        <thead>
                            <tr>
                                <th class="py-2 px-4 border-b text-left">Name</th>
                                <th class="py-2 px-4 border-b text-left">Email</th>
                                <th class="py-2 px-4 border-b text-left">Role</th>
                                <th class="py-2 px-4 border-b text-left">Properties</th>
                                <th class="py-2 px-4 border-b text-left">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td class="py-2 px-4 border-b"><?php echo htmlspecialchars($user['name'] ?? ''); ?></td>
                                    <td class="py-2 px-4 border-b"><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td class="py-2 px-4 border-b"><?php echo htmlspecialchars($user['role']); ?></td>
                                    <td class="py-2 px-4 border-b"><?php echo $user['properties_count']; ?></td>
                                    <td class="py-2 px-4 border-b">
                                        <form method="POST" class="inline">
                                            <input type="hidden" name="action" value="delete_user">
                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                            <button type="submit" class="text-red-600 hover:underline" onclick="return confirm('Are you sure?') && <?php echo $user['id'] === $_SESSION['admin_id'] ? 'false' : 'true'; ?>">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php // Add other tabs similarly ?>
            <?php endif; ?>
        </main>
    </div>
</body>
</html>

// Add to POST handler
        case 'create_post':
            $title = $_POST['title'] ?? '';
            $content = $_POST['content'] ?? '';
            $slug = $_POST['slug'] ?? '';
            // Add other fields as needed
            if ($title && $content && $slug) {
                $stmt = $db->prepare("INSERT INTO BlogPost (title, content, slug, authorId, createdAt, updatedAt) VALUES (:title, :content, :slug, :authorId, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)");
                $stmt->execute(['title' => $title, 'content' => $content, 'slug' => $slug, 'authorId' => $_SESSION['admin_id']]);
            }
            break;
        case 'delete_post':
            $postId = $_POST['post_id'] ?? '';
            $stmt = $db->prepare("DELETE FROM BlogPost WHERE id = :id");
            $stmt->execute(['id' => $postId]);
            break;

// Note: For edit, would need a separate form or modal, simplifying here





