#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Real Estate Complete Deployment Script');
console.log('==========================================\n');

// Step 1: Build the Next.js application
console.log('📦 Building Next.js application...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ Build completed successfully!\n');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

// Step 2: Check if out folder exists
const outDir = path.join(__dirname, 'out');
if (!fs.existsSync(outDir)) {
  console.error('❌ Out folder not found. Build may have failed.');
  process.exit(1);
}

// Step 3: Check if php-backend folder exists
const phpBackendDir = path.join(__dirname, 'php-backend');
if (!fs.existsSync(phpBackendDir)) {
  console.error('❌ PHP backend folder not found.');
  process.exit(1);
}

console.log('✅ Static files generated in "out" folder');
console.log('✅ PHP backend ready in "php-backend" folder\n');

// Step 4: Create deployment package info
console.log('📋 Complete Deployment Package Ready:');
console.log('=====================================');
console.log('Frontend (Static): ./out/');
console.log('Backend (PHP): ./php-backend/');
console.log('Database Schema: ./php-backend/config/database-simple.sql');
console.log('Blog Data: ./php-backend/config/blog-data.sql');
console.log('Sample Properties: ./php-backend/config/sample-properties.sql');
console.log('Deployment Guide: ./DEPLOYMENT_GUIDE.md\n');

// Step 5: Show next steps
console.log('🎯 Complete Deployment Steps:');
console.log('=============================');
console.log('1. Upload contents of "out" folder to your public_html directory');
console.log('2. Upload "php-backend" folder to public_html/php-backend/');
console.log('3. Create MySQL database in hosting control panel');
console.log('4. Import database-simple.sql (main tables)');
console.log('5. Import blog-data.sql (blog posts)');
console.log('6. Import sample-properties.sql (sample data)');
console.log('7. Update database credentials in php-backend/config/database.php');
console.log('8. Set folder permissions (uploads folder to 755)');
console.log('9. Test all features with debug-all.html');
console.log('10. Your complete real estate website is ready!\n');

console.log('📖 For detailed instructions, see DEPLOYMENT_GUIDE.md');
console.log('🎉 Your hybrid Real Estate application is ready for shared hosting!');

// Step 6: Create a simple deployment checklist
const checklist = `
# 📋 Deployment Checklist

## Pre-Upload
- [ ] Built Next.js application (npm run build)
- [ ] Verified 'out' folder exists with static files
- [ ] Verified 'php-backend' folder exists with PHP APIs

## Upload to Shared Hosting
- [ ] Uploaded all contents of 'out' folder to public_html/
- [ ] Uploaded 'php-backend' folder to public_html/php-backend/
- [ ] Created uploads folder: public_html/php-backend/uploads/

## Database Setup
- [ ] Created MySQL database in hosting control panel
- [ ] Imported database.sql schema
- [ ] Updated database credentials in php-backend/config/database.php

## Permissions & Security
- [ ] Set uploads folder permissions to 755
- [ ] Set PHP files permissions to 644
- [ ] Added .htaccess protection for config files

## Testing
- [ ] Frontend loads: https://yourdomain.com
- [ ] API responds: https://yourdomain.com/php-backend/api/auth/check-session.php
- [ ] User registration works
- [ ] Property creation works
- [ ] File upload works

## Go Live!
- [ ] All tests passed
- [ ] Application is live and functional
`;

fs.writeFileSync(path.join(__dirname, 'DEPLOYMENT_CHECKLIST.md'), checklist);
console.log('📝 Created DEPLOYMENT_CHECKLIST.md for your reference');
