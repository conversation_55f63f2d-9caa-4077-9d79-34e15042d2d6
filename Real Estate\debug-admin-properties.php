<?php
/**
 * Debug Admin Properties API
 * Tests the admin properties endpoint and shows detailed response
 */
?>
<!DOCTYPE html>
<html>
<head>
    <title>🐛 Debug Admin Properties - housing.okayy.in</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 20px auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .test-item { padding: 10px; margin: 5px 0; background: #f8f9fa; border-radius: 5px; }
        .btn { background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; max-height: 400px; }
        .json-output { background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 Debug Admin Properties API</h1>
        <p><strong>Purpose:</strong> Debug why Manage Properties page shows no data</p>
        
        <?php
        // Test 1: Database Connection
        echo "<div class='test-section'>";
        echo "<h2>1. 🔗 Database Connection Test</h2>";
        
        try {
            require_once 'php-backend/config/database.php';
            $database = new Database();
            $db = $database->getConnection();
            echo "<div class='test-item'><span class='success'>✅ Database connection: Working</span></div>";
            
            // Test properties table
            $stmt = $db->query("SELECT COUNT(*) as count FROM properties");
            $result = $stmt->fetch();
            $propertyCount = $result['count'];
            echo "<div class='test-item'><span class='info'>🏠 Total properties in database: $propertyCount</span></div>";
            
            // Test users table
            $stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE role = 'ADMIN'");
            $result = $stmt->fetch();
            $adminCount = $result['count'];
            echo "<div class='test-item'><span class='info'>👑 Admin users: $adminCount</span></div>";
            
        } catch (Exception $e) {
            echo "<div class='test-item'><span class='error'>❌ Database error: " . $e->getMessage() . "</span></div>";
        }
        echo "</div>";
        
        // Test 2: Check if saved_properties table exists
        echo "<div class='test-section'>";
        echo "<h2>2. 📋 Table Structure Check</h2>";
        
        try {
            // Check if saved_properties table exists
            $stmt = $db->query("SHOW TABLES LIKE 'saved_properties'");
            $savedPropertiesExists = $stmt->rowCount() > 0;
            
            if ($savedPropertiesExists) {
                echo "<div class='test-item'><span class='success'>✅ saved_properties table: Exists</span></div>";
            } else {
                echo "<div class='test-item'><span class='warning'>⚠️ saved_properties table: Missing</span></div>";
            }
            
            // Check if inquiries table exists
            $stmt = $db->query("SHOW TABLES LIKE 'inquiries'");
            $inquiriesExists = $stmt->rowCount() > 0;
            
            if ($inquiriesExists) {
                echo "<div class='test-item'><span class='success'>✅ inquiries table: Exists</span></div>";
            } else {
                echo "<div class='test-item'><span class='warning'>⚠️ inquiries table: Missing</span></div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='test-item'><span class='error'>❌ Table check error: " . $e->getMessage() . "</span></div>";
        }
        echo "</div>";
        
        // Test 3: Test Admin Properties API Directly
        echo "<div class='test-section'>";
        echo "<h2>3. 🔌 Admin Properties API Test</h2>";
        
        // Test the API endpoint directly
        echo "<div class='test-item'>";
        echo "<strong>Testing /php-backend/api/admin/properties.php</strong><br>";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://' . $_SERVER['HTTP_HOST'] . '/php-backend/api/admin/properties.php');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "HTTP Status: $httpCode<br>";
        
        if ($httpCode == 200) {
            echo "<span class='success'>✅ API accessible</span><br>";
            $jsonData = json_decode($response, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                echo "<span class='success'>✅ Valid JSON response</span><br>";
                echo "<div class='json-output'>";
                echo "<strong>API Response:</strong><br>";
                echo "<pre>" . json_encode($jsonData, JSON_PRETTY_PRINT) . "</pre>";
                echo "</div>";
            } else {
                echo "<span class='error'>❌ Invalid JSON: " . json_last_error_msg() . "</span><br>";
                echo "<div class='json-output'>";
                echo "<strong>Raw response:</strong><br>";
                echo "<pre>" . htmlspecialchars($response) . "</pre>";
                echo "</div>";
            }
        } elseif ($httpCode == 403) {
            echo "<span class='warning'>🔒 Access forbidden (not logged in as admin)</span><br>";
            echo "<div class='json-output'>";
            echo "<strong>Response:</strong><br>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
            echo "</div>";
        } else {
            echo "<span class='error'>❌ HTTP Error: $httpCode</span><br>";
            echo "<div class='json-output'>";
            echo "<strong>Response:</strong><br>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
            echo "</div>";
        }
        echo "</div>";
        echo "</div>";
        
        // Test 4: Manual Query Test
        echo "<div class='test-section'>";
        echo "<h2>4. 🔍 Manual Query Test</h2>";
        
        try {
            // Test the exact query used by the API
            $query = "SELECT p.*, u.name as owner_name, u.email as owner_email
                      FROM properties p 
                      JOIN users u ON p.owner_id = u.id 
                      ORDER BY p.created_at DESC 
                      LIMIT 5";
            
            $stmt = $db->prepare($query);
            $stmt->execute();
            $properties = $stmt->fetchAll();
            
            echo "<div class='test-item'>";
            echo "<span class='success'>✅ Query executed successfully</span><br>";
            echo "<strong>Found " . count($properties) . " properties</strong>";
            echo "</div>";
            
            if (count($properties) > 0) {
                echo "<div class='json-output'>";
                echo "<strong>Sample property data:</strong><br>";
                echo "<pre>" . json_encode($properties[0], JSON_PRETTY_PRINT) . "</pre>";
                echo "</div>";
            } else {
                echo "<div class='test-item'>";
                echo "<span class='warning'>⚠️ No properties found in database</span>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='test-item'><span class='error'>❌ Query error: " . $e->getMessage() . "</span></div>";
        }
        echo "</div>";
        
        // Test 5: Check Session Authentication
        echo "<div class='test-section'>";
        echo "<h2>5. 🔐 Session Authentication Test</h2>";
        
        echo "<div class='test-item'>";
        if (isset($_COOKIE['session_token'])) {
            echo "<span class='info'>🍪 Session token found in cookies</span><br>";
            $session_token = $_COOKIE['session_token'];
            
            try {
                $query = "SELECT u.*, s.expires_at FROM users u 
                          JOIN user_sessions s ON u.id = s.user_id 
                          WHERE s.session_token = :token";
                
                $stmt = $db->prepare($query);
                $stmt->bindParam(':token', $session_token);
                $stmt->execute();
                $user = $stmt->fetch();
                
                if ($user) {
                    echo "<span class='success'>✅ Valid session found</span><br>";
                    echo "<strong>User:</strong> " . htmlspecialchars($user['name']) . " (" . $user['role'] . ")<br>";
                    echo "<strong>Expires:</strong> " . $user['expires_at'] . "<br>";
                    
                    if ($user['role'] === 'ADMIN') {
                        echo "<span class='success'>✅ User has admin privileges</span>";
                    } else {
                        echo "<span class='warning'>⚠️ User is not an admin</span>";
                    }
                } else {
                    echo "<span class='error'>❌ Invalid or expired session</span>";
                }
            } catch (Exception $e) {
                echo "<span class='error'>❌ Session check error: " . $e->getMessage() . "</span>";
            }
        } else {
            echo "<span class='warning'>⚠️ No session token found in cookies</span>";
        }
        echo "</div>";
        echo "</div>";
        ?>
        
        <div class="test-section">
            <h2>6. 🔧 Recommended Fixes</h2>
            <div class="test-item">
                <strong>Based on the test results above:</strong>
                <ol>
                    <li>If no properties found: Add some test properties to the database</li>
                    <li>If authentication fails: Login as admin first</li>
                    <li>If saved_properties table missing: Create the table or modify the query</li>
                    <li>If API returns 403: Check admin session and login status</li>
                </ol>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="/admin/login" class="btn">👑 Admin Login</a>
            <a href="/admin/properties" class="btn">🏠 Test Properties Page</a>
            <a href="/admin/dashboard" class="btn">📊 Admin Dashboard</a>
        </div>
        
        <div class="test-section">
            <h2>🗑️ Cleanup</h2>
            <p><strong>After testing, delete this file:</strong> <code>debug-admin-properties.php</code></p>
        </div>
    </div>
</body>
</html>
