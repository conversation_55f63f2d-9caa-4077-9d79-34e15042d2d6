'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'

export default function OkayHomePage() {
  const [activeService, setActiveService] = useState(0)

  const services = [
    {
      id: 'real-estate',
      title: 'Real Estate',
      description: 'Find your dream home or investment property',
      image: '/images/real-estate-hero.jpg',
      icon: '🏠',
      color: 'from-blue-500 to-blue-600',
      link: '/',
      features: ['Buy Properties', 'Rent Homes', 'PG Accommodation', 'Sell Property']
    },
    {
      id: 'groceries',
      title: 'Groceries',
      description: 'Fresh groceries delivered to your doorstep',
      image: '/images/groceries-hero.jpg',
      icon: '🛒',
      color: 'from-green-500 to-green-600',
      link: '/groceries',
      features: ['Fresh Vegetables', 'Daily Essentials', 'Organic Products', 'Quick Delivery']
    },
    {
      id: 'jobs',
      title: 'Jobs',
      description: 'Discover career opportunities that match your skills',
      image: '/images/jobs-hero.jpg',
      icon: '💼',
      color: 'from-purple-500 to-purple-600',
      link: '/jobs',
      features: ['Job Search', 'Career Guidance', 'Resume Builder', 'Interview Prep']
    },
    {
      id: 'tools',
      title: 'Online Tools',
      description: 'Powerful tools to boost your productivity',
      image: '/images/tools-hero.jpg',
      icon: '🛠️',
      color: 'from-orange-500 to-orange-600',
      link: '/tools',
      features: ['PDF Tools', 'Image Editor', 'Calculators', 'Converters']
    }
  ]

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/okayy" className="text-2xl font-bold text-gray-900">
                Okayy<span className="text-blue-600">.in</span>
              </Link>
            </div>
            <nav className="hidden md:flex space-x-8">
              <Link href="#services" className="text-gray-600 hover:text-gray-900">Services</Link>
              <Link href="#about" className="text-gray-600 hover:text-gray-900">About</Link>
              <Link href="#contact" className="text-gray-600 hover:text-gray-900">Contact</Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Your Gateway to
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
              Everything You Need
            </span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            From finding your dream home to getting groceries delivered, discovering career opportunities, 
            and accessing powerful online tools - all in one place.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="#services" 
              className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              Explore Services
            </Link>
            <Link 
              href="#about" 
              className="border border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
            >
              Learn More
            </Link>
          </div>
        </div>
      </section>

      {/* Services Gallery */}
      <section id="services" className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Services
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Comprehensive solutions for your daily needs, all under one roof
            </p>
          </div>

          {/* Service Cards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {services.map((service, index) => (
              <div
                key={service.id}
                className={`group relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden cursor-pointer transform hover:-translate-y-2 ${
                  activeService === index ? 'ring-2 ring-blue-500' : ''
                }`}
                onClick={() => setActiveService(index)}
              >
                <div className={`h-48 bg-gradient-to-br ${service.color} flex items-center justify-center`}>
                  <span className="text-6xl">{service.icon}</span>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{service.title}</h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  <ul className="space-y-1 mb-4">
                    {service.features.map((feature, idx) => (
                      <li key={idx} className="text-sm text-gray-500 flex items-center">
                        <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Link
                    href={service.link}
                    className={`inline-flex items-center text-white bg-gradient-to-r ${service.color} px-4 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300`}
                  >
                    Explore {service.title}
                    <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </div>
            ))}
          </div>

          {/* Featured Service Showcase */}
          <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
            <div className="grid grid-cols-1 lg:grid-cols-2">
              <div className="p-8 lg:p-12">
                <div className="flex items-center mb-4">
                  <span className="text-4xl mr-4">{services[activeService].icon}</span>
                  <h3 className="text-2xl font-bold text-gray-900">{services[activeService].title}</h3>
                </div>
                <p className="text-gray-600 mb-6 text-lg">{services[activeService].description}</p>
                <div className="grid grid-cols-2 gap-4 mb-8">
                  {services[activeService].features.map((feature, idx) => (
                    <div key={idx} className="flex items-center">
                      <div className={`w-3 h-3 bg-gradient-to-r ${services[activeService].color} rounded-full mr-3`}></div>
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>
                <Link
                  href={services[activeService].link}
                  className={`inline-flex items-center text-white bg-gradient-to-r ${services[activeService].color} px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300`}
                >
                  Get Started
                  <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </Link>
              </div>
              <div className={`bg-gradient-to-br ${services[activeService].color} flex items-center justify-center min-h-[300px]`}>
                <span className="text-8xl opacity-20">{services[activeService].icon}</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">10K+</div>
              <div className="text-gray-600">Properties Listed</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-green-600 mb-2">5K+</div>
              <div className="text-gray-600">Grocery Orders</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-purple-600 mb-2">2K+</div>
              <div className="text-gray-600">Jobs Posted</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-orange-600 mb-2">50+</div>
              <div className="text-gray-600">Online Tools</div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="md:col-span-1">
              <Link href="/okayy" className="text-2xl font-bold mb-4 block">
                Okayy<span className="text-blue-400">.in</span>
              </Link>
              <p className="text-gray-400 mb-4">
                Your one-stop destination for real estate, groceries, jobs, and online tools.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="text-gray-400 hover:text-white">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-white">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                  </svg>
                </a>
              </div>
            </div>

            {/* Services */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Services</h3>
              <ul className="space-y-2">
                <li><Link href="/" className="text-gray-400 hover:text-white">Real Estate</Link></li>
                <li><Link href="/groceries" className="text-gray-400 hover:text-white">Groceries</Link></li>
                <li><Link href="/jobs" className="text-gray-400 hover:text-white">Jobs</Link></li>
                <li><Link href="/tools" className="text-gray-400 hover:text-white">Online Tools</Link></li>
              </ul>
            </div>

            {/* Company */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Company</h3>
              <ul className="space-y-2">
                <li><Link href="/about" className="text-gray-400 hover:text-white">About Us</Link></li>
                <li><Link href="/contact" className="text-gray-400 hover:text-white">Contact</Link></li>
                <li><Link href="/careers" className="text-gray-400 hover:text-white">Careers</Link></li>
                <li><Link href="/privacy" className="text-gray-400 hover:text-white">Privacy Policy</Link></li>
              </ul>
            </div>

            {/* Contact */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Contact</h3>
              <ul className="space-y-2 text-gray-400">
                <li>📧 <EMAIL></li>
                <li>📞 +91 9876543210</li>
                <li>📍 Hyderabad, India</li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Okayy.in. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </main>
  )
}
