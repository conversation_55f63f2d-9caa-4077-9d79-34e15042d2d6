'use client'

import { useState, useEffect } from 'react'

interface PropertyAnalytics {
  propertyId: string
  title: string
  views: number
  inquiries: number
  saves: number
  viewsThisWeek: number
  inquiriesThisWeek: number
  savesThisWeek: number
  createdAt: string
  lastViewed?: string
}

interface AnalyticsProps {
  properties: any[]
}

export function PropertyAnalytics({ properties }: AnalyticsProps) {
  const [analytics, setAnalytics] = useState<PropertyAnalytics[]>([])
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('7d') // 7d, 30d, 90d

  useEffect(() => {
    fetchAnalytics()
  }, [properties, timeRange])

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      // For now, we'll use the data we already have from properties
      // In a real app, you'd fetch detailed analytics from an API
      const analyticsData = properties.map(property => ({
        propertyId: property.id,
        title: property.title,
        views: property.viewCount || 0,
        inquiries: property._count?.inquiries || 0,
        saves: property._count?.savedBy || 0,
        viewsThisWeek: Math.floor((property.viewCount || 0) * 0.3), // Mock data
        inquiriesThisWeek: Math.floor((property._count?.inquiries || 0) * 0.4), // Mock data
        savesThisWeek: Math.floor((property._count?.savedBy || 0) * 0.2), // Mock data
        createdAt: property.createdAt,
        lastViewed: property.updatedAt
      }))
      setAnalytics(analyticsData)
    } catch (error) {
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const getTotalMetrics = () => {
    return analytics.reduce(
      (totals, property) => ({
        totalViews: totals.totalViews + property.views,
        totalInquiries: totals.totalInquiries + property.inquiries,
        totalSaves: totals.totalSaves + property.saves,
        weeklyViews: totals.weeklyViews + property.viewsThisWeek,
        weeklyInquiries: totals.weeklyInquiries + property.inquiriesThisWeek,
        weeklySaves: totals.weeklySaves + property.savesThisWeek
      }),
      {
        totalViews: 0,
        totalInquiries: 0,
        totalSaves: 0,
        weeklyViews: 0,
        weeklyInquiries: 0,
        weeklySaves: 0
      }
    )
  }

  const getTopPerformer = () => {
    if (analytics.length === 0) return null
    return analytics.reduce((top, current) => 
      (current.views + current.inquiries * 2 + current.saves) > 
      (top.views + top.inquiries * 2 + top.saves) ? current : top
    )
  }

  const metrics = getTotalMetrics()
  const topPerformer = getTopPerformer()

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Overview Metrics */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Property Analytics</h2>
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="border border-gray-300 rounded px-3 py-1 text-sm"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-600 font-medium">Total Views</p>
                <p className="text-2xl font-bold text-blue-900">{metrics.totalViews}</p>
                <p className="text-xs text-blue-600">+{metrics.weeklyViews} this week</p>
              </div>
              <div className="text-blue-500">
                <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                  <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </div>

          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-600 font-medium">Total Inquiries</p>
                <p className="text-2xl font-bold text-green-900">{metrics.totalInquiries}</p>
                <p className="text-xs text-green-600">+{metrics.weeklyInquiries} this week</p>
              </div>
              <div className="text-green-500">
                <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </div>

          <div className="bg-purple-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-purple-600 font-medium">Total Saves</p>
                <p className="text-2xl font-bold text-purple-900">{metrics.totalSaves}</p>
                <p className="text-xs text-purple-600">+{metrics.weeklySaves} this week</p>
              </div>
              <div className="text-purple-500">
                <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Top Performer */}
        {topPerformer && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-yellow-800 mb-2">🏆 Top Performing Property</h3>
            <p className="text-lg font-semibold text-yellow-900">{topPerformer.title}</p>
            <div className="flex items-center space-x-4 mt-2 text-sm text-yellow-700">
              <span>{topPerformer.views} views</span>
              <span>{topPerformer.inquiries} inquiries</span>
              <span>{topPerformer.saves} saves</span>
            </div>
          </div>
        )}
      </div>

      {/* Individual Property Performance */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4">Property Performance</h3>
        {analytics.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No properties to analyze yet</p>
          </div>
        ) : (
          <div className="space-y-4">
            {analytics.map((property) => (
              <div key={property.propertyId} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 mb-2">{property.title}</h4>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Views:</span>
                        <span className="ml-2 font-medium">{property.views}</span>
                        <span className="ml-1 text-blue-600 text-xs">
                          (+{property.viewsThisWeek} this week)
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-500">Inquiries:</span>
                        <span className="ml-2 font-medium">{property.inquiries}</span>
                        <span className="ml-1 text-green-600 text-xs">
                          (+{property.inquiriesThisWeek} this week)
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-500">Saves:</span>
                        <span className="ml-2 font-medium">{property.saves}</span>
                        <span className="ml-1 text-purple-600 text-xs">
                          (+{property.savesThisWeek} this week)
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right text-sm text-gray-500">
                    <p>Listed: {new Date(property.createdAt).toLocaleDateString()}</p>
                    {property.lastViewed && (
                      <p>Last activity: {new Date(property.lastViewed).toLocaleDateString()}</p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
