import Image from 'next/image';
import Link from 'next/link';
import { Navbar } from '@/components/Navbar';
import { Footer } from '@/components/Footer';
import { notFound } from 'next/navigation';

// Define a simplified type for Property
interface PropertyWithDetails {
  id: string;
  title: string;
  description: string;
  price: number;
  currency: string;
  type: string;
  listing_type: string;
  bedrooms: number | null;
  bathrooms: number | null;
  area: number | null;
  address: string;
  city: string;
  state: string;
  pincode: string;
  images: string; // JSON string
  amenities: string; // JSON string
  is_featured: boolean;
  is_approved: boolean;
  approval_status: string;
  view_count: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  approvedAt: Date | null;
  approvedBy: string | null;
  ownerId: string;
  owner_name: string;
  owner_email: string;
  created_at: string;
}

// Generate static params for build
export async function generateStaticParams() {
  return [
    { id: 'sample-pg-1' },
    { id: 'sample-pg-2' },
    { id: 'sample-pg-3' },
  ];
}

export default async function PGDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params;
  const propertyId = resolvedParams.id;

  // Fetch property data server-side
  let property: PropertyWithDetails | null = null;
  let error: string | null = null;

  try {
    const response = await fetch(`https://housing.okayy.in/php-backend/api/properties/get.php?id=${propertyId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store',
    });

    if (response.ok) {
      const data = await response.json();

      if (data.success && data.property && data.property.type === 'PG') {
        // Only show approved properties
        if (data.property.approval_status === 'APPROVED') {
          property = data.property;
        } else {
          error = 'PG accommodation not found or not approved';
        }
      } else {
        error = 'PG accommodation not found';
      }
    } else {
      error = 'Failed to load PG details';
    }
  } catch (err) {
    console.error('Error fetching PG property:', err);
    error = 'Failed to load PG details';
  }

  if (error || !property) {
    notFound();
  }

  // Ensure images and amenities are parsed only if they are strings
  const images = typeof property.images === 'string' ? JSON.parse(property.images) : property.images;
  const amenities = typeof property.amenities === 'string' ? JSON.parse(property.amenities) : property.amenities;
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <main className="min-h-screen bg-gray-50">
      <Navbar />
      
      {/* Property Images */}
      <section className="relative">
        <div className="container-custom py-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div className="relative h-96 lg:h-[500px] rounded-2xl overflow-hidden">
              <Image
                src={images[0] || '/placeholder-property.jpg'}
                alt={property.title}
                fill
                className="object-cover"
                priority
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              {images.slice(1, 5).map((image: string, index: number) => (
                <div key={index} className="relative h-44 lg:h-60 rounded-xl overflow-hidden">
                  <Image
                    src={image}
                    alt={`${property.title} - Image ${index + 2}`}
                    fill
                    className="object-cover"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Property Details */}
      <section className="py-12">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-2xl shadow-large p-8">
                <div className="mb-6">
                  <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    {property.title}
                  </h1>
                  <div className="flex items-center text-gray-600 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span>{property.address}, {property.city}, {property.state}</span>
                  </div>
                  <div className="text-4xl font-bold text-primary mb-6">
                    {formatCurrency(property.price)}/month
                  </div>
                </div>

                {/* PG Specific Features */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
                  <div className="text-center p-4 bg-gray-50 rounded-xl">
                    <div className="text-2xl font-bold text-primary">{property.view_count}</div>
                    <div className="text-gray-600">Views</div>
                  </div>
                  {property.area && (
                    <div className="text-center p-4 bg-gray-50 rounded-xl">
                      <div className="text-2xl font-bold text-primary">{property.area}</div>
                      <div className="text-gray-600">Sq Ft</div>
                    </div>
                  )}
                  <div className="text-center p-4 bg-gray-50 rounded-xl">
                    <div className="text-2xl font-bold text-primary">{property.type}</div>
                    <div className="text-gray-600">Type</div>
                  </div>
                </div>

                {/* Description */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">Description</h2>
                  <p className="text-gray-600 leading-relaxed">
                    {property.description}
                  </p>
                </div>

                {/* Amenities */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">Amenities</h2>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {amenities.map((amenity: string, index: number) => (
                      <div key={index} className="flex items-center p-3 bg-gray-50 rounded-xl">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="text-gray-700">{amenity}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-2xl shadow-large p-6 sticky top-8">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Contact Agent</h3>
                <div className="mb-6">
                  <div className="flex items-center mb-3">
                    <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-3">
                      <span className="text-white font-semibold">
                        {property.owner_name?.charAt(0) || 'A'}
                      </span>
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{property.owner_name || 'N/A'}</div>
                      <div className="text-gray-600 text-sm">Property Agent</div>
                    </div>
                  </div>
                </div>

                <form className="space-y-4">
                  <div>
                    <input
                      type="text"
                      placeholder="Your Name"
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                  <div>
                    <input
                      type="email"
                      placeholder="Your Email"
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                  <div>
                    <input
                      type="tel"
                      placeholder="Your Phone"
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                  <div>
                    <textarea
                      rows={4}
                      placeholder="I'm interested in this property. Please contact me with more information."
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                    />
                  </div>
                  <button
                    type="submit"
                    className="w-full btn-primary"
                  >
                    Send Message
                  </button>
                </form>

                <div className="mt-6 pt-6 border-t border-gray-200">
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>Listed on:</span>
                    <span>{new Date(property.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Similar Properties */}
      <section className="py-12 bg-gray-100">
        <div className="container-custom">
          <h2 className="text-2xl md:text-3xl font-bold mb-8">Similar PG Accommodations</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="col-span-full text-center py-8">
              <p className="text-gray-500">Similar PG accommodations will be displayed here</p>
              <Link href="/pg/" className="text-primary hover:underline mt-2 inline-block">
                Browse All PG Accommodations
              </Link>
            </div>
          </div>
        </div>
      </section>
      
      <Footer />
    </main>
  );
}
