'use client'

import { Navbar } from '@/components/Navbar'
import { Footer } from '@/components/Footer'
import { PropertyListingForm } from '@/components/PropertyListingForm'
import { authAPI } from '@/config/api'

import { useRouter } from 'next/navigation'
import { useState, useEffect } from 'react'

export default function CreatePropertyPage() {
  const [session, setSession] = useState<{ user: any } | null>(null);
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await authAPI.checkSession()
        setSession(response)
      } catch (err) {
        console.error('Session fetch error:', err);
        setSession({ user: null });
      }
    }

    checkAuth()
  }, []);
  const router = useRouter()

  useEffect(() => {
    if (session && !session.user) {
      router.push('/login?error=Please%20login%20to%20list%20a%20property');
    }
  }, [session, router]);

  if (session === null) {
    return (
      <main className="min-h-screen">
        <Navbar />
        <div className="container-custom py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-gray-600">Checking session...</p>
          </div>
        </div>
        <Footer />
      </main>
    );
  }

  if (!session.user) {
    return (
      <main className="min-h-screen">
        <Navbar />
        <div className="container-custom py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading...</p>
          </div>
        </div>
        <Footer />
      </main>
    )
  }

  

  return (
    <main className="min-h-screen bg-gray-50">
      <Navbar />
      
      {/* Page Header */}
      <section className="bg-white py-12 border-b">
        <div className="container-custom">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              List Your Property for FREE
            </h1>
            <p className="text-lg text-gray-600 mb-6">
              Reach thousands of potential buyers and renters. Create your property listing in just a few minutes.
            </p>
            <div className="flex items-center justify-center space-x-6 text-sm text-gray-500">
              <div className="flex items-center">
                <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Free to list
              </div>
              <div className="flex items-center">
                <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Wide reach
              </div>
              <div className="flex items-center">
                <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Easy process
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Form Section */}
      <section className="py-12">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-md p-8">
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Property Details</h2>
                <p className="text-gray-600">
                  Please provide accurate information about your property. All listings are subject to admin approval.
                </p>
              </div>
              
              <PropertyListingForm />
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
