(()=>{var e={};e.id=182,e.ids=[182],e.modules={708:(e,t,r)=>{Promise.resolve().then(r.bind(r,8072))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1388:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3262:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Music\\\\Real Estate\\\\Real Estate\\\\src\\\\app\\\\admin\\\\properties\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\properties\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>n});var s=r(7413),a=r(5091),i=r.n(a);r(1135);let n={title:{default:"Real Estate India - Buy, Sell, and Rent Properties",template:"%s | Real Estate India"},description:"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities, or list your property with us. Expert guidance for all your property needs.",keywords:["real estate India","property for sale","property for rent","buy property","sell property","apartments","houses","villas","commercial property"],authors:[{name:"Real Estate India"}],creator:"Real Estate India",publisher:"Real Estate India",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("https://realestate-india.com"),alternates:{canonical:"/"},openGraph:{type:"website",locale:"en_IN",url:"https://realestate-india.com",title:"Real Estate India - Buy, Sell, and Rent Properties",description:"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities.",siteName:"Real Estate India"},twitter:{card:"summary_large_image",title:"Real Estate India - Buy, Sell, and Rent Properties",description:"Find your dream home in India with our comprehensive real estate platform.",creator:"@realestateindia"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function o({children:e}){return(0,s.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,s.jsx)("body",{className:`${i().variable} font-sans bg-background text-text-primary antialiased`,children:e})})}},4940:()=>{},5171:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},6189:(e,t,r)=>{"use strict";var s=r(5773);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},6411:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>m,tree:()=>d});var s=r(5239),a=r(8088),i=r(8170),n=r.n(i),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["admin",{children:["properties",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3262)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\properties\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=["C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\properties\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/properties/page",pathname:"/admin/properties",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6740:(e,t,r)=>{Promise.resolve().then(r.bind(r,3262))},8072:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(687),a=r(3210),i=r(6189);function n(){let[e,t]=(0,a.useState)(!0),[r,n]=(0,a.useState)([]),[o,l]=(0,a.useState)(""),[d,p]=(0,a.useState)("all"),c=(0,i.useRouter)(),m=async()=>{try{let e=await fetch("/php-backend/api/admin/properties.php",{credentials:"include"}),t=await e.json();t.success&&n(t.properties||[])}catch(e){console.error("Failed to load properties:",e)}finally{t(!1)}},x=async e=>{try{let t=await fetch("/php-backend/api/admin/approve-property.php",{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({property_id:e})}),r=await t.json();r.success?(alert("Property approved successfully!"),m()):alert("Failed to approve property: "+r.message)}catch(e){alert("Error approving property")}},u=async e=>{let t=prompt("Enter rejection reason:");if(t)try{let r=await fetch("/php-backend/api/admin/reject-property.php",{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({property_id:e,reason:t})}),s=await r.json();s.success?(alert("Property rejected successfully!"),m()):alert("Failed to reject property: "+s.message)}catch(e){alert("Error rejecting property")}},h=r.filter(e=>{let t=e.title?.toLowerCase().includes(o.toLowerCase())||e.owner_name?.toLowerCase().includes(o.toLowerCase())||e.city?.toLowerCase().includes(o.toLowerCase()),r="all"===d||e.approval_status===d.toUpperCase();return t&&r});return e?(0,s.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading properties..."})]})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,s.jsx)("header",{className:"bg-white shadow",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Manage Properties"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Review and manage all property listings"})]}),(0,s.jsx)("div",{className:"flex space-x-4",children:(0,s.jsx)("button",{onClick:()=>c.push("/admin/dashboard"),className:"bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700",children:"Back to Dashboard"})})]})})}),(0,s.jsx)("main",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Properties"}),(0,s.jsx)("input",{type:"text",value:o,onChange:e=>l(e.target.value),placeholder:"Search by title, owner, or city...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Filter by Status"}),(0,s.jsxs)("select",{value:d,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"all",children:"All Properties"}),(0,s.jsx)("option",{value:"pending",children:"Pending"}),(0,s.jsx)("option",{value:"approved",children:"Approved"}),(0,s.jsx)("option",{value:"rejected",children:"Rejected"})]})]})]}),(0,s.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:["Showing ",h.length," of ",r.length," properties"]})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Property"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Owner"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:h.map(e=>(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:[e.type," in ",e.city,", ",e.state]})]})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("div",{className:"text-sm text-gray-900",children:e.owner_name}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.owner_email})]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["₹",e.price?.toLocaleString()]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${"APPROVED"===e.approval_status?"bg-green-100 text-green-800":"PENDING"===e.approval_status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:e.approval_status})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsxs)("div",{className:"flex space-x-2",children:["PENDING"===e.approval_status&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{onClick:()=>x(e.id),className:"bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700",children:"Approve"}),(0,s.jsx)("button",{onClick:()=>u(e.id),className:"bg-red-600 text-white px-3 py-1 rounded text-xs hover:bg-red-700",children:"Reject"})]}),(0,s.jsx)("a",{href:`/properties/${e.id}`,target:"_blank",className:"bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700",children:"View"})]})})]},e.id))})]})})}),0===h.length&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-gray-500",children:"No properties found matching your criteria."})})]})})]})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9243:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[771],()=>r(6411));module.exports=s})();