<?php
/**
 * Comprehensive Test Script for housing.okayy.in
 * Tests all the fixes: images, property details, client-side exceptions
 */
?>
<!DOCTYPE html>
<html>
<head>
    <title>🧪 Complete Fix Test - housing.okayy.in</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 20px auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .test-item { padding: 10px; margin: 5px 0; background: #f8f9fa; border-radius: 5px; }
        .btn { background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Complete Fix Test for housing.okayy.in</h1>
        <p><strong>Testing:</strong> Image previews, Property details, Client-side exceptions</p>
        
        <?php
        // Test 1: Database and API Connectivity
        echo "<div class='test-section'>";
        echo "<h2>1. 🔗 Database & API Connectivity</h2>";
        
        try {
            require_once 'php-backend/config/database.php';
            $database = new Database();
            $db = $database->getConnection();
            echo "<div class='test-item'><span class='success'>✅ Database connection: Working</span></div>";
            
            // Test properties API
            $stmt = $db->query("SELECT COUNT(*) as count FROM properties WHERE approval_status = 'APPROVED'");
            $result = $stmt->fetch();
            $approvedCount = $result['count'];
            echo "<div class='test-item'><span class='info'>📊 Approved properties: $approvedCount</span></div>";
            
        } catch (Exception $e) {
            echo "<div class='test-item'><span class='error'>❌ Database error: " . $e->getMessage() . "</span></div>";
        }
        echo "</div>";
        
        // Test 2: API Endpoints
        echo "<div class='test-section'>";
        echo "<h2>2. 🔌 API Endpoints Test</h2>";
        
        $endpoints = [
            'Properties List' => '/php-backend/api/properties/index.php',
            'Property Details' => '/php-backend/api/properties/get.php?id=test',
            'Image Upload' => '/php-backend/api/upload/index.php',
            'Admin Properties' => '/php-backend/api/admin/properties.php'
        ];
        
        foreach ($endpoints as $name => $endpoint) {
            $url = 'https://' . $_SERVER['HTTP_HOST'] . $endpoint;
            echo "<div class='test-item'>";
            echo "<strong>$name:</strong> ";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode == 200) {
                $json = json_decode($response, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    echo "<span class='success'>✅ Working (JSON)</span>";
                } else {
                    echo "<span class='warning'>⚠️ Working (Non-JSON)</span>";
                }
            } else {
                echo "<span class='error'>❌ HTTP $httpCode</span>";
            }
            
            echo " - <a href='$url' target='_blank' class='btn'>Test</a>";
            echo "</div>";
        }
        echo "</div>";
        
        // Test 3: Image Upload Directory
        echo "<div class='test-section'>";
        echo "<h2>3. 📸 Image Upload System</h2>";
        
        $uploadDir = 'php-backend/uploads/';
        echo "<div class='test-item'>";
        echo "<strong>Upload Directory:</strong> ";
        
        if (is_dir($uploadDir)) {
            if (is_writable($uploadDir)) {
                echo "<span class='success'>✅ Exists and writable</span>";
                
                // Count existing images
                $imageFiles = glob($uploadDir . '*.{jpg,jpeg,png,gif,webp}', GLOB_BRACE);
                $imageCount = count($imageFiles);
                echo "<div class='test-item'><span class='info'>📁 Existing images: $imageCount</span></div>";
                
                // Show sample images
                if ($imageCount > 0) {
                    echo "<div class='test-item'><strong>Sample images:</strong><br>";
                    foreach (array_slice($imageFiles, 0, 3) as $image) {
                        $imageName = basename($image);
                        $imageUrl = "https://" . $_SERVER['HTTP_HOST'] . "/php-backend/uploads/$imageName";
                        echo "<img src='$imageUrl' style='width:100px;height:80px;object-fit:cover;margin:5px;border-radius:5px;' onerror='this.style.display=\"none\"'>";
                    }
                    echo "</div>";
                }
            } else {
                echo "<span class='error'>❌ Not writable</span>";
            }
        } else {
            echo "<span class='error'>❌ Directory missing</span>";
        }
        echo "</div>";
        echo "</div>";
        
        // Test 4: Frontend Pages
        echo "<div class='test-section'>";
        echo "<h2>4. 🌐 Frontend Pages Test</h2>";
        
        $pages = [
            'Homepage' => '/',
            'Properties List' => '/properties',
            'Admin Login' => '/admin/login',
            'User Login' => '/login',
            'Property Create' => '/properties/create'
        ];
        
        foreach ($pages as $name => $path) {
            echo "<div class='test-item'>";
            echo "<strong>$name:</strong> ";
            echo "<a href='$path' target='_blank' class='btn'>Test Page</a>";
            echo "<span class='info'> - Check browser console for errors</span>";
            echo "</div>";
        }
        echo "</div>";
        
        // Test 5: Sample Property Details
        echo "<div class='test-section'>";
        echo "<h2>5. 🏠 Property Details Test</h2>";
        
        try {
            $stmt = $db->query("SELECT id, title FROM properties WHERE approval_status = 'APPROVED' LIMIT 3");
            $properties = $stmt->fetchAll();
            
            if (count($properties) > 0) {
                echo "<div class='test-item'><span class='success'>✅ Found " . count($properties) . " approved properties</span></div>";
                
                foreach ($properties as $property) {
                    $detailsUrl = "/properties/" . $property['id'];
                    echo "<div class='test-item'>";
                    echo "<strong>" . htmlspecialchars($property['title']) . ":</strong> ";
                    echo "<a href='$detailsUrl' target='_blank' class='btn'>View Details</a>";
                    echo "</div>";
                }
            } else {
                echo "<div class='test-item'><span class='warning'>⚠️ No approved properties found</span></div>";
            }
        } catch (Exception $e) {
            echo "<div class='test-item'><span class='error'>❌ Error: " . $e->getMessage() . "</span></div>";
        }
        echo "</div>";
        
        // Test 6: JavaScript Error Detection
        echo "<div class='test-section'>";
        echo "<h2>6. 🐛 JavaScript Error Detection</h2>";
        echo "<div id='js-errors'></div>";
        echo "<div class='test-item'><span class='info'>ℹ️ JavaScript errors will appear above (if any)</span></div>";
        echo "</div>";
        ?>
        
        <!-- JavaScript Error Catcher -->
        <script>
            let errorCount = 0;
            const errorContainer = document.getElementById('js-errors');
            
            // Catch JavaScript errors
            window.addEventListener('error', function(e) {
                errorCount++;
                const errorDiv = document.createElement('div');
                errorDiv.className = 'test-item';
                errorDiv.innerHTML = `<span class="error">❌ JS Error ${errorCount}: ${e.message} at ${e.filename}:${e.lineno}</span>`;
                errorContainer.appendChild(errorDiv);
            });
            
            // Catch unhandled promise rejections
            window.addEventListener('unhandledrejection', function(e) {
                errorCount++;
                const errorDiv = document.createElement('div');
                errorDiv.className = 'test-item';
                errorDiv.innerHTML = `<span class="error">❌ Promise Rejection ${errorCount}: ${e.reason}</span>`;
                errorContainer.appendChild(errorDiv);
            });
            
            // Test API calls
            async function testAPICalls() {
                const apiTests = [
                    { name: 'Properties API', url: '/php-backend/api/properties/index.php' },
                    { name: 'Check Session', url: '/php-backend/api/auth/check-session.php' }
                ];
                
                for (const test of apiTests) {
                    try {
                        const response = await fetch(test.url);
                        const data = await response.text();
                        console.log(`✅ ${test.name}: OK`);
                    } catch (error) {
                        console.error(`❌ ${test.name}: ${error.message}`);
                        errorCount++;
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'test-item';
                        errorDiv.innerHTML = `<span class="error">❌ API Error: ${test.name} - ${error.message}</span>`;
                        errorContainer.appendChild(errorDiv);
                    }
                }
                
                // Show success message if no errors
                if (errorCount === 0) {
                    setTimeout(() => {
                        if (errorCount === 0) {
                            const successDiv = document.createElement('div');
                            successDiv.className = 'test-item';
                            successDiv.innerHTML = `<span class="success">✅ No JavaScript errors detected!</span>`;
                            errorContainer.appendChild(successDiv);
                        }
                    }, 2000);
                }
            }
            
            // Run tests when page loads
            document.addEventListener('DOMContentLoaded', testAPICalls);
        </script>
        
        <div class="test-section">
            <h2>7. 📋 Manual Testing Checklist</h2>
            <div class="test-item">
                <strong>Test these manually:</strong>
                <ul>
                    <li>✅ Property images load correctly</li>
                    <li>✅ Property details pages open without errors</li>
                    <li>✅ User registration/login works</li>
                    <li>✅ Property posting works</li>
                    <li>✅ Admin dashboard functions properly</li>
                    <li>✅ No "Application error" messages appear</li>
                </ul>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="/" class="btn">🏠 Go to Homepage</a>
            <a href="/properties" class="btn">🏠 View Properties</a>
            <a href="/admin/login" class="btn">👑 Admin Login</a>
        </div>
        
        <div class="test-section">
            <h2>🗑️ Cleanup</h2>
            <p><strong>After testing, delete this file:</strong> <code>test-all-fixes.php</code></p>
        </div>
    </div>
</body>
</html>
