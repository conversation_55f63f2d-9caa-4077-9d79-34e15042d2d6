<?php
require_once '../../config/database.php';

setCorsHeaders();

$method = $_SERVER['REQUEST_METHOD'];

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if user is admin
    $user = getCurrentUser($db);
    if (!$user || $user['role'] !== 'ADMIN') {
        sendError('Admin access required', 403);
    }
    
    switch ($method) {
        case 'GET':
            handleGetAdminProperties($db);
            break;
        default:
            sendError('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Admin Properties API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

function handleGetAdminProperties($db) {
    $status = $_GET['status'] ?? 'all';
    
    $where_conditions = [];
    $params = [];
    
    if ($status !== 'all') {
        $where_conditions[] = "p.approval_status = :status";
        $params[':status'] = strtoupper($status);
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Get properties with owner information (handle missing tables gracefully)
    $query = "SELECT p.*, u.name as owner_name, u.email as owner_email,
              COALESCE((SELECT COUNT(*) FROM inquiries WHERE property_id = p.id), 0) as inquiries_count,
              0 as saved_count
              FROM properties p
              JOIN users u ON p.owner_id = u.id
              $where_clause
              ORDER BY p.created_at DESC";
    
    $stmt = $db->prepare($query);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    
    $properties = $stmt->fetchAll();
    
    // Process properties data
    foreach ($properties as &$property) {
        $property['images'] = json_decode($property['images'], true) ?: [];
        $property['amenities'] = json_decode($property['amenities'], true) ?: [];
        $property['owner'] = [
            'name' => $property['owner_name'],
            'email' => $property['owner_email']
        ];
        unset($property['owner_name'], $property['owner_email']);
    }
    
    sendResponse([
        'success' => true,
        'properties' => $properties,
        'total' => count($properties)
    ]);
}

function getCurrentUser($db) {
    // Get session token
    $session_token = null;
    
    if (isset($_COOKIE['session_token'])) {
        $session_token = $_COOKIE['session_token'];
    } elseif (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $auth_header = $_SERVER['HTTP_AUTHORIZATION'];
        if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
            $session_token = $matches[1];
        }
    }
    
    if (!$session_token) {
        return null;
    }
    
    // Check session
    $query = "SELECT u.* FROM users u 
              JOIN user_sessions s ON u.id = s.user_id 
              WHERE s.session_token = :token AND s.expires_at > NOW()";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':token', $session_token);
    $stmt->execute();
    
    return $stmt->fetch();
}
?>
