(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[957],{1008:(e,t,s)=>{"use strict";s.d(t,{Eo:()=>o,Er:()=>c,M5:()=>i,R2:()=>n,hh:()=>d});let a="https://housing.okayy.in/php-backend/api",r={LOGIN:"".concat(a,"/auth/login.php"),SIGNUP:"".concat(a,"/auth/signup.php"),LOGOUT:"".concat(a,"/auth/logout.php"),CHECK_SESSION:"".concat(a,"/auth/check-session.php"),PROPERTIES:"".concat(a,"/properties/index.php"),PROPERTY_BY_ID:e=>"".concat(a,"/properties/get.php?id=").concat(e),BLOG_POSTS:"".concat(a,"/blog/index.php"),BLOG_POST_BY_SLUG:e=>"".concat(a,"/blog/get.php?slug=").concat(e),USER_PROPERTIES:"".concat(a,"/user/properties.php"),USER_INQUIRIES:"".concat(a,"/user/inquiries.php"),ADMIN_PROPERTIES:"".concat(a,"/admin/properties.php"),APPROVE_PROPERTY:e=>"".concat(a,"/admin/approve.php?id=").concat(e),REJECT_PROPERTY:e=>"".concat(a,"/admin/reject.php?id=").concat(e)},l=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s={headers:{"Content-Type":"application/json"},credentials:"include"},a={...s,...t,headers:{...s.headers,...t.headers}};try{let t=await fetch(e,a),s=await t.json();if(!t.ok)throw Error(s.error||"HTTP error! status: ".concat(t.status));return s}catch(e){throw console.error("API request failed:",e),e}},n={login:async(e,t)=>l(r.LOGIN,{method:"POST",body:JSON.stringify({email:e,password:t})}),signup:async(e,t,s,a)=>l(r.SIGNUP,{method:"POST",body:JSON.stringify({name:e,email:t,password:s,phone:a})}),logout:async()=>l(r.LOGOUT,{method:"POST"}),checkSession:async()=>l(r.CHECK_SESSION)},i={getProperties:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[s,a]=e;null!=a&&""!==a&&t.append(s,a.toString())}),l("".concat(r.PROPERTIES,"?").concat(t.toString()))},createProperty:async e=>l(r.PROPERTIES,{method:"POST",body:JSON.stringify(e)}),getPropertyById:async e=>l(r.PROPERTY_BY_ID(e))},c={getProperties:async e=>l(e?"".concat(r.ADMIN_PROPERTIES,"?status=").concat(e):r.ADMIN_PROPERTIES),approveProperty:async e=>l(r.APPROVE_PROPERTY(e),{method:"PUT"}),rejectProperty:async(e,t)=>l(r.REJECT_PROPERTY(e),{method:"PUT",body:JSON.stringify({reason:t})})},d={getPosts:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[s,a]=e;null!=a&&""!==a&&t.append(s,a.toString())}),l("".concat(r.BLOG_POSTS,"?").concat(t.toString()))},getPostBySlug:async e=>l(r.BLOG_POST_BY_SLUG(e))},o={getProperties:async()=>l(r.USER_PROPERTIES),getInquiries:async()=>l(r.USER_INQUIRIES)}},4163:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(5155),r=s(2115),l=s(5695),n=s(1008);function i(){let[e,t]=(0,r.useState)(null),[s,i]=(0,r.useState)([]),[c,d]=(0,r.useState)({total:0,pending:0,approved:0,rejected:0}),[o,x]=(0,r.useState)(!0),[h,p]=(0,r.useState)("overview"),m=(0,l.useRouter)();(0,r.useEffect)(()=>{u()},[]);let u=async()=>{try{let e=await n.R2.checkSession();if(!e.user||"ADMIN"!==e.user.role)return void m.push("/login");t(e.user),await g()}catch(e){console.error("Auth check failed:",e),m.push("/login")}},g=async()=>{try{var e,t,s,a;let r=await n.Er.getProperties("all");i(r.properties||[]);let l=(null==(e=r.properties)?void 0:e.length)||0,c=(null==(t=r.properties)?void 0:t.filter(e=>"PENDING"===e.approval_status).length)||0,o=(null==(s=r.properties)?void 0:s.filter(e=>"APPROVED"===e.approval_status).length)||0,x=(null==(a=r.properties)?void 0:a.filter(e=>"REJECTED"===e.approval_status).length)||0;d({total:l,pending:c,approved:o,rejected:x})}catch(e){console.error("Failed to load dashboard data:",e)}finally{x(!1)}},y=async e=>{try{await n.Er.approveProperty(e),await g(),alert("Property approved successfully!")}catch(e){alert(e.message||"Failed to approve property")}},j=async e=>{let t=prompt("Enter rejection reason:");if(t)try{await n.Er.rejectProperty(e,t),await g(),alert("Property rejected successfully!")}catch(e){alert(e.message||"Failed to reject property")}},N=async()=>{try{await n.R2.logout(),m.push("/login")}catch(e){console.error("Logout failed:",e)}};if(o)return(0,a.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading admin dashboard..."})]})});let v=s.filter(e=>"PENDING"===e.approval_status);return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,a.jsx)("header",{className:"bg-white shadow",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Admin Dashboard"}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Welcome back, ",null==e?void 0:e.name]})]}),(0,a.jsx)("button",{onClick:N,className:"bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700",children:"Logout"})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsx)("div",{className:"border-b border-gray-200 mb-8",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8",children:[{id:"overview",name:"Overview"},{id:"pending",name:"Pending Approvals"},{id:"all",name:"All Properties"}].map(e=>(0,a.jsx)("button",{onClick:()=>p(e.id),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(h===e.id?"border-red-500 text-red-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:e.name},e.id))})}),"overview"===h&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"T"})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Properties"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:c.total})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"P"})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Pending"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:c.pending})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"A"})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Approved"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:c.approved})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"R"})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Rejected"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:c.rejected})]})})]})})})]}),"pending"===h&&(0,a.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:[(0,a.jsx)("div",{className:"px-4 py-5 sm:px-6",children:(0,a.jsxs)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:["Pending Property Approvals (",v.length,")"]})}),(0,a.jsxs)("ul",{className:"divide-y divide-gray-200",children:[v.map(e=>(0,a.jsx)("li",{className:"px-4 py-4 sm:px-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["₹",e.price.toLocaleString()," • ",e.type," • Owner: ",e.owner_name]}),(0,a.jsxs)("p",{className:"text-xs text-gray-400",children:["Submitted: ",new Date(e.created_at).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>y(e.id),className:"bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700",children:"Approve"}),(0,a.jsx)("button",{onClick:()=>j(e.id),className:"bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700",children:"Reject"})]})]})},e.id)),0===v.length&&(0,a.jsx)("li",{className:"px-4 py-8 text-center text-gray-500",children:"No pending properties to review"})]})]}),"all"===h&&(0,a.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:[(0,a.jsx)("div",{className:"px-4 py-5 sm:px-6",children:(0,a.jsxs)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:["All Properties (",s.length,")"]})}),(0,a.jsx)("ul",{className:"divide-y divide-gray-200",children:s.map(e=>(0,a.jsx)("li",{className:"px-4 py-4 sm:px-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["₹",e.price.toLocaleString()," • ",e.type," • Owner: ",e.owner_name]}),(0,a.jsxs)("p",{className:"text-xs text-gray-400",children:["Created: ",new Date(e.created_at).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("APPROVED"===e.approval_status?"bg-green-100 text-green-800":"PENDING"===e.approval_status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:e.approval_status}),"PENDING"===e.approval_status&&(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("button",{onClick:()=>y(e.id),className:"bg-green-600 text-white px-2 py-1 rounded text-xs hover:bg-green-700",children:"Approve"}),(0,a.jsx)("button",{onClick:()=>j(e.id),className:"bg-red-600 text-white px-2 py-1 rounded text-xs hover:bg-red-700",children:"Reject"})]})]})]})},e.id))})]})]})]})}},5695:(e,t,s)=>{"use strict";var a=s(8999);s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},8505:(e,t,s)=>{Promise.resolve().then(s.bind(s,4163))}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(8505)),_N_E=e.O()}]);