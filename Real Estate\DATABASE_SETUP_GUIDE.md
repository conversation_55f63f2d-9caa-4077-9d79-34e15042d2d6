# 🗄️ Database Setup Guide for housing.okayy.in

## 📋 Prerequisites

1. **Hosting Control Panel Access** (cPanel, Plesk, etc.)
2. **MySQL Database** support on your hosting
3. **phpMyAdmin** or similar database management tool

## 🔧 Step 1: Update Database Configuration

### 1.1 Edit Database Credentials

Update the file: `php-backend/config/database.php`

```php
class Database {
    private $host = 'localhost';                    // Usually 'localhost' for shared hosting
    private $db_name = 'housing_okayy_db';         // Your actual database name
    private $username = 'housing_okayy_user';      // Your actual database username  
    private $password = 'your_secure_password';    // Your actual database password
    private $conn;
```

### 1.2 Get Your Actual Database Credentials

**From your hosting control panel:**
1. Go to **MySQL Databases** section
2. Note down:
   - Database name (e.g., `username_housing`)
   - Database username (e.g., `username_housing`)
   - Database password (the one you set)

**Common hosting patterns:**
- **cPanel**: `username_dbname` format
- **Plesk**: Usually custom names
- **Shared hosting**: Often prefixed with your account name

## 🗄️ Step 2: Create Database

### 2.1 Through Hosting Control Panel

1. **Login to your hosting control panel**
2. **Find "MySQL Databases" or "Databases"**
3. **Create new database:**
   - Name: `housing_okayy_db` (or similar)
   - Character set: `utf8mb4_unicode_ci`
4. **Create database user:**
   - Username: `housing_okayy_user` (or similar)
   - Password: Strong password
5. **Add user to database** with ALL PRIVILEGES

### 2.2 Through phpMyAdmin

1. **Open phpMyAdmin**
2. **Click "New" to create database**
3. **Enter database name** and select `utf8mb4_unicode_ci`
4. **Click "Create"**

## 📊 Step 3: Import Database Schema

### 3.1 Using phpMyAdmin

1. **Select your database** in phpMyAdmin
2. **Click "Import" tab**
3. **Choose file:** `php-backend/config/database.sql`
4. **Click "Go"**

### 3.2 Using SQL Command

```sql
-- Copy and paste the contents of database.sql
-- Or run this command if you have SSH access:
mysql -u username -p database_name < php-backend/config/database.sql
```

## 👤 Step 4: Setup Admin User

### 4.1 Using SQL File

1. **In phpMyAdmin, go to SQL tab**
2. **Copy contents of:** `php-backend/config/update-admin-credentials.sql`
3. **Paste and execute**

### 4.2 Using PHP Script

1. **Upload** `reset-admin-production.php` to your server
2. **Run:** `https://yourdomain.com/reset-admin-production.php`
3. **Note the credentials** shown
4. **Delete the file** after use

## ✅ Step 5: Verify Setup

### 5.1 Test Database Connection

1. **Upload** `debug-api.php` to your server
2. **Visit:** `https://yourdomain.com/debug-api.php`
3. **Check for:**
   - ✅ Database connection successful
   - ✅ Users table exists
   - ✅ Admin users: 1

### 5.2 Test Admin Login

1. **Go to:** `https://yourdomain.com/admin/login`
2. **Use credentials:**
   - Email: `<EMAIL>`
   - Password: `Admin@2024!`
3. **Should redirect** to admin dashboard

## 🔒 Step 6: Security

### 6.1 Change Default Password

1. **Login as admin**
2. **Go to profile/settings**
3. **Change password** to something more secure

### 6.2 Clean Up

```bash
# Delete these files after setup:
rm debug-api.php
rm reset-admin-production.php
rm DATABASE_SETUP_GUIDE.md
```

## 🚨 Troubleshooting

### Database Connection Failed

**Check:**
1. Database credentials in `database.php`
2. Database exists and user has permissions
3. Hosting firewall settings

### Admin Login Failed

**Solutions:**
1. Run `update-admin-credentials.sql` again
2. Check if users table exists
3. Verify password hash is correct

### Tables Don't Exist

**Solutions:**
1. Re-import `database.sql`
2. Check for SQL errors in import
3. Verify database permissions

## 📞 Common Hosting Configurations

### cPanel Hosting
```php
private $host = 'localhost';
private $db_name = 'username_housing';     // Replace 'username' with your cPanel username
private $username = 'username_housing';    // Same as database name usually
private $password = 'your_password';       // Password you set
```

### Plesk Hosting
```php
private $host = 'localhost';
private $db_name = 'housing_okayy_db';     // Custom name you chose
private $username = 'housing_user';        // Custom username you chose  
private $password = 'your_password';       // Password you set
```

### Shared Hosting (Generic)
```php
private $host = 'localhost';               // Sometimes 'mysql.yourdomain.com'
private $db_name = 'provided_db_name';     // Check hosting control panel
private $username = 'provided_username';   // Check hosting control panel
private $password = 'your_password';       // Password you set
```

## ✨ Final Verification

After completing all steps:

1. ✅ Database connection works
2. ✅ All tables created successfully  
3. ✅ Admin user can login
4. ✅ Property posting works
5. ✅ No client-side errors

Your housing.okayy.in application should now be fully functional!
