"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[527],{4527:(e,t,r)=>{r.d(t,{SearchBar:()=>l});var a=r(5155),o=r(2115),n=r(5695);let s=["Hyderabad","Jubilee Hills, Hyderabad","Banjara Hills, Hyderabad","HITEC City, Hyderabad","Gachibowli, Hyderabad","Madhapur, Hyderabad","Kondapur, Hyderabad","Kukatpally, Hyderabad","Ameerpet, Hyderabad","Secunderabad","Begumpet, Hyderabad","Somajiguda, Hyderabad","Financial District, Hyderabad","Miyapur, Hyderabad","Uppal, Hyderabad","LB Nagar, Hyderabad","Mehdipatnam, Hyderabad","Charminar, Hyderabad","Abids, Hyderabad","Tank Bund, Hyderabad","Mumbai","Bandra West, Mumbai","Andheri, Mumbai","Powai, Mumbai","Worli, Mumbai","Lower Parel, Mumbai","Bangalore","Koramangala, Bangalore","Whitefield, Bangalore","Electronic City, Bangalore","Indiranagar, Bangalore","Jayanagar, Bangalore","Delhi","New Delhi","Connaught Place, Delhi","Karol Bagh, Delhi","Lajpat Nagar, Delhi","Dwarka, Delhi","Gurgaon","Sector 14, Gurgaon","DLF Phase 1, Gurgaon","Golf Course Road, Gurgaon","Noida","Sector 62, Noida","Sector 18, Noida","Greater Noida","Pune","Wakad, Pune","Hinjewadi, Pune","Kharadi, Pune","Aundh, Pune","Koregaon Park, Pune","Chennai","Anna Nagar, Chennai","T. Nagar, Chennai","Velachery, Chennai","OMR, Chennai","Adyar, Chennai"];function l(){let e=(0,n.useRouter)(),[t,r]=(0,o.useState)("buy"),[l,i]=(0,o.useState)(""),[d,c]=(0,o.useState)(""),[u,h]=(0,o.useState)(""),[m,p]=(0,o.useState)(""),[x,v]=(0,o.useState)(""),[g,w]=(0,o.useState)(""),[j,b]=(0,o.useState)(!1),[y,k]=(0,o.useState)([]),[f,C]=(0,o.useState)(-1),D=(0,o.useRef)(null),N=e=>{i(e),C(-1),e.length>0?(k(s.filter(t=>t.toLowerCase().includes(e.toLowerCase())).slice(0,8)),b(!0)):b(!1)},L=e=>{i(e),b(!1),k([]),setTimeout(()=>{var e;null==(e=D.current)||e.blur()},100)};(0,o.useEffect)(()=>{let e=e=>{var t;let r=e.target,a=D.current,o=null==a||null==(t=a.parentElement)?void 0:t.querySelector(".suggestions-dropdown");a&&(a.contains(r)||(null==o?void 0:o.contains(r)))||b(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let M=()=>{if("/properties"===window.location.pathname){let e=document.querySelector("section[data-results]");if(e){let t=e.getBoundingClientRect().top+window.pageYOffset-80;window.scrollTo({top:t,behavior:"smooth"})}}};return(0,a.jsxs)("div",{className:"bg-white/95 backdrop-blur-md rounded-2xl shadow-large border border-white/20 p-8 max-w-6xl mx-auto animate-scale-in",children:[(0,a.jsxs)("div",{className:"flex space-x-2 mb-8 bg-gray-100 p-2 rounded-xl",children:[(0,a.jsxs)("button",{className:"flex-1 px-6 py-3 rounded-lg font-semibold transition-all duration-300 ".concat("buy"===t?"bg-primary-600 text-white shadow-soft transform scale-105":"text-text-secondary hover:text-text-primary hover:bg-white/50"),onClick:()=>r("buy"),children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 inline mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"})}),"Buy"]}),(0,a.jsx)("button",{className:"flex-1 px-6 py-3 rounded-lg font-semibold transition-all duration-300 ".concat("rent"===t?"bg-primary-600 text-white shadow-soft transform scale-105":"text-text-secondary hover:text-text-primary hover:bg-white/50"),onClick:()=>r("rent"),children:"Rent"}),(0,a.jsxs)("button",{className:"flex-1 px-6 py-3 rounded-lg font-semibold transition-all duration-300 ".concat("pg"===t?"bg-primary-600 text-white shadow-soft transform scale-105":"text-text-secondary hover:text-text-primary hover:bg-white/50"),onClick:()=>r("pg"),children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 inline mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})}),"PG"]}),(0,a.jsxs)("button",{className:"flex-1 px-6 py-3 rounded-lg font-semibold transition-all duration-300 ".concat("sell"===t?"bg-primary-600 text-white shadow-soft transform scale-105":"text-text-secondary hover:text-text-primary hover:bg-white/50"),onClick:()=>r("sell"),children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 inline mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Sell"]})]}),(0,a.jsxs)("form",{onSubmit:r=>{if(r.preventDefault(),b(!1),"pg"===t){let t=new URLSearchParams;l&&t.set("city",l),u&&t.set("priceRange",u),x&&t.set("roomType",x),g&&t.set("gender",g);let r=t.toString();e.push("/pg".concat(r?"?".concat(r):""));return}let a=new URLSearchParams;if(l&&a.set("city",l),d&&a.set("type",d.toUpperCase()),m&&a.set("bedrooms",m),u&&""!==u){if(u.includes("-")){let[e,t]=u.split("-");e&&"0"!==e&&a.set("minPrice",e),t&&!u.endsWith("+")&&a.set("maxPrice",t)}else if(u.endsWith("+")){let e=u.replace("+","");a.set("minPrice",e)}}let o=a.toString(),n="/properties".concat(o?"?".concat(o):"");"/properties"===window.location.pathname?(window.history.pushState({},"",n),window.dispatchEvent(new CustomEvent("searchUpdate",{detail:{url:n}})),setTimeout(M,100)):e.push(n)},children:[(0,a.jsx)("div",{className:"grid grid-cols-1 gap-6 mb-6",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{htmlFor:"location",className:"block text-sm font-semibold text-text-primary",children:[(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 inline mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),"Location"]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{ref:D,type:"text",id:"location",placeholder:"Enter city, area (e.g., Hyderabad, Jubilee Hills)",className:"input-field pl-12",value:l,onChange:e=>N(e.target.value),onFocus:()=>l.length>0&&b(!0),onKeyDown:e=>{if(j&&0!==y.length)switch(e.key){case"ArrowDown":e.preventDefault(),C(e=>e<y.length-1?e+1:0);break;case"ArrowUp":e.preventDefault(),C(e=>e>0?e-1:y.length-1);break;case"Enter":e.preventDefault(),f>=0&&L(y[f]);break;case"Escape":b(!1),C(-1)}},autoComplete:"off"}),(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 absolute left-4 top-1/2 transform -translate-y-1/2 text-text-tertiary",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),j&&y.length>0&&(0,a.jsx)("div",{className:"suggestions-dropdown absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-xl z-[9999] mt-1 max-h-60 overflow-y-auto",children:y.map((e,t)=>(0,a.jsx)("button",{type:"button",className:"w-full text-left px-4 py-3 border-b border-gray-100 last:border-b-0 transition-colors duration-200 cursor-pointer ".concat(f===t?"bg-blue-100 text-blue-800":"hover:bg-blue-50 hover:text-blue-700"),onClick:t=>{t.preventDefault(),t.stopPropagation(),L(e)},onMouseDown:t=>{t.preventDefault(),L(e)},onMouseEnter:()=>C(t),children:(0,a.jsxs)("div",{className:"flex items-center pointer-events-none",children:[(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-3 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e})]})},t))})]})]})}),"pg"===t?(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{htmlFor:"pgRoomType",className:"block text-sm font-semibold text-text-primary",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 inline mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"})}),"Room Type"]}),(0,a.jsxs)("select",{id:"pgRoomType",className:"input-field",value:x,onChange:e=>v(e.target.value),children:[(0,a.jsx)("option",{value:"",children:"Any Room Type"}),(0,a.jsx)("option",{value:"single",children:"\uD83D\uDECF️ Single Room"}),(0,a.jsx)("option",{value:"double",children:"\uD83D\uDECF️\uD83D\uDECF️ Double Sharing"}),(0,a.jsx)("option",{value:"triple",children:"\uD83D\uDECF️\uD83D\uDECF️\uD83D\uDECF️ Triple Sharing"}),(0,a.jsx)("option",{value:"dormitory",children:"\uD83C\uDFE0 Dormitory"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{htmlFor:"pgPriceRange",className:"block text-sm font-semibold text-text-primary",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 inline mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Monthly Rent"]}),(0,a.jsxs)("select",{id:"pgPriceRange",className:"input-field",value:u,onChange:e=>h(e.target.value),children:[(0,a.jsx)("option",{value:"",children:"Any Budget"}),(0,a.jsx)("option",{value:"0-5000",children:"₹0 - ₹5,000"}),(0,a.jsx)("option",{value:"5000-10000",children:"₹5,000 - ₹10,000"}),(0,a.jsx)("option",{value:"10000-15000",children:"₹10,000 - ₹15,000"}),(0,a.jsx)("option",{value:"15000-20000",children:"₹15,000 - ₹20,000"}),(0,a.jsx)("option",{value:"20000-25000",children:"₹20,000 - ₹25,000"}),(0,a.jsx)("option",{value:"25000+",children:"₹25,000+"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{htmlFor:"pgGender",className:"block text-sm font-semibold text-text-primary",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 inline mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),"Gender Preference"]}),(0,a.jsxs)("select",{id:"pgGender",className:"input-field",value:g,onChange:e=>w(e.target.value),children:[(0,a.jsx)("option",{value:"",children:"Any Gender"}),(0,a.jsx)("option",{value:"male",children:"\uD83D\uDC68 Boys Only"}),(0,a.jsx)("option",{value:"female",children:"\uD83D\uDC69 Girls Only"}),(0,a.jsx)("option",{value:"co-ed",children:"\uD83D\uDC6B Co-ed"})]})]})]}):(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{htmlFor:"propertyType",className:"block text-sm font-semibold text-text-primary",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 inline mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})}),"Property Type"]}),(0,a.jsxs)("select",{id:"propertyType",className:"input-field",value:d,onChange:e=>c(e.target.value),children:[(0,a.jsx)("option",{value:"",children:"Any Type"}),(0,a.jsx)("option",{value:"house",children:"\uD83C\uDFE0 House"}),(0,a.jsx)("option",{value:"apartment",children:"\uD83C\uDFE2 Apartment"}),(0,a.jsx)("option",{value:"villa",children:"\uD83C\uDFE1 Villa"}),(0,a.jsx)("option",{value:"plot",children:"\uD83D\uDCD0 Plot"}),(0,a.jsx)("option",{value:"commercial",children:"\uD83C\uDFEA Commercial"}),(0,a.jsx)("option",{value:"office",children:"\uD83C\uDFE2 Office"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{htmlFor:"priceRange",className:"block text-sm font-semibold text-text-primary",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 inline mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Price Range"]}),(0,a.jsxs)("select",{id:"priceRange",className:"input-field",value:u,onChange:e=>h(e.target.value),children:[(0,a.jsx)("option",{value:"",children:"Any Budget"}),"rent"===t?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("option",{value:"0-10000",children:"₹0 - ₹10,000"}),(0,a.jsx)("option",{value:"10000-25000",children:"₹10,000 - ₹25,000"}),(0,a.jsx)("option",{value:"25000-50000",children:"₹25,000 - ₹50,000"}),(0,a.jsx)("option",{value:"50000-100000",children:"₹50,000 - ₹1,00,000"}),(0,a.jsx)("option",{value:"100000+",children:"₹1,00,000+"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("option",{value:"0-1000000",children:"₹0 - ₹10 Lakh"}),(0,a.jsx)("option",{value:"1000000-2500000",children:"₹10 Lakh - ₹25 Lakh"}),(0,a.jsx)("option",{value:"2500000-5000000",children:"₹25 Lakh - ₹50 Lakh"}),(0,a.jsx)("option",{value:"5000000-10000000",children:"₹50 Lakh - ₹1 Crore"}),(0,a.jsx)("option",{value:"10000000-20000000",children:"₹1 Crore - ₹2 Crore"}),(0,a.jsx)("option",{value:"20000000+",children:"₹2 Crore+"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{htmlFor:"bedrooms",className:"block text-sm font-semibold text-text-primary",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 inline mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"})}),"Bedrooms"]}),(0,a.jsxs)("select",{id:"bedrooms",className:"input-field",value:m,onChange:e=>p(e.target.value),children:[(0,a.jsx)("option",{value:"",children:"Any Size"}),(0,a.jsx)("option",{value:"1",children:"1+ Bedroom"}),(0,a.jsx)("option",{value:"2",children:"2+ Bedrooms"}),(0,a.jsx)("option",{value:"3",children:"3+ Bedrooms"}),(0,a.jsx)("option",{value:"4",children:"4+ Bedrooms"}),(0,a.jsx)("option",{value:"5",children:"5+ Bedrooms"})]})]})]}),(0,a.jsx)("div",{className:"mt-8 flex justify-center",children:(0,a.jsxs)("button",{type:"submit",className:"btn-primary px-12 py-4 text-lg shadow-colored-lg",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),"Search Properties"]})})]})]})}},5695:(e,t,r)=>{var a=r(8999);r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})}}]);