(()=>{var e={};e.id=957,e.ids=[957],e.modules={216:(e,t,s)=>{"use strict";s.d(t,{Eo:()=>c,Er:()=>d,M5:()=>l,R2:()=>n,hh:()=>o});let r="/php-backend/api",a={LOGIN:`${r}/auth/login.php`,SIGNUP:`${r}/auth/signup.php`,LOGOUT:`${r}/auth/logout.php`,CHECK_SESSION:`${r}/auth/check-session.php`,PROPERTIES:`${r}/properties/index.php`,PROPERTY_BY_ID:e=>`${r}/properties/get.php?id=${e}`,UPLOAD:`${r}/upload/index.php`,CONTACT:`${r}/contact/index.php`,BLOG_POSTS:`${r}/blog/index.php`,BLOG_POST_BY_SLUG:e=>`${r}/blog/get.php?slug=${e}`,USER_PROPERTIES:`${r}/user/properties.php`,USER_INQUIRIES:`${r}/user/inquiries.php`,ADMIN_PROPERTIES:`${r}/admin/properties.php`,APPROVE_PROPERTY:e=>`${r}/admin/approve.php?id=${e}`,REJECT_PROPERTY:e=>`${r}/admin/reject.php?id=${e}`},i=async(e,t={})=>{let s={headers:{"Content-Type":"application/json"},credentials:"include"},r={...s,...t,headers:{...s.headers,...t.headers}};try{let t;console.log("API Request:",{url:e,options:r});let s=await fetch(e,r),a=s.headers.get("content-type");if(a&&a.includes("application/json"))t=await s.json();else{let e=await s.text();throw console.error("Non-JSON response:",e),Error(`Server returned non-JSON response: ${e.substring(0,200)}`)}if(console.log("API Response:",{status:s.status,data:t}),!s.ok)throw Error(t.error||`HTTP error! status: ${s.status}`);return t}catch(t){throw console.error("API request failed:",{url:e,error:t}),t}},n={login:async(e,t)=>i(a.LOGIN,{method:"POST",body:JSON.stringify({email:e,password:t})}),signup:async(e,t,s,r)=>i(a.SIGNUP,{method:"POST",body:JSON.stringify({name:e,email:t,password:s,phone:r})}),logout:async()=>i(a.LOGOUT,{method:"POST"}),checkSession:async()=>i(a.CHECK_SESSION)},l={getProperties:async(e={})=>{let t=new URLSearchParams;return Object.entries(e).forEach(([e,s])=>{null!=s&&""!==s&&t.append(e,s.toString())}),i(`${a.PROPERTIES}?${t.toString()}`)},createProperty:async e=>i(a.PROPERTIES,{method:"POST",body:JSON.stringify(e)}),getPropertyById:async e=>i(a.PROPERTY_BY_ID(e))},d={getProperties:async e=>i(e?`${a.ADMIN_PROPERTIES}?status=${e}`:a.ADMIN_PROPERTIES),approveProperty:async e=>i(a.APPROVE_PROPERTY(e),{method:"PUT"}),rejectProperty:async(e,t)=>i(a.REJECT_PROPERTY(e),{method:"PUT",body:JSON.stringify({reason:t})})},o={getPosts:async(e={})=>{let t=new URLSearchParams;return Object.entries(e).forEach(([e,s])=>{null!=s&&""!==s&&t.append(e,s.toString())}),i(`${a.BLOG_POSTS}?${t.toString()}`)},getPostBySlug:async e=>i(a.BLOG_POST_BY_SLUG(e))},c={getProperties:async()=>i(a.USER_PROPERTIES),getInquiries:async()=>i(a.USER_INQUIRIES)}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1031:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Music\\\\Real Estate\\\\Real Estate\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\dashboard\\page.tsx","default")},1125:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=s(5239),a=s(8088),i=s(8170),n=s.n(i),l=s(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o={children:["",{children:["admin",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1031)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\dashboard\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\dashboard\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/dashboard/page",pathname:"/admin/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},1135:()=>{},1388:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3271:(e,t,s)=>{Promise.resolve().then(s.bind(s,1031))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l,metadata:()=>n});var r=s(7413),a=s(5091),i=s.n(a);s(1135);let n={title:{default:"Real Estate India - Buy, Sell, and Rent Properties",template:"%s | Real Estate India"},description:"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities, or list your property with us. Expert guidance for all your property needs.",keywords:["real estate India","property for sale","property for rent","buy property","sell property","apartments","houses","villas","commercial property"],authors:[{name:"Real Estate India"}],creator:"Real Estate India",publisher:"Real Estate India",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("https://realestate-india.com"),alternates:{canonical:"/"},openGraph:{type:"website",locale:"en_IN",url:"https://realestate-india.com",title:"Real Estate India - Buy, Sell, and Rent Properties",description:"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities.",siteName:"Real Estate India"},twitter:{card:"summary_large_image",title:"Real Estate India - Buy, Sell, and Rent Properties",description:"Find your dream home in India with our comprehensive real estate platform.",creator:"@realestateindia"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function l({children:e}){return(0,r.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,r.jsx)("body",{className:`${i().variable} font-sans bg-background text-text-primary antialiased`,children:e})})}},4940:()=>{},4943:(e,t,s)=>{Promise.resolve().then(s.bind(s,9573))},5171:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},6189:(e,t,s)=>{"use strict";var r=s(5773);s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9243:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9573:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(687),a=s(3210),i=s(6189),n=s(216);function l(){let[e,t]=(0,a.useState)(null),[s,l]=(0,a.useState)([]),[d,o]=(0,a.useState)({total:0,pending:0,approved:0,rejected:0}),[c,p]=(0,a.useState)(!0),[m,h]=(0,a.useState)("overview"),x=(0,i.useRouter)(),u=async()=>{try{let e=await n.Er.getProperties("all");l(e.properties||[]);let t=e.properties?.length||0,s=e.properties?.filter(e=>"PENDING"===e.approval_status).length||0,r=e.properties?.filter(e=>"APPROVED"===e.approval_status).length||0,a=e.properties?.filter(e=>"REJECTED"===e.approval_status).length||0;o({total:t,pending:s,approved:r,rejected:a})}catch(e){console.error("Failed to load dashboard data:",e)}finally{p(!1)}},g=async e=>{try{await n.Er.approveProperty(e),await u(),alert("Property approved successfully!")}catch(e){alert(e.message||"Failed to approve property")}},v=async e=>{let t=prompt("Enter rejection reason:");if(t)try{await n.Er.rejectProperty(e,t),await u(),alert("Property rejected successfully!")}catch(e){alert(e.message||"Failed to reject property")}},y=async()=>{try{await n.R2.logout(),x.push("/login")}catch(e){console.error("Logout failed:",e)}};if(c)return(0,r.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading admin dashboard..."})]})});let P=s.filter(e=>"PENDING"===e.approval_status);return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,r.jsx)("header",{className:"bg-white shadow",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Admin Dashboard"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Welcome back, ",e?.name]})]}),(0,r.jsx)("button",{onClick:y,className:"bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700",children:"Logout"})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"border-b border-gray-200 mb-8",children:(0,r.jsx)("nav",{className:"-mb-px flex space-x-8",children:[{id:"overview",name:"Overview"},{id:"pending",name:"Pending Approvals"},{id:"all",name:"All Properties"}].map(e=>(0,r.jsx)("button",{onClick:()=>h(e.id),className:`py-2 px-1 border-b-2 font-medium text-sm ${m===e.id?"border-red-500 text-red-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:e.name},e.id))})}),"overview"===m&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold",children:"T"})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Properties"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:d.total})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold",children:"P"})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Pending"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:d.pending})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold",children:"A"})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Approved"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:d.approved})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold",children:"R"})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Rejected"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:d.rejected})]})})]})})})]}),"pending"===m&&(0,r.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:[(0,r.jsx)("div",{className:"px-4 py-5 sm:px-6",children:(0,r.jsxs)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:["Pending Property Approvals (",P.length,")"]})}),(0,r.jsxs)("ul",{className:"divide-y divide-gray-200",children:[P.map(e=>(0,r.jsx)("li",{className:"px-4 py-4 sm:px-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["₹",e.price.toLocaleString()," • ",e.type," • Owner: ",e.owner.name]}),(0,r.jsxs)("p",{className:"text-xs text-gray-400",children:["Submitted: ",new Date(e.created_at).toLocaleDateString()]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>g(e.id),className:"bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700",children:"Approve"}),(0,r.jsx)("button",{onClick:()=>v(e.id),className:"bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700",children:"Reject"})]})]})},e.id)),0===P.length&&(0,r.jsx)("li",{className:"px-4 py-8 text-center text-gray-500",children:"No pending properties to review"})]})]}),"all"===m&&(0,r.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:[(0,r.jsx)("div",{className:"px-4 py-5 sm:px-6",children:(0,r.jsxs)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:["All Properties (",s.length,")"]})}),(0,r.jsx)("ul",{className:"divide-y divide-gray-200",children:s.map(e=>(0,r.jsx)("li",{className:"px-4 py-4 sm:px-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["₹",e.price.toLocaleString()," • ",e.type," • Owner: ",e.owner.name]}),(0,r.jsxs)("p",{className:"text-xs text-gray-400",children:["Created: ",new Date(e.created_at).toLocaleDateString()]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${"APPROVED"===e.approval_status?"bg-green-100 text-green-800":"PENDING"===e.approval_status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:e.approval_status}),"PENDING"===e.approval_status&&(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("button",{onClick:()=>g(e.id),className:"bg-green-600 text-white px-2 py-1 rounded text-xs hover:bg-green-700",children:"Approve"}),(0,r.jsx)("button",{onClick:()=>v(e.id),className:"bg-red-600 text-white px-2 py-1 rounded text-xs hover:bg-red-700",children:"Reject"})]})]})]})},e.id))})]})]})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[771],()=>s(1125));module.exports=r})();