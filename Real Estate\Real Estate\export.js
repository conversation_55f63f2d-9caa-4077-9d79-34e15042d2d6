const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Starting static export build...');

try {
  // Clean previous builds
  if (fs.existsSync('.next')) {
    console.log('Cleaning previous build...');
    fs.rmSync('.next', { recursive: true, force: true });
  }
  
  if (fs.existsSync('out')) {
    console.log('Cleaning previous export...');
    fs.rmSync('out', { recursive: true, force: true });
  }

  // Run the build
  console.log('Building application...');
  execSync('npm run build', { stdio: 'inherit' });
  
  console.log('Static export completed successfully!');
  console.log('Files are available in the "out" directory');
  
} catch (error) {
  console.error('Build failed:', error.message);
  process.exit(1);
}
